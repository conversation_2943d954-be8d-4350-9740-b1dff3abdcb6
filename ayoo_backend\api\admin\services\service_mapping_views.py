from fastapi import APIRouter, HTTPException, UploadFile
from ..payloads.service import CreateServiceMapping, UpdateServiceMapping, CreateServiceMappingObj, UpdateServiceMappingObj
from ...DAOs.serviceDAO import ServiceMappingDAO, ServiceMappingClinicFilter
from ...DataModels.services import ServiceMapping, ServiceTypes, ServiceModalities
from ...DTOs.servicesDTO import ServiceMappingDTO
from typing import List
from datetime import datetime
import io
import openpyxl

mapping_router = APIRouter(prefix='/mapping', tags=['Services'])


def get_dto(mapping: ServiceMapping):
    modality = ServiceModalities[mapping.service.modality].value
    service_type = ServiceTypes[mapping.service.type].value

    return ServiceMappingDTO(
        maping_id=mapping.mapping_id,
        clinic_id=mapping.clinic_id,
        created_at=mapping.created_at,
        discount_price=mapping.discount_price,
        hc_available=mapping.hc_available,
        hc_price=mapping.hc_price,
        original_price=mapping.original_price,
        service_id=mapping.service_id,
        is_active=mapping.is_active,
        modality=modality,
        type=service_type,
        service_name=mapping.service.name,
        vendor_s_id=mapping.vendor_s_id
    )


@mapping_router.post("")
async def create_mapping(data: CreateServiceMapping) -> str:
    if (data.clinic_id == ""):
        raise HTTPException(422, "clinic id cannot be empty")

    if (data.service_id == ""):
        raise HTTPException(422, "service id cannot be empty")

    # TODO: check if already mapped
    obj = CreateServiceMappingObj(
        clinic_id=data.clinic_id,
        service_id=data.service_id,
        vendor_s_id=data.vendor_s_id,
        original_price=data.original_price,
        discount_price=data.discount_price,
        hc_available=data.hc_available,
        hc_price=data.hc_price,
        is_active=True,
        created_at=datetime.now()
    )

    mapping: ServiceMapping = ServiceMappingDAO().create(obj.dict())
    return mapping.mapping_id


@mapping_router.put("/{mapping_id}")
async def update_service_mapping(mapping_id: str, data: UpdateServiceMapping) -> str:
    mapping: ServiceMapping = ServiceMappingDAO().get(mapping_id)

    obj = UpdateServiceMappingObj(
        mapping_id=mapping_id,
        clinic_id=mapping.clinic_id,
        service_id=mapping.service_id,
        vendor_s_id=data.vendor_s_id,
        original_price=data.original_price,
        discount_price=data.discount_price,
        hc_available=data.hc_available,
        hc_price=data.hc_price,
        is_active=True,
        created_at=datetime.now()
    )

    mapping: ServiceMapping = ServiceMappingDAO().update(mapping_id, obj.dict())
    return mapping.mapping_id

# @mapping_router.patch("/{mapping_id}")
# async def update_mapping_status(mapping_id: str):


@mapping_router.get("/list", response_model=List[ServiceMappingDTO])
async def get_all_service_mappings():
    mappings: List[ServiceMapping] = ServiceMappingDAO().list()
    return [get_dto(mapping) for mapping in mappings]


@mapping_router.get("/upload_data/{clinic_id}", include_in_schema=False)
async def upload_sheet(clinic_id: str, file: UploadFile) -> bool:
    if not file.filename.endswith('.xlsx'):
        raise HTTPException(422, "File not a excel sheet")
    f = await file.read()
    xlsx = io.BytesIO(f)
    wb = openpyxl.load_workbook(xlsx)
    sheet_name = 'MasterData'
    ws = None
    try:
        ws = wb[sheet_name]
    except:
        raise HTTPException(422, f"Sheet Name should be {sheet_name}")

    mappings = []
    i = 0
    for cells in ws.iter_rows():
        i += 1
        # TODO: REMOVE LATER
        # print([cell.value for cell in cells])
        if (i == 1):
            continue

        service_id = cells[0].value
        original_price = cells[1].value
        discount_price = cells[2].value
        hc_available = cells[3].value
        hc_price = cells[4].value
        mapping = CreateServiceMappingObj(
            created_at=datetime.now(),
            clinic_id=clinic_id,
            service_id=service_id,
            original_price=original_price,
            discount_price=discount_price,
            hc_available=hc_available,
            hc_price=hc_price,
            is_active=True
        )
        mappings.append(mapping)

    ServiceMappingDAO().bulk_delete(clinic_id)
    ServiceMappingDAO().bulk_create(mappings)
    return True


@mapping_router.get("/{mapping_id}", response_model=ServiceMappingDTO)
async def get_service_mapping(mapping_id: str):
    mapping: ServiceMapping = ServiceMappingDAO().get(mapping_id)
    return get_dto(mapping)


@mapping_router.get("/clinic/{clinic_id}", response_model=List[ServiceMappingDTO])
async def get_service_mapped_to_clinic(clinic_id: str):
    query = ServiceMappingClinicFilter(clinic_id=clinic_id)
    mappings: List[ServiceMapping] = ServiceMappingDAO().list(filters=query)
    return [get_dto(mapping) for mapping in mappings]


@mapping_router.delete("/{mapping_id}")
async def delete_mapping(mapping_id: str):
    return ServiceMappingDAO().delete(mapping_id)
