import google.auth
from google.oauth2 import service_account
from google.auth.transport.requests import Request
import requests
import os

from ayoo_backend.api.api_configs import FIREBASE_SERVER_TOKEN


def get_access_token():
    # Path to your service account JSON file
    current_dir = os.path.dirname(__file__)
    service_account_file = os.path.join(current_dir, 'fir-console-75c16-44ad1d2554d5.json')

    # service_account_file = 'fir-console-75c16-44ad1d2554d5.json'

    # Load credentials from the service account file
    credentials = service_account.Credentials.from_service_account_file(
        service_account_file,
        scopes=['https://www.googleapis.com/auth/firebase.messaging'])

    # Obtain an access token
    request = Request()
    credentials.refresh(request)
    access_token = credentials.token
    return access_token

def send_fcm_message_to_firebase(data, headers):
    url = 'https://fcm.googleapis.com/v1/projects/fir-console-75c16/messages:send'
    print('headers: ',headers)
    print('\n\ndata: ', data)
    response = requests.post(url, headers=headers, json=data)
    response.raise_for_status()
    return response.json()

def send_fcm_message_to_firebase_multi(data, headers):
    url = 'https://fcm.googleapis.com/v1/projects/fir-console-75c16/messages:send'
    print('headers: ',headers)
    print('\n\ndata: ', data)
    response = requests.post(url, headers=headers, json=data)
    # response.raise_for_status()
    return response.json()


def subscribe_tokens_to_topic(tokens, topic="all_users"):
    url = "https://iid.googleapis.com/iid/v1:batchAdd"
    headers = {
        "Authorization": f"Bearer {get_access_token()}",
        "Content-Type": "application/json"
    }
    data = {
        "to": f"/topics/{topic}",
        "registration_tokens": tokens
    }
    response = requests.post(url, headers=headers, json=data)
    return response.json()

def send_fcm_message_to_topic(topic, title, body):
    url = "https://fcm.googleapis.com/v1/projects/fir-console-75c16/messages:send"
    headers = {
        "Authorization": f"Bearer {get_access_token()}",
        "Content-Type": "application/json"
    }
    data = {
        "message": {
            "topic": topic,
            "notification": {
                "title": title,
                "body": body
            }
        }
    }
    response = requests.post(url, headers=headers, json=data)
    print("FCM Response:", response.status_code, response.text)
    return response.json()
