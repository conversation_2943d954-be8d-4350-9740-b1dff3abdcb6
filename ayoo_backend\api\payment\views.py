from fastapi import APIRouter, Depends, HTTPException
from uuid import uuid4
from .payment_controller import get_normal_payment_url, add_zero_payment_entry, initiate_payment, check_adhoc_payment_status
from ..revenue_models import Paymentview, AdhocPaymentStatusRequest, AdhocPaymentStatusResponse
# from ..revenue_controller

payment_router = APIRouter(prefix="/payment")


@payment_router.post("/request")  # , response_class=HTMLResponse)
async def generate_payment_request(paymentview: Paymentview):
    if not paymentview.appointment_id:
        detail = "blank appointment id provided"
        raise HTTPException(409, detail)

    secret_key = str(uuid4())
    order_id = str(uuid4())

    if int(paymentview.amount) == 0:
        res, msg = await add_zero_payment_entry(
            paymentview=paymentview,
            order_id=order_id,
            secret_key=secret_key
        )

        url = {
            "details": msg,
            "order_id": order_id,
            "user_id": paymentview.user_id,
            "Bypass": False,
        }

        if not res:
            return url

        url["Bypass"] = True
        return (url)

    """
    p_billing_name = form_data['billing_name']
    p_billing_address = form_data['billing_address']
    p_billing_city = form_data['billing_city']
    p_billing_state = form_data['billing_state']
    p_billing_zip = form_data['billing_zip']
    p_billing_country = form_data['billing_country']
    p_billing_tel = form_data['billing_tel']
    p_billing_email = form_data['billing_email']
    p_delivery_name = form_data['delivery_name']
    p_delivery_address = form_data['delivery_address']
    p_delivery_city = form_data['delivery_city']
    p_delivery_state = form_data['delivery_state']
    p_delivery_zip = form_data['delivery_zip']
    p_delivery_country = form_data['delivery_country']
    p_delivery_tel = form_data['delivery_tel']
    p_merchant_param1 = form_data['merchant_param1']
    p_merchant_param2 = form_data['merchant_param2']
    p_merchant_param3 = form_data['merchant_param3']
    p_merchant_param4 = form_data['merchant_param4']
    p_merchant_param5 = form_data['merchant_param5']"""
    # p_promo_code = form_data['promo_code']
    # p_customer_identifier = form_data['customer_identifier']

    res, msg = initiate_payment(paymentview, order_id, secret_key)

    if not res:
        return {
            "details": msg,
            "order_id": order_id,
            "user_id": paymentview.user_id
        }

    url = await get_normal_payment_url(
        paymentview.amount,
        secret_key,
        order_id,
        paymentview.user_id
    )

    return {
        "showUrl": url,
        "order_id": order_id,
        "user_id": paymentview.user_id
    }


@payment_router.post("/check_adhoc_status", response_model=AdhocPaymentStatusResponse, tags=["payment"])
async def check_adhoc_payment_status_endpoint(request: AdhocPaymentStatusRequest):
    """
    Check the status of an adhoc payment using CCAvenue's orderStatusTracker API.

    This endpoint:
    1. Queries the paymentsgateway3 collection for adhoc payments
    2. Uses CCAvenue's orderStatusTracker API with AES encryption
    3. Updates the payment status in the database
    4. Returns the current payment status

    Args:
        request: Contains the order_id to check

    Returns:
        AdhocPaymentStatusResponse: Payment status details including whether it was updated from gateway
    """
    return await check_adhoc_payment_status(request)
