import uuid
import datetime

from fastapi import <PERSON>TT<PERSON><PERSON>x<PERSON>
from jose import jwt
from .services.zoom_meet_service import ZoomSessionBuilder
from sqlalchemy.orm import scoped_session
from .DataModels.jitsi import PatientDetails, DoctorDetails

from . import dbmodels
from .dbmodels import <PERSON><PERSON><PERSON>, DBGuest, DBRelatives, DBDoctor
from .patient_models import JitsiMeetingInfo
from .views import logger
from .api_configs import APPLICATION_ID, JITSI_SECRET_KEY, JITSI_ALGORITHM, MEET_URL, ZOOM_ACC_ID, ZOOM_CLIENT_ID, ZOOM_SECRET
from .DAOs.appointmentDAO import AppointmentDAO, GetAppointmentById
from .ayoo_utils import calcualte_appointment_duration

APPLICATION_ID = APPLICATION_ID
JITSI_SECRET_KEY = JITSI_SECRET_KEY
JITSI_ALGORITHM = JITSI_ALGORITHM


class JitsiMeetController:

    def __init__(self, db: scoped_session, mongo=None):
        self.db = db
        self.mongo = mongo
        self.mongo_db = self.mongo['ayoo']

    def create_jitsi_login_token(self, payload: dict):
        encoded_jitsi_jwt = jwt.encode(payload, JITSI_SECRET_KEY, algorithm=JITSI_ALGORITHM)
        return encoded_jitsi_jwt

    def get_patient_details(self, patientid: str):

        resp_user: DBUser = self.db.query(dbmodels.DBUser).filter_by(
            userid=patientid, is_deleted=False).one_or_none()
        if resp_user:
            return resp_user

        resp_guest: DBGuest = self.db.query(dbmodels.DBGuest).filter(
            DBGuest.guestid == patientid).one_or_none()
        if resp_guest:
            return resp_guest

        resp_relative: DBRelatives = self.db.query(dbmodels.DBRelatives).filter(
            DBRelatives.relativeid == patientid).one_or_none()
        if resp_relative:
            return resp_relative
        else:
            return None

    def get_doctor_details(self, doctor_id: str):
        try:
            doctor_details: DBDoctor = self.db.query(dbmodels.DBDoctor).filter_by(
                doctorid=doctor_id).one_or_none()
            if doctor_details is None:
                return None
            return doctor_details
        except Exception as e:
            raise Exception('Error while getting doctor details')

    def create_doctor_jitsi_meet_token(self, doctor_id: str, meeting_id: str, appointment_slot: datetime.datetime, nbf: int,
                                       exp: int):
        try:
            doctor_details = self.get_doctor_details(doctor_id=doctor_id)
            current_time = datetime.datetime.utcnow()
            iat_timestamp = int(current_time.timestamp())

            doctor_token_payload = {
                "aud": APPLICATION_ID,
                "iss": APPLICATION_ID,
                "sub": MEET_URL,
                "iat": iat_timestamp,
                "nbf": nbf,
                "exp": exp,
                "room": meeting_id,
                "context": {
                    "user": {
                        "name": doctor_details.firstname,
                        "email": doctor_details.email,
                        "affiliation": "owner"
                    }
                }
            }
            created_token = self.create_jitsi_login_token(payload=doctor_token_payload)
            return created_token
        except Exception as e:
            raise Exception(f'Error while getting doctor details: {str(e)}')

    def create_patient_jitsi_meet_token(self, patient_id: str, meeting_id: str, appointment_slot: datetime.datetime, nbf: int,
                                        exp: int):
        try:
            patient_details = self.get_patient_details(patientid=patient_id)
            current_time = datetime.datetime.utcnow()
            iat_timestamp = int(current_time.timestamp())

            patient_token_payload = {
                "aud": APPLICATION_ID,
                "iss": APPLICATION_ID,
                "sub": MEET_URL,
                "iat": iat_timestamp,
                "nbf": nbf,
                "exp": exp,
                "room": meeting_id,
                "context": {
                    "user": {
                        "name": patient_details.firstname,
                        "email": patient_details.email,
                        "affiliation": "member"
                    }
                }
            }
            created_token = self.create_jitsi_login_token(payload=patient_token_payload)
            return created_token
        except Exception as e:
            raise Exception(f'Error while getting patient details: {str(e)}')

    def check_meeting_exist(self, meeting_code: str, doctor_meeting_code: str):
        try:
            meeting_exist = self.mongo_db['JitsiMeetInfo'].find_one(
                {"$or": [{"meeting_code": meeting_code}, {"doctor_meeting_code": doctor_meeting_code}], "appointment_slot": {"$gte": datetime.datetime.now() - datetime.timedelta(days = 1)}}
                    
            )
            return meeting_exist
        except Exception as e:
            #logger.info(f'Error occurred as : {str(e)} while checking the meeting code')
            return None

    def check_meeting_using_appointment_id(self, appointment_id: str):
        try:
            meeting_exist = self.mongo_db['JitsiMeetInfo'].find_one({"appointment_id": appointment_id})
            return meeting_exist
        except Exception as e:
            #logger.info(f'Error occurred as : {str(e)} while checking the ')
            return None

    def create_meeting_link(self, appointment_id: str, case_id: str, doctorid: str, patientid: str,
                            appointment_slot: datetime.datetime):
        try:
            meeting_id = None
            meeting_code = None
            doctor_meeting_code = None
            for rl in range(10):
                meeting_id = str(uuid.uuid4())
                meeting_code = meeting_id[-4:]
                doctor_meeting_code = meeting_id.split('-')[1]
                if self.check_meeting_exist(meeting_code=meeting_code, doctor_meeting_code=doctor_meeting_code):
                    continue
                else:
                    break

            doctor_details = self.get_doctor_details(doctor_id=doctorid)
            if doctor_details is None:
                raise Exception('Something went wrong while getting doctor details for this appointment')

            consultation_duration = int(doctor_details.consulting_duration_virtual)

            appointment = AppointmentDAO().list(filter= GetAppointmentById(appointment_id = appointment_id))

            if len(appointment) == 0:
                raise Exception("Appointment not found while creating meeting link !!!!")
            appointment = appointment[0]
            slot_duration = (appointment.end_date - appointment.appointment_slot).total_seconds()/60

            start_time = appointment_slot - datetime.timedelta(minutes=15)
            expiration_time = appointment_slot + datetime.timedelta(minutes=consultation_duration) + datetime.timedelta(minutes=30)

            nbf = int(start_time.timestamp())
            exp = int(expiration_time.timestamp())

            doctor_token = self.create_doctor_jitsi_meet_token(doctor_id=doctorid, meeting_id=meeting_id,
                                                               appointment_slot=appointment_slot, nbf=nbf, exp=exp)

            patient_token = self.create_patient_jitsi_meet_token(patient_id=patientid, meeting_id=meeting_id,
                                                                 appointment_slot=appointment_slot, nbf=nbf, exp=exp)

            meeting_link = f'https://{MEET_URL}/{meeting_id}'

            doctor_meeting_link = f'https://{MEET_URL}/{meeting_id}?jwt={doctor_token}'
            patient_meeting_link = f'https://{MEET_URL}/{meeting_id}?jwt={patient_token}'

            patient_details = self.get_patient_details(patientid=patientid)

            doctor_email_id = doctor_details.email
            patient_email_id = patient_details.email
            #zoom_meeting_response = ZoomSessionBuilder().create_session(appointment_id)

            #doctor_meeting_link = zoom_meeting_response.get("start_url", "ZOOM_SESSION")
            #patient_meeting_link = zoom_meeting_response.get("join_url", "ZOOM_SESSION")

            jitsi_info = JitsiMeetingInfo(meeting_id=meeting_id,
                                          meeting_link=meeting_link,
                                          meeting_code=meeting_code,
                                          appointment_id=appointment_id,
                                          case_id=case_id,
                                          doctorid=doctorid,
                                          patientid=patientid,
                                          appointment_slot=appointment_slot,
                                          doctor_joining_link=doctor_meeting_link,
                                          patient_joining_link=patient_meeting_link,
                                          doctor_meeting_code=doctor_meeting_code
                                          )
            print(patient_details.__dict__)
            patient = PatientDetails(firstname = patient_details.firstname, lastname = patient_details.lastname, patient_id = patientid)
            doctor = DoctorDetails(firstname = doctor_details.firstname, lastname = doctor_details.lastname, doctor_id = doctor_details.doctorid)
            self.mongo_db['JitsiMeetInfo'].insert_one({**dict(jitsi_info), "doctor_details": doctor.dict(), "patient_details": patient.dict(), "slot_duration": slot_duration, "end_date": appointment.end_date})
            return meeting_link
        except Exception as e:
            import traceback
            print(traceback.print_exc())
            #logger.info(f'error occurred as {str(e)} while creating or inserting jitsi meeting link')
            raise Exception(str(e))

    def check_time_for_meeting_info(self, meeting_code: str):
        try:
            meeting_exist = self.check_meeting_exist(meeting_code=str(meeting_code),
                                                     doctor_meeting_code=str(meeting_code))
            if not meeting_exist:
                raise Exception(f'meeting not found for meeting_code: {str(meeting_code)}')
            else:
                doctor_details = self.get_doctor_details(doctor_id=meeting_exist['doctorid'])
                if doctor_details is None:
                    raise Exception('Something went wrong while getting doctor details for this appointment')

            role = 0
            role_dict = {
                0: "Patient",
                1: "Doctor"
            }
            if meeting_code != meeting_exist.get("meeting_code"):
                role = 1
            try:
                self.mongo_db["JitsiMeetInfo"].find_one_and_update({"meeting_code": meeting_exist.get("meeting_code")}, update = {"$set":{"patient_consent": True}}, upsert = False)
            except Exception as e:
                print(e)
                raise Exception("Patient Consent not updated!!!!!!")

            consultation_duration = doctor_details.consulting_duration_virtual
            meeting_datetime = meeting_exist['appointment_slot']
            end = meeting_exist.get("end_date")
            current_datetime = datetime.datetime.now()
            slot_duration = (end - meeting_datetime).total_seconds()/60
            fifteen_minutes_before_meeting_time = meeting_datetime - datetime.timedelta(minutes=5)
            thirty_minutes_after_meeting_time =   end + datetime.timedelta(minutes = calcualte_appointment_duration(slot_duration))

            if current_datetime > fifteen_minutes_before_meeting_time and current_datetime < thirty_minutes_after_meeting_time:

                if meeting_code == meeting_exist['meeting_code']:
                    return JitsiMeetingInfo(
                        meeting_id=meeting_exist['meeting_id'],
                        meeting_link=meeting_exist['patient_joining_link'],
                        meeting_code=meeting_exist['meeting_code'],
                        appointment_id=meeting_exist['appointment_id'],
                        case_id=meeting_exist['case_id'],
                        doctorid=meeting_exist['doctorid'],
                        patientid=meeting_exist['patientid'],
                        appointment_slot=meeting_exist['appointment_slot'],
                        role=role,
                        role_name=role_dict.get(role),
                        patient_name=meeting_exist.get("patient_details", {}).get("firstname") + " " + meeting_exist.get("patient_details", {}).get("lastname"),
                        doctor_name=meeting_exist.get("doctor_details", {}).get("firstname") + " " + meeting_exist.get("doctor_details", {}).get("lastname"),
                        slot_duration = meeting_exist.get("slot_duration")

                    )

                elif meeting_code == meeting_exist['doctor_meeting_code']:
                    return JitsiMeetingInfo(
                        meeting_id=meeting_exist['meeting_id'],
                        meeting_link=meeting_exist['doctor_joining_link'],
                        meeting_code=meeting_exist['doctor_meeting_code'],
                        appointment_id=meeting_exist['appointment_id'],
                        case_id=meeting_exist['case_id'],
                        doctorid=meeting_exist['doctorid'],
                        patientid=meeting_exist['patientid'],
                        appointment_slot=meeting_exist['appointment_slot'],
                        role=role,
                        role_name=role_dict.get(role),
                        patient_name=meeting_exist.get("patient_details", {}).get("firstname") + " " + meeting_exist.get("patient_details", {}).get("lastname"),
                        doctor_name=meeting_exist.get("doctor_details", {}).get("firstname") + " " + meeting_exist.get("doctor_details", {}).get("lastname"),
                        slot_duration = meeting_exist.get("slot_duration")
                    )
                else:
                    raise Exception('Invalid meeting code')

            elif current_datetime < fifteen_minutes_before_meeting_time:
                raise Exception('user has joined very early')
            elif current_datetime > thirty_minutes_after_meeting_time:
                raise Exception('user has joined very late')
            else:
                raise Exception('meeting time has passed')

        except Exception as e:
            raise HTTPException(status_code=409,
                                detail=f'error occurred as {str(e)} while getting meeting information for meeting_code: {str(meeting_code)}')

    def remove_meeting_info(self, appointment_id:str):
        try:
            # remove meeting info while toggling appointment to in clinic from virtual
            self.mongo_db['JitsiMeetInfo'].delete_one({'appointment_id':appointment_id})
        except Exception as e:
            raise Exception(f'Meeting remove error: {str(e)}')

if __name__ == '__main__':
    pass
