import random
import string
import uuid
from datetime import datetime, timedelta
import calendar
import copy
import enum
import time
from typing import Optional
from pymongo import DESCENDING
import pymongo
from sqlalchemy import and_, or_

from ayoo_backend.api.views import logger, loggers

import sqlalchemy.exc
import json

from fastapi import HTTPException
from sqlalchemy.orm import scoped_session
from .payment.payment_controller import remove_custom_fees

from ayoo_backend.api import dbmodels
from ayoo_backend.api.api_configs import OTP_GENERATOR, DATABASE_URL, p_redirect_url, p_cancel_url
from ayoo_backend.api.pdf_gen_old import PDF_Gen
from ayoo_backend.api.api_configs import OTP_GENERATOR
from ayoo_backend.api.aws_s3 import AWSS3Client
from ayoo_backend.api.chat_controller import ChatController
from ayoo_backend.api.chat_models import SaveChatModel, GetChat, SaveChat, CloseChat, GetUsersAllChat, BroadcastMessage
from ayoo_backend.api.dbmodels import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, DBRelatives, DBFormResponseTable
from ayoo_backend.api.doctor_controller import DoctorController
from ayoo_backend.api.doctormodels import SearchDoctorsBasedOnSpecialization
from ayoo_backend.api.firebase_controller import FireBaseNotificationController
from ayoo_backend.api.firebase_models import ChatEvent, PrescriptionEvent
from ayoo_backend.api.mental_health_case_models import AddChiefComplain, MentalHealthCase, GetMentalHealthCaseDetails, \
    SleepAndAppetite, DoctorNotes, SubstanceUseAdd, CaseElement, Assessment, MentalHealthMedication, \
    TreatmentPlanAdd, FollowUp, GetDoctorCases, CaseSummary, InterventionAdd, TherapyTreatmentPlan, \
    InterventionResultUpdate, TreatmentPlanResponse, TherapyRecommendationAdd, ReferCase, ClientEducation, \
    CareAssessment, CareAssessmentPatientFeedback, CareAssessmentRemoveAttribute, AssessmentFormsSubmit, \
    PatientMedicalHistory, ClinicalHistoryPermission, GetClinicalHistory, TherapyTreatmentPlanComment, GetReportsList, \
    UploadReport, UploadExtraDocument, DeleteReport, GetPatientReports, LabTest, \
    DeleteChiefComplain, GetReferralDoctorsList, TherapyNotes, ReferCaseUpdate, ReferCaseDelete, \
    UploadDocument, UploadPrescribedReport, DeleteDocument, UpdatePrescribedReport, UpdateExtraDocument, CaseElement1, \
    WorkUpPlan, WorkUpPlanAdd, GetDoctorCaseInfo, VitalsRecord
from ayoo_backend.api.supporting_staff_controller import SupportingStaffController
from ayoo_backend.api.view_controller import UserController, AdminController
from ayoo_backend.api.ayoo_utils import string_data_type_validation, is_valid_integer, is_blank_string, is_valid_length, \
    get_age_in_years
from .vitals.vitals_models import vital_units


class BaseHealthController:

    def __init__(self, db: scoped_session, mongo=None):
        self.db = db
        self.mongo = mongo
        self.mongo_db = self.mongo['ayoo']
        self.s3 = AWSS3Client()
        # self.pg=PDF_Gen(db=db, mongo=mongo)

    # @staticmethod
    def generate_case_id(self):

        mongo_collection = self.mongo_db['Appointments']
        appointment_count = (mongo_collection.count_documents({}) + 1)
        count_length = len(str(appointment_count))
        random_length = (6 - count_length)
        x = ''.join(random.choices(string.ascii_letters.upper() +
                                   string.digits, k=random_length))
        caseid = 'A' + str(appointment_count) + str(x)

        check_for_unique_case_id = mongo_collection.find_one({'caseid': caseid})
        if check_for_unique_case_id is not None:
            self.generate_case_id()

        return caseid

    def get_open_case_id(self, patient_id: str, doctor_id: str):
        try:
            val = self.mongo_db['Appointments'].find_one({"$and": [
                {"patient_id": str(patient_id)},
                {"doctorid": str(doctor_id)},
                {"case_open": True}
            ]}, sort=[('appointment_slot', -1)])

            if val is not None:
                return val['caseid']

            val = self.mongo_db['ReferCase'].find_one({"$and": [
                {"patient_id": str(patient_id)},
                {"referred_to": str(doctor_id)}
            ]})

            if val is not None:
                return val.get('referred_case_id')

            user = self.mongo_db['UserCollection'].find_one({"userid": patient_id})

            if 'psychiatrist_prescriptions' in user:
                mental_health_case = next(
                    (case for case in user["psychiatrist_prescriptions"] if
                     case["case_doctor"] == doctor_id and case['is_open'] == True),
                    None
                )

                if mental_health_case is not None:
                    return mental_health_case['case_id']

            if 'therapist_prescriptions' in user:
                mental_health_case = next(
                    (case for case in user["therapist_prescriptions"] if
                     case["case_doctor"] == doctor_id and case['is_open'] == True),
                    None
                )
                if mental_health_case is not None:
                    return mental_health_case['case_id']

            if 'medical_health_prescriptions' in user:
                mental_health_case = next(
                    (case for case in user["medical_health_prescriptions"] if
                     case["case_doctor"] == doctor_id and case['is_open'] == True),
                    None
                )
                if mental_health_case is not None:
                    return mental_health_case['case_id']

            return None

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            return None

    def check_existing_open_case_by_case_id(self, patient_id: str, case_id: str):
        try:

            user = self.mongo_db['UserCollection'].find_one({"userid": patient_id})

            if 'psychiatrist_prescriptions' in user:
                mental_health_case = next(
                    (case for case in user["psychiatrist_prescriptions"] if
                     case["case_id"] == case_id and case['is_open'] == True),
                    None
                )

                if mental_health_case is not None:
                    return True

            if 'therapist_prescriptions' in user:
                mental_health_case = next(
                    (case for case in user["therapist_prescriptions"] if
                     case["case_id"] == case_id and case['is_open'] == True),
                    None
                )
                if mental_health_case is not None:
                    return True

            if 'medical_health_prescriptions' in user:
                mental_health_case = next(
                    (case for case in user["medical_health_prescriptions"] if
                     case["case_id"] == case_id and case['is_open'] == True),
                    None
                )

                if mental_health_case is not None:
                    return True

            return False

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            return str(e)

    def get_case_type(self, patient_id: str, case_id: str):
        try:

            user = self.mongo_db['UserCollection'].find_one({"userid": patient_id})
            if user is None:
                return None

            if 'psychiatrist_prescriptions' in user:
                mental_health_case = next(
                    (case for case in user["psychiatrist_prescriptions"] if
                     case["case_id"] == case_id),
                    None
                )

                if mental_health_case is not None:
                    return 'Psychiatry'

            if 'therapist_prescriptions' in user:
                mental_health_case = next(
                    (case for case in user["therapist_prescriptions"] if
                     case["case_id"] == case_id),
                    None
                )
                if mental_health_case is not None:
                    return 'Therapy'

            if 'medical_health_prescriptions' in user:
                mental_health_case = next(
                    (case for case in user["medical_health_prescriptions"] if
                     case["case_id"] == case_id),
                    None
                )

                if mental_health_case is not None:
                    return 'Medical Health'

            new_case = self.mongo_db['Appointments'].find_one({"$and": [
                {"patient_id": str(patient_id)},
                {"caseid": str(case_id)}
            ]})
            if new_case is not None:
                return 'New'
            return None

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            return str(e)

    def check_case_is_not_in_other_dict(self, patient_id: str, case_id: str):
        try:

            user = self.mongo_db['UserCollection'].find_one({"userid": patient_id})
            if user is None:
                return False

            if 'psychiatrist_prescriptions' in user:
                mental_health_case = next(
                    (case for case in user["psychiatrist_prescriptions"] if
                     case["case_id"] == case_id),
                    None
                )

                if mental_health_case is not None:
                    return True

            if 'therapist_prescriptions' in user:
                mental_health_case = next(
                    (case for case in user["therapist_prescriptions"] if
                     case["case_id"] == case_id),
                    None
                )
                if mental_health_case is not None:
                    return True

            if 'medical_health_prescriptions' in user:
                mental_health_case = next(
                    (case for case in user["medical_health_prescriptions"] if
                     case["case_id"] == case_id),
                    None
                )

                if mental_health_case is not None:
                    return True

            return False

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            return str(e)

    def get_patient_details(self, patientid: str):
        from ayoo_backend.api import dbmodels

        patient_data = {}
        resp_user: DBUser = self.db.query(dbmodels.DBUser).filter_by(
            userid=patientid, is_deleted=False).one_or_none()
        if resp_user:
            get_mongo_data = self.mongo_db['UserCollection'].find_one({'userid': patientid})
            if get_mongo_data is not None:
                patient_data['family_doctor'] = get_mongo_data[
                    'family_doctor'] if 'family_doctor' in get_mongo_data else None

            patient_data['ayoo_id'] = resp_user.ayoo_id
            patient_data['firstname'] = resp_user.firstname
            patient_data['lastname'] = resp_user.lastname
            patient_data['dob'] = str(resp_user.birthdate)
            patient_data['email'] = resp_user.email
            patient_data['mobile'] = resp_user.mobile
            patient_data['gender'] = resp_user.gender

            return patient_data

        resp_relative: DBRelatives = self.db.query(dbmodels.DBRelatives).filter(
            DBRelatives.relativeid == patientid).first()
        if resp_relative:
            patient_data['ayoo_id'] = resp_relative.ayoo_id
            patient_data['firstname'] = resp_relative.firstname
            patient_data['lastname'] = resp_relative.lastname
            patient_data['dob'] = str(resp_relative.birthdate)
            patient_data['email'] = resp_relative.email
            patient_data['mobile'] = resp_relative.mobile
            patient_data['gender'] = resp_relative.gender

            return patient_data
        else:
            return None

    def get_doctor_details(self, doctor_id: str):
        try:
            doctor_ctrl = DoctorController(db=self.db, mongo=self.mongo)

            service_provider = {}
            doctor_details, msg = doctor_ctrl.get_by_id(
                doctorid=doctor_id)
            if doctor_details:
                service_provider = {
                    'doctor_firstname': doctor_details['firstname'],
                    'doctor_lastname': doctor_details['lastname'],
                    'graduation': doctor_details['graduation'],
                    'masters': doctor_details['masters'],
                    'fellowship': doctor_details['fellowship'],
                    'specialization': doctor_details['specialization'],
                    'practice_area': doctor_details['practice_area'],
                    'consulting_duration_virtual': doctor_details['consulting_duration_virtual'],
                    'consulting_duration_clinic': doctor_details['consulting_duration_clinic'],
                    'consulting_fees_virtual': doctor_details['consulting_fees_virtual'],
                    'consulting_fees_clinic': doctor_details['consulting_fees_clinic'],
                    'profile_image': {
                        'image_id': doctor_details['image_id'] if 'image_id' in doctor_details else None,
                        'profile_image_url': doctor_details[
                            'profile_image_url'] if 'profile_image_url' in doctor_details else None
                    },
                    'license_no': doctor_details['license'] if 'license' in doctor_details else '',
                    'signature_img': doctor_details['signature'] if 'signature' in doctor_details else ''
                }

            return service_provider
        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            raise Exception('Error while getting doctor details')


class MentalHealthController(BaseHealthController):
    def __init__(self, db: scoped_session, mongo=None):
        super().__init__(db=db, mongo=mongo)

    def has_write_access(self, userid: str):
        try:
            doctor_ctrl = DoctorController(db=self.db, mongo=self.mongo)
            admin_ctrl = AdminController(db=self.db, mongo=self.mongo)
            sup_stf_ctrl = SupportingStaffController(db=self.db, mongo=self.mongo)

            doctor_check, resp_msg = doctor_ctrl.get_by_id(doctorid=userid)
            if doctor_check is not None:
                return True
            admin_check = admin_ctrl.get_admin_by_id(userid=userid)
            if admin_check is not None:
                return True
            supporting_staff_check, msg = sup_stf_ctrl.get_supporting_staff_by_id(team_member_id=userid)
            if supporting_staff_check is not None:
                return True

            return False

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            err = str(e)
            raise HTTPException(status_code=409, detail=f'Internal Error code {err} for checking for user id')

    def is_valid_patient_id(self, patient_id: str):
        ctrl = UserController(db=self.db, otp_generator=None)

        user_details = ctrl.get_user_details(userid=patient_id, mongo=self.mongo)
        if user_details is None:
            raise Exception('Invalid patient ID')

    def is_valid_case_id(self, patient_id: str, case_id: str):
        valid_case_id = self.mongo_db['Appointments'].find_one(
            {"caseid": case_id, "patient_id": patient_id})

        if valid_case_id is not None:
            return

        valid_case_id = self.mongo_db['UserCollection'].find_one({
            "userid": patient_id,
            "psychiatrist_prescriptions": {
                "$elemMatch": {
                    "case_id": case_id
                }
            }
        })

        if valid_case_id is not None:
            return

        valid_case_id = self.mongo_db['UserCollection'].find_one({
            "userid": patient_id,
            "therapist_prescriptions": {
                "$elemMatch": {
                    "case_id": case_id
                }
            }
        })
        if valid_case_id is not None:
            return

        valid_case_id = self.mongo_db['UserCollection'].find_one({
            "userid": patient_id,
            "medical_health_prescriptions": {
                "$elemMatch": {
                    "case_id": case_id
                }
            }
        })
        if valid_case_id is not None:
            return
        # if valid_case_id is None:
        raise Exception('Invalid case ID')

    def is_psychiatry_case_open(self, patient_id: str, case_id: str):
        try:
            case_open = self.mongo_db['UserCollection'].find_one({
                "userid": patient_id,
                "psychiatrist_prescriptions": {
                    "$elemMatch": {
                        "case_id": case_id,
                        "is_open": False
                    }
                }
            })

            if case_open is not None:
                return False
                # raise Exception('Case closed')

            return True
        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            raise Exception(e)

    def is_therapy_case_open(self, patient_id: str, case_id: str):
        try:
            case_open = self.mongo_db['UserCollection'].find_one({
                "userid": patient_id,
                "therapist_prescriptions": {
                    "$elemMatch": {
                        "case_id": case_id,
                        "is_open": False
                    }
                }
            })

            if case_open is not None:
                return False
                # raise Exception('Case closed')

            return True
        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            raise Exception(e)

    def is_medical_health_case_open(self, patient_id: str, case_id: str):
        try:
            case_open = self.mongo_db['UserCollection'].find_one({
                "userid": patient_id,
                "medical_health_prescriptions": {
                    "$elemMatch": {
                        "case_id": case_id,
                        "is_open": False
                    }
                }
            })

            if case_open is not None:
                return False
                # raise Exception('Case closed')

            return True
        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            raise Exception(e)

    def is_case_open(self, patient_id: str, case_id: str):
        get_case_type = self.get_case_type(patient_id=patient_id, case_id=case_id)
        if get_case_type is None:
            raise Exception('Invalid case id or patient id')

        case_open = None
        if get_case_type == 'Medical Health':
            case_open = self.is_medical_health_case_open(patient_id=patient_id, case_id=case_id)
            # #logger.info('Medical Health case open => ', case_open)

        if get_case_type == 'Psychiatry':
            case_open = self.is_psychiatry_case_open(patient_id=patient_id, case_id=case_id)
            # #logger.info('Psychiatry case open => ', case_open)

        if get_case_type == 'Therapy':
            case_open = self.is_therapy_case_open(patient_id=patient_id, case_id=case_id)
            # #logger.info('Therapy case open => ', case_open)

        return case_open

    def get_blank_therapist_case_dict(self, patient_id: str, case_id: str, doctor_id: str = None):
        if doctor_id is None:
            get_doctor_id = self.mongo_db['Appointments'].find_one(
                {"caseid": case_id, "patient_id": patient_id})
            if get_doctor_id is None:
                raise Exception('No doctor found for the given case')

            doctor_id = get_doctor_id['doctorid']

        therapist_case_blank_dict = dict(
            case_id=case_id,
            date_open=datetime.now(),
            case_doctor=doctor_id,
            is_open=True,
            chief_complain={
                'all_records': [],
                'current_record': {}
            },
            sleep_appetite={
                'all_records': [],
                'current_record': {}
            },
            notes={
                'all_records': [],
                'current_record': {}
            },
            # intervention={
            #     'all_records': [],
            #     'current_record': {}
            # },
            assessment={
                'all_records': [],
                'current_record': {}
            },
            treatment_plan={
                'all_records': [],
                'current_record': {}
            },
            treatment_plan_response={
                'all_records': [],
                'current_record': {}
            },
            therapy_recommendation={
                'all_records': [],
                'current_record': {}
            },
            follow_up={
                'all_records': [],
                'current_record': {}
            },
            notes_for_doctor={
                'all_records': [],
                'current_record': {}
            },
            care_assessment={},
            assessment_forms=[],
            case_summary={
                'all_records': [],
                'current_record': {}
            },
            date_closed=None,
            referral_data={
                'referred_to': [],
                'referred_by': []
            },
            medical_history={},
            allow_clinical_history_access=False
        )

        return therapist_case_blank_dict

    def get_blank_psychiatrist_case_dict(self, patient_id: str, case_id: str, doctor_id: str = None):
        if doctor_id is None:
            get_doctor_id = self.mongo_db['Appointments'].find_one(
                {"caseid": case_id, "patient_id": patient_id})
            if get_doctor_id is None:
                raise Exception('No doctor found for the given case')
            doctor_id = get_doctor_id['doctorid']

        psychiatric_case_blank_dict = dict(
            case_id=case_id,
            date_open=datetime.now(),
            case_doctor=doctor_id,
            is_open=True,
            chief_complain={
                'all_records': [],
                'current_record': {}
            },
            sleep_appetite={
                'all_records': [],
                'current_record': {}
            },
            doctor_notes={
                'all_records': [],
                'current_record': {}
            },
            substances={
                'all_records': [],
                'current_record': {}
            },
            assessment={
                'all_records': [],
                'current_record': {}
            },
            lab_tests={
                'all_records': [],
                'current_record': {}
            },
            medications={
                'all_records': [],
                'current_record': {}
            },
            medicine_reminder=True,
            treatment_plan={
                'all_records': [],
                'current_record': {}
            },
            therapy_recommendation={
                'all_records': [],
                'current_record': {}
            },
            follow_up={
                'all_records': [],
                'current_record': {}
            },
            care_assessment={},
            assessment_forms=[],
            case_summary={
                'all_records': [],
                'current_record': {}
            },
            date_closed=None,
            referral_data={
                'referred_to': [],
                'referred_by': []
            },
            medical_history={},
            allow_clinical_history_access=False
        )

        return psychiatric_case_blank_dict

    # get case type of mental health case
    def get_mental_health_case_type(self, patient_id: str, case_id: str):
        try:
            get_case = self.mongo_db['UserCollection'].find_one({
                "userid": patient_id,
                "therapist_prescriptions": {
                    "$elemMatch": {
                        "case_id": case_id
                    }
                }
            })
            if get_case is not None:
                return 'therapist_prescriptions'

            get_case = self.mongo_db['UserCollection'].find_one({
                "userid": patient_id,
                "psychiatrist_prescriptions": {
                    "$elemMatch": {
                        "case_id": case_id
                    }
                }
            })
            if get_case is not None:
                return 'psychiatrist_prescriptions'

            get_case = self.mongo_db['UserCollection'].find_one({
                "userid": patient_id,
                "medical_health_prescriptions": {
                    "$elemMatch": {
                        "case_id": case_id
                    }
                }
            })
            if get_case is not None:
                return 'medical_health_prescriptions'

            return None

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            raise Exception(str(e))

    def if_user_exists_with_therapy_case_record(self, patient_id: str, case_id: str, doctor_id: str = None):

        user_data_exists = self.mongo_db['UserCollection'].find_one({"userid": patient_id})

        if user_data_exists is not None and 'therapist_prescriptions' in user_data_exists:
            return

        # check if case is not in other dict
        case_already_exist = self.check_case_is_not_in_other_dict(patient_id=patient_id, case_id=case_id)
        if case_already_exist is True:
            raise Exception(
                'Case ID not of therapy type. It already exist in either psychiatry case or primary care case')

        dict_to_add = self.get_blank_therapist_case_dict(patient_id=patient_id, case_id=case_id, doctor_id=doctor_id)

        if user_data_exists is None:
            self.insert_new_user_record_with_therapist_case(mh_data=dict_to_add, patient_id=patient_id)

        else:
            # if user data exists, check if it has mental_health_case key
            if 'therapist_prescriptions' not in user_data_exists:
                self.insert_therapist_key_to_existing_user_record(mh_data=dict_to_add, patient_id=patient_id)

    def if_therapist_case_data_exists(self, patient_id: str, case_id: str, doctor_id: str = None):

        case_exist = self.mongo_db['UserCollection'].find_one(
            {"userid": patient_id, "therapist_prescriptions.case_id": case_id})
        if case_exist is None:
            # check if case is not in other dict
            case_already_exist = self.check_case_is_not_in_other_dict(patient_id=patient_id, case_id=case_id)
            if case_already_exist is True:
                raise Exception(
                    'Case ID not of therapy type. It already exist in either psychiatry case or primary care case')

            dict_to_add = self.get_blank_therapist_case_dict(patient_id=patient_id, case_id=case_id,
                                                             doctor_id=doctor_id)
            self.insert_new_therapist_case_dict_to_existing_mh_record(mh_data=dict_to_add, patient_id=patient_id)

    def is_valid_therapy_case_conditions(self, patient_id: str, case_id: str):
        # check if patient_id provided is correct
        # self.is_valid_patient_id(patient_id=patient_id)
        valid_patient = self.get_patient_details(patientid=patient_id)
        if valid_patient is None:
            raise Exception(f'Patient does not exists with patient ID: {patient_id}')

        # check if case id is valid
        self.is_valid_case_id(patient_id=patient_id, case_id=case_id)

        # check if case is open
        case_open = self.is_therapy_case_open(patient_id=patient_id, case_id=case_id)
        if case_open is False:
            raise Exception('Case is not active')

        # check if there is existing data for the patient in UserCollection
        self.if_user_exists_with_therapy_case_record(patient_id=patient_id, case_id=case_id)

        # # if mental_health_case key found, check if given case id exists
        self.if_therapist_case_data_exists(patient_id=patient_id, case_id=case_id)

    def get_updated_therapy_case_record(self, patient_id: str, case_id: str):
        try:
            user = self.mongo_db['UserCollection'].find_one({"userid": patient_id})
            if 'therapist_prescriptions' not in user:
                raise Exception('No record found')
            mental_health_case = next(
                (case for case in user["therapist_prescriptions"] if case["case_id"] == case_id),
                None
            )
            mental_health_case['session_no'] = mental_health_case['notes']['current_record'][
                'session'] if 'session' in mental_health_case['notes']['current_record'] else 1
            case_doctor_details = self.get_doctor_details(doctor_id=mental_health_case['case_doctor'])

            referral_to_data = []
            referral_by_data = []
            for referred_to_data in mental_health_case['referral_data']['referred_to']:
                referred_to_doctor_details = self.get_doctor_details(doctor_id=referred_to_data['referred_to'])
                referral_to_data.append({
                    "referred_to": referred_to_data['referred_to'],
                    "case_id": referred_to_data['case_id'],
                    # "treatment_name": referred_to_data['treatment_name'],
                    "referral_comments": referred_to_data['referral_comments'],
                    "referral_date": referred_to_data['referral_date'],
                    "referred_to_doctor_details": referred_to_doctor_details
                })

            for referred_by_data in mental_health_case['referral_data']['referred_by']:
                referred_by_doctor_details = self.get_doctor_details(doctor_id=referred_by_data['referred_by'])
                referral_by_data.append({
                    "referred_by": referred_by_data['referred_by'],
                    "case_id": referred_by_data['case_id'],
                    # "treatment_name": referred_by_data['treatment_name'],
                    "referral_comments": referred_by_data['referral_comments'],
                    "referral_date": referred_by_data['referral_date'],
                    "referred_by_doctor_details": referred_by_doctor_details
                })

            mental_health_case['case_doctor_details'] = case_doctor_details
            mental_health_case['referral_data'] = {
                "referred_to": referral_to_data,
                "referred_by": referral_by_data
            }

            chat_history = self.mongo_db['ChatMessages'].find_one(
                {"$or": [{
                    "$and": [{"user_one_id": patient_id}, {"user_two_id": mental_health_case['case_doctor']},
                             {"chat_open": True}]
                }, {
                    "$and": [{"user_one_id": mental_health_case['case_doctor']}, {"user_two_id": patient_id},
                             {"chat_open": True}]
                }
                ]})

            mental_health_case['chat_history'] = chat_history['chat_history'] if chat_history is not None else []

            return mental_health_case
        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            raise Exception(str(e))

    def insert_new_user_record_with_therapist_case(self, mh_data: {}, patient_id: str):
        try:
            self.mongo_db['UserCollection'].insert_one({"userid": patient_id,
                                                        "therapist_prescriptions": [mh_data]
                                                        })
        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            raise HTTPException(status_code=409, detail=str(e))

    def insert_therapist_key_to_existing_user_record(self, mh_data: {}, patient_id: str):
        try:
            self.mongo_db['UserCollection'].find_one_and_update({"userid": patient_id}, {
                "$set": {
                    "therapist_prescriptions": [mh_data]
                }
            })
        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            raise HTTPException(status_code=409, detail=str(e))

    def insert_new_therapist_case_dict_to_existing_mh_record(self, mh_data: {}, patient_id: str):
        try:
            self.mongo_db['UserCollection'].find_one_and_update({"userid": patient_id}, {
                "$push": {
                    "therapist_prescriptions": mh_data
                }
            })
        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            raise HTTPException(status_code=409, detail=str(e))

    def add_record_to_existing_therapist_case_key(self, key_name: str, data_to_record: {}, patient_id: str,
                                                  case_id: str):
        try:
            self.mongo_db['UserCollection'].find_one_and_update(
                {"userid": patient_id,
                 "therapist_prescriptions.case_id": case_id},
                {
                    "$push": {
                        f'therapist_prescriptions.$.{key_name}.all_records': data_to_record
                    },
                    "$set": {
                        f'therapist_prescriptions.$.{key_name}.current_record': data_to_record
                    }
                }
            )
        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            raise Exception(str(e))

    def if_user_exists_with_psychiatry_case_record(self, patient_id: str, case_id: str, doctor_id: str = None):

        user_data_exists = self.mongo_db['UserCollection'].find_one({"userid": patient_id})

        if user_data_exists is not None and 'psychiatrist_prescriptions' in user_data_exists:
            return

        # check if case is not in other dict
        case_already_exist = self.check_case_is_not_in_other_dict(patient_id=patient_id, case_id=case_id)
        if case_already_exist is True:
            raise Exception(
                'Case ID not of psychiatry type. It already exist in either therapy case or primary care case')

        dict_to_add = self.get_blank_psychiatrist_case_dict(patient_id=patient_id, case_id=case_id, doctor_id=doctor_id)
        if user_data_exists is None:
            self.insert_new_user_record_with_mh_case(mh_data=dict_to_add, patient_id=patient_id)

        else:
            # if user data exists, check if it has mental_health_case key
            if 'psychiatrist_prescriptions' not in user_data_exists:
                self.insert_mh_key_to_existing_user_record(mh_data=dict_to_add, patient_id=patient_id)

    def if_psychiatrist_case_data_exists(self, patient_id: str, case_id: str, doctor_id: str = None):

        case_exist = self.mongo_db['UserCollection'].find_one(
            {"userid": patient_id, "psychiatrist_prescriptions.case_id": case_id})
        if case_exist is None:

            # check if case is not in other dict
            case_already_exist = self.check_case_is_not_in_other_dict(patient_id=patient_id, case_id=case_id)
            if case_already_exist is True:
                raise Exception(
                    'Case ID not of psychiatry type. It already exist in either therapy case or primary care case')

            dict_to_add = self.get_blank_psychiatrist_case_dict(patient_id=patient_id, case_id=case_id,
                                                                doctor_id=doctor_id)
            self.insert_new_case_dict_to_existing_mh_record(mh_data=dict_to_add, patient_id=patient_id)

    # Check if the user is attempting to update a record that was added today.
    # Users are not allowed to edit any record one day after its insertion.

    def is_valid_psychiatry_case_conditions(self, patient_id: str, case_id: str):
        # check if patient_id provided is correct
        # self.is_valid_patient_id(patient_id=patient_id)
        valid_patient = self.get_patient_details(patientid=patient_id)
        if valid_patient is None:
            raise Exception(f'Patient does not exists with patient ID: {patient_id}')

        # check if case id is valid
        self.is_valid_case_id(patient_id=patient_id, case_id=case_id)

        # check if case is open
        case_open = self.is_psychiatry_case_open(patient_id=patient_id, case_id=case_id)
        if case_open is False:
            raise Exception('Case is not active')

        self.if_user_exists_with_psychiatry_case_record(patient_id=patient_id, case_id=case_id)

        # # if mental_health_case key found, check if given case id exists
        self.if_psychiatrist_case_data_exists(patient_id=patient_id, case_id=case_id)

    def get_updated_psychiatry_case_record(self, patient_id: str, case_id: str):
        try:
            user = self.mongo_db['UserCollection'].find_one({"userid": patient_id})
            if 'psychiatrist_prescriptions' not in user:
                raise Exception('No record found')
            mental_health_case = next(
                (case for case in user["psychiatrist_prescriptions"] if case["case_id"] == case_id),
                None
            )
            mental_health_case['session_no'] = mental_health_case['doctor_notes']['current_record'][
                'session'] if 'session' in mental_health_case['doctor_notes']['current_record'] else 1
            case_doctor_details = self.get_doctor_details(doctor_id=mental_health_case['case_doctor'])

            referral_to_data = []
            referral_by_data = []
            for referred_to_data in mental_health_case['referral_data']['referred_to']:
                referred_to_doctor_details = self.get_doctor_details(doctor_id=referred_to_data['referred_to'])
                referral_to_data.append({
                    "referred_to": referred_to_data['referred_to'],
                    "case_id": referred_to_data['case_id'],
                    # "treatment_name": referred_to_data['treatment_name'],
                    "referral_comments": referred_to_data['referral_comments'],
                    "referral_date": referred_to_data['referral_date'],
                    "referred_to_doctor_details": referred_to_doctor_details
                })
            for referred_by_data in mental_health_case['referral_data']['referred_by']:
                referred_by_doctor_details = self.get_doctor_details(doctor_id=referred_by_data['referred_by'])
                referral_by_data.append({
                    "referred_by": referred_by_data['referred_by'],
                    "case_id": referred_by_data['case_id'],
                    # "treatment_name": referred_by_data['treatment_name'],
                    "referral_comments": referred_by_data['referral_comments'],
                    "referral_date": referred_by_data['referral_date'],
                    "referred_by_doctor_details": referred_by_doctor_details
                })
            mental_health_case['case_doctor_details'] = case_doctor_details
            mental_health_case['referral_data'] = {
                "referred_to": referral_to_data,
                "referred_by": referral_by_data
            }

            chat_history = self.mongo_db['ChatMessages'].find_one(
                {"$or": [{
                    "$and": [{"user_one_id": patient_id}, {"user_two_id": mental_health_case['case_doctor']},
                             {"chat_open": True}]
                }, {
                    "$and": [{"user_one_id": mental_health_case['case_doctor']}, {"user_two_id": patient_id},
                             {"chat_open": True}]
                }
                ]})

            mental_health_case['chat_history'] = chat_history['chat_history'] if chat_history is not None else []

            return mental_health_case

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            raise HTTPException(status_code=409, detail=str(e))

    def insert_new_user_record_with_mh_case(self, mh_data: {}, patient_id: str):
        try:
            self.mongo_db['UserCollection'].insert_one({"userid": patient_id,
                                                        "psychiatrist_prescriptions": [mh_data]
                                                        })
        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            raise HTTPException(status_code=409, detail=str(e))

    def insert_mh_key_to_existing_user_record(self, mh_data: {}, patient_id: str):
        try:
            self.mongo_db['UserCollection'].find_one_and_update({"userid": patient_id}, {
                "$set": {
                    "psychiatrist_prescriptions": [mh_data]
                }
            })
        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            raise HTTPException(status_code=409, detail=str(e))

    def insert_new_case_dict_to_existing_mh_record(self, mh_data: {}, patient_id: str):
        try:
            self.mongo_db['UserCollection'].find_one_and_update({"userid": patient_id}, {
                "$push": {
                    "psychiatrist_prescriptions": mh_data
                }
            })
        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            raise HTTPException(status_code=409, detail=str(e))

    def insert_new_case_dict_to_existing_medical_health_record(self, mh_data: {}, patient_id: str):
        try:
            self.mongo_db['UserCollection'].find_one_and_update({"userid": patient_id}, {
                "$push": {
                    "medical_health_prescriptions": mh_data
                }
            })
        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            raise HTTPException(status_code=409, detail=str(e))

    def add_record_to_existing_psychiatry_case_key(self, key_name: str, data_to_record: {}, patient_id: str,
                                                   case_id: str):
        try:
            self.mongo_db['UserCollection'].find_one_and_update(
                {"userid": patient_id,
                 "psychiatrist_prescriptions.case_id": case_id},
                {
                    "$push": {
                        f'psychiatrist_prescriptions.$.{key_name}.all_records': data_to_record
                    },
                    "$set": {
                        f'psychiatrist_prescriptions.$.{key_name}.current_record': data_to_record
                    }
                }
            )
        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            raise Exception(str(e))

    def get_blank_medical_health_case_dict(self, patient_id: str, case_id: str, doctor_id: str = None):
        if doctor_id is None:
            get_doctor_id = self.mongo_db['Appointments'].find_one(
                {"caseid": case_id, "patient_id": patient_id})
            if get_doctor_id is None:
                raise Exception('No doctor found for the given case')
            doctor_id = get_doctor_id['doctorid']

        medical_health_case_blank_dict = dict(
            case_id=case_id,
            date_open=datetime.now(),
            case_doctor=doctor_id,
            is_open=True,
            chief_complain={
                'all_records': [],
                'current_record': {}
            },
            doctor_notes={
                'all_records': [],
                'current_record': {}
            },
            substances={
                'all_records': [],
                'current_record': {}
            },
            assessment={
                'all_records': [],
                'current_record': {}
            },
            lab_tests={
                'all_records': [],
                'current_record': {}
            },
            medications={
                'all_records': [],
                'current_record': {}
            },
            medicine_reminder=True,
            work_up_plan={
                'all_records': [],
                'current_record': {}
            },
            follow_up={
                'all_records': [],
                'current_record': {}
            },
            care_assessment={},
            assessment_forms=[],
            case_summary={
                'all_records': [],
                'current_record': {}
            },
            date_closed=None,
            referral_data={
                'referred_to': [],
                'referred_by': []
            },
            medical_history={},
            allow_clinical_history_access=False
        )

        return medical_health_case_blank_dict

    def if_user_exists_with_medical_health_case_record(self, patient_id: str, case_id: str, doctor_id: str = None):

        user_data_exists = self.mongo_db['UserCollection'].find_one({"userid": patient_id})

        if user_data_exists is not None and 'medical_health_prescriptions' in user_data_exists:
            return

        # check if case is not in other dict
        case_already_exist = self.check_case_is_not_in_other_dict(patient_id=patient_id, case_id=case_id)
        if case_already_exist is True:
            raise Exception(
                'Case ID not of medical health type. It already exist in mental health case')

        dict_to_add = self.get_blank_medical_health_case_dict(patient_id=patient_id, case_id=case_id,
                                                              doctor_id=doctor_id)

        if user_data_exists is None:
            self.insert_new_user_record_with_medical_health_case(mh_data=dict_to_add, patient_id=patient_id)

        else:
            # if user data exists, check if it has mental_health_case key
            if 'medical_health_prescriptions' not in user_data_exists:
                self.insert_medical_health_key_to_existing_user_record(mh_data=dict_to_add, patient_id=patient_id)

    def if_medical_health_case_data_exists(self, patient_id: str, case_id: str, doctor_id: str = None):

        case_exist = self.mongo_db['UserCollection'].find_one(
            {"userid": patient_id, "medical_health_prescriptions.case_id": case_id})
        if case_exist is None:

            # check if case is not in other dict
            case_already_exist = self.check_case_is_not_in_other_dict(patient_id=patient_id, case_id=case_id)
            if case_already_exist is True:
                raise Exception(
                    'Case ID not of medical_health type. It already exist in either therapy case or primary care case')

            dict_to_add = self.get_blank_medical_health_case_dict(patient_id=patient_id, case_id=case_id,
                                                                  doctor_id=doctor_id)
            self.insert_new_case_dict_to_existing_medical_health_record(mh_data=dict_to_add, patient_id=patient_id)

    # Check if the user is attempting to update a record that was added today.
    # Users are not allowed to edit any record one day after its insertion.

    def is_valid_medical_health_case_conditions(self, patient_id: str, case_id: str):
        # check if patient_id provided is correct
        # self.is_valid_patient_id(patient_id=patient_id)
        valid_patient = self.get_patient_details(patientid=patient_id)
        if valid_patient is None:
            raise Exception(f'Patient does not exists with patient ID: {patient_id}')

        # check if case id is valid
        self.is_valid_case_id(patient_id=patient_id, case_id=case_id)

        # check if case is open
        case_open = self.is_medical_health_case_open(patient_id=patient_id, case_id=case_id)
        if case_open is False:
            raise Exception('Case is not active')

        self.if_user_exists_with_medical_health_case_record(patient_id=patient_id, case_id=case_id)

        # # if mental_health_case key found, check if given case id exists
        self.if_medical_health_case_data_exists(patient_id=patient_id, case_id=case_id)

    def get_updated_medical_health_case_record(self, patient_id: str, case_id: str):
        try:
            user = self.mongo_db['UserCollection'].find_one({"userid": patient_id})
            if 'medical_health_prescriptions' not in user:
                raise Exception('No record found')
            mental_health_case = next(
                (case for case in user["medical_health_prescriptions"] if case["case_id"] == case_id),
                None
            )
            mental_health_case['session_no'] = mental_health_case['doctor_notes']['current_record'][
                'session'] if 'session' in mental_health_case['doctor_notes']['current_record'] else 1
            case_doctor_details = self.get_doctor_details(doctor_id=mental_health_case['case_doctor'])

            referral_to_data = []
            referral_by_data = []
            for referred_to_data in mental_health_case['referral_data']['referred_to']:
                referred_to_doctor_details = self.get_doctor_details(doctor_id=referred_to_data['referred_to'])
                referral_to_data.append({
                    "referred_to": referred_to_data['referred_to'],
                    "case_id": referred_to_data['case_id'],
                    # "treatment_name": referred_to_data['treatment_name'],
                    "referral_comments": referred_to_data['referral_comments'],
                    "referral_date": referred_to_data['referral_date'],
                    "referred_to_doctor_details": referred_to_doctor_details
                })
            for referred_by_data in mental_health_case['referral_data']['referred_by']:
                referred_by_doctor_details = self.get_doctor_details(doctor_id=referred_by_data['referred_by'])
                referral_by_data.append({
                    "referred_by": referred_by_data['referred_by'],
                    "case_id": referred_by_data['case_id'],
                    # "treatment_name": referred_by_data['treatment_name'],
                    "referral_comments": referred_by_data['referral_comments'],
                    "referral_date": referred_by_data['referral_date'],
                    "referred_by_doctor_details": referred_by_doctor_details
                })
            mental_health_case['case_doctor_details'] = case_doctor_details
            mental_health_case['referral_data'] = {
                "referred_to": referral_to_data,
                "referred_by": referral_by_data
            }

            chat_history = self.mongo_db['ChatMessages'].find_one(
                {"$or": [{
                    "$and": [{"user_one_id": patient_id}, {"user_two_id": mental_health_case['case_doctor']},
                             {"chat_open": True}]
                }, {
                    "$and": [{"user_one_id": mental_health_case['case_doctor']}, {"user_two_id": patient_id},
                             {"chat_open": True}]
                }
                ]})

            mental_health_case['chat_history'] = chat_history['chat_history'] if chat_history is not None else []

            return mental_health_case

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            raise HTTPException(status_code=409, detail=str(e))

    def insert_new_user_record_with_medical_health_case(self, mh_data: {}, patient_id: str):
        try:
            self.mongo_db['UserCollection'].insert_one({"userid": patient_id,
                                                        "medical_health_prescriptions": [mh_data]
                                                        })
        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            raise HTTPException(status_code=409, detail=str(e))

    def insert_medical_health_key_to_existing_user_record(self, mh_data: {}, patient_id: str):
        try:
            self.mongo_db['UserCollection'].find_one_and_update({"userid": patient_id}, {
                "$set": {
                    "medical_health_prescriptions": [mh_data]
                }
            })
        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            raise HTTPException(status_code=409, detail=str(e))

    def add_record_to_existing_medical_health_case_key(self, key_name: str, data_to_record: {}, patient_id: str,
                                                       case_id: str):
        try:
            self.mongo_db['UserCollection'].find_one_and_update(
                {"userid": patient_id,
                 "medical_health_prescriptions.case_id": case_id},
                {
                    "$push": {
                        f'medical_health_prescriptions.$.{key_name}.all_records': data_to_record
                    },
                    "$set": {
                        f'medical_health_prescriptions.$.{key_name}.current_record': data_to_record
                    }
                }
            )
        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            raise Exception(str(e))

    def get_medical_health_case_details(self, data: GetMentalHealthCaseDetails):
        try:
            # self.is_valid_patient_id(patient_id=data.patient_id)
            valid_patient = self.get_patient_details(patientid=data.patient_id)
            if valid_patient is None:
                raise Exception(f'Patient does not exists with patient ID: {data.patient_id}')

            self.is_valid_case_id(patient_id=data.patient_id, case_id=data.case_id)

            mental_health_case = self.get_updated_medical_health_case_record(patient_id=data.patient_id,
                                                                             case_id=data.case_id)
            assessment_forms = self.get_latest_form_assessment_results(patient_id=data.patient_id, case_id=data.case_id)

            latest_case_data = dict(
                case_id=mental_health_case['case_id'],
                date_open=mental_health_case['date_open'],
                case_doctor=mental_health_case['case_doctor'],
                is_open=mental_health_case['is_open'],
                chief_complain=mental_health_case['chief_complain']['current_record'],
                doctor_notes=mental_health_case['doctor_notes']['current_record'],
                substances=mental_health_case['substances']['current_record'],
                assessment=mental_health_case['assessment']['current_record'],
                lab_tests=mental_health_case['lab_tests'][
                    'current_record'] if 'lab_tests' in mental_health_case else None,
                medications=mental_health_case['medications']['current_record'],
                medicine_reminder=mental_health_case['medicine_reminder'],
                work_up_plan=mental_health_case['work_up_plan']['current_record'],
                follow_up=mental_health_case['follow_up']['current_record'],
                client_education=mental_health_case['client_education'][
                    'current_record'] if 'client_education' in mental_health_case else None,
                care_assessment=mental_health_case[
                    'care_assessment'] if 'care_assessment' in mental_health_case else {},
                assessment_forms=assessment_forms,
                case_summary=mental_health_case['case_summary']['current_record'],
                date_closed=mental_health_case['date_closed'],
                referral_data=mental_health_case['referral_data'],
                medical_history=mental_health_case[
                    'medical_history'] if 'medical_history' in mental_health_case else {},
                allow_clinical_history_access=mental_health_case[
                    'allow_clinical_history_access'] if 'allow_clinical_history_access' in mental_health_case else False,
                s3_url=mental_health_case['patients_prescription_view'][-1][
                    's3_url'] if 'patients_prescription_view' in mental_health_case else ''
            )
            if not mental_health_case:
                # return None, 'No case details found for given data'
                return None
            return dict(
                msg='Success',
                data=latest_case_data
            )

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(e)
            return None

    def get_psychiatry_case_details(self, data: GetMentalHealthCaseDetails):
        try:
            # self.is_valid_patient_id(patient_id=data.patient_id)
            valid_patient = self.get_patient_details(patientid=data.patient_id)
            if valid_patient is None:
                raise Exception(f'Patient does not exists with patient ID: {data.patient_id}')

            self.is_valid_case_id(patient_id=data.patient_id, case_id=data.case_id)

            mental_health_case = self.get_updated_psychiatry_case_record(patient_id=data.patient_id,
                                                                         case_id=data.case_id)
            assessment_forms = self.get_latest_form_assessment_results(patient_id=data.patient_id, case_id=data.case_id)

            latest_case_data = dict(
                case_id=mental_health_case['case_id'],
                date_open=mental_health_case['date_open'],
                case_doctor=mental_health_case['case_doctor'],
                is_open=mental_health_case['is_open'],
                chief_complain=mental_health_case['chief_complain']['current_record'],
                sleep_appetite=mental_health_case['sleep_appetite']['current_record'],
                doctor_notes=mental_health_case['doctor_notes']['current_record'],
                substances=mental_health_case['substances']['current_record'],
                assessment=mental_health_case['assessment']['current_record'],
                lab_tests=mental_health_case['lab_tests'][
                    'current_record'] if 'lab_tests' in mental_health_case else None,
                medications=mental_health_case['medications']['current_record'],
                medicine_reminder=mental_health_case['medicine_reminder'],
                treatment_plan=mental_health_case['treatment_plan']['current_record'],
                therapy_recommendation=mental_health_case['therapy_recommendation'][
                    'current_record'] if 'therapy_recommendation' in mental_health_case else None,
                follow_up=mental_health_case['follow_up']['current_record'],
                client_education=mental_health_case['client_education'][
                    'current_record'] if 'client_education' in mental_health_case else None,
                care_assessment=mental_health_case[
                    'care_assessment'] if 'care_assessment' in mental_health_case else {},
                assessment_forms=assessment_forms,
                case_summary=mental_health_case['case_summary']['current_record'],
                date_closed=mental_health_case['date_closed'],
                referral_data=mental_health_case['referral_data'],
                medical_history=mental_health_case[
                    'medical_history'] if 'medical_history' in mental_health_case else {},
                allow_clinical_history_access=mental_health_case[
                    'allow_clinical_history_access'] if 'allow_clinical_history_access' in mental_health_case else False,
                s3_url=mental_health_case['patients_prescription_view'][-1][
                    's3_url'] if 'patients_prescription_view' in mental_health_case else ''
            )
            if not mental_health_case:
                # return None, 'No case details found for given data'
                return None
            return dict(
                msg='Success',
                data=latest_case_data
            )

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(e)
            return None

    def get_therapy_case_details(self, data: GetMentalHealthCaseDetails):
        try:
            # self.is_valid_patient_id(patient_id=data.patient_id)
            valid_patient = self.get_patient_details(patientid=data.patient_id)
            if valid_patient is None:
                raise Exception(f'Patient does not exists with patient ID: {data.patient_id}')
            self.is_valid_case_id(patient_id=data.patient_id, case_id=data.case_id)

            mental_health_case = self.get_updated_therapy_case_record(patient_id=data.patient_id, case_id=data.case_id)
            assessment_forms = self.get_latest_form_assessment_results(patient_id=data.patient_id, case_id=data.case_id)

            latest_case_data = dict(
                case_id=mental_health_case['case_id'],
                date_open=mental_health_case['date_open'],
                case_doctor=mental_health_case['case_doctor'],
                is_open=mental_health_case['is_open'],
                chief_complain=mental_health_case['chief_complain']['current_record'],
                sleep_appetite=mental_health_case['sleep_appetite']['current_record'],
                notes=mental_health_case['notes']['current_record'],
                assessment=mental_health_case['assessment'][
                    'current_record'] if 'assessment' in mental_health_case else {},
                # interventions=mental_health_case['interventions']['current_record'],
                treatment_plan=mental_health_case['treatment_plan']['current_record'],
                treatment_plan_response=mental_health_case['treatment_plan_response']['current_record'],
                therapy_recommendation=mental_health_case['therapy_recommendation'][
                    'current_record'] if 'therapy_recommendation' in mental_health_case else None,
                follow_up=mental_health_case['follow_up']['current_record'],
                notes_for_doctor=mental_health_case['notes_for_doctor']['current_record'],
                client_education=mental_health_case['client_education'][
                    'current_record'] if 'client_education' in mental_health_case else None,
                care_assessment=mental_health_case[
                    'care_assessment'] if 'care_assessment' in mental_health_case else {},
                case_summary=mental_health_case['case_summary']['current_record'],
                assessment_forms=assessment_forms,
                date_closed=mental_health_case['date_closed'],
                referral_data=mental_health_case['referral_data'],
                medical_history=mental_health_case[
                    'medical_history'] if 'medical_history' in mental_health_case else {},
                allow_clinical_history_access=mental_health_case[
                    'allow_clinical_history_access'] if 'allow_clinical_history_access' in mental_health_case else False,
                s3_url=mental_health_case['patients_prescription_view'][-1][
                    's3_url'] if 'patients_prescription_view' in mental_health_case else ''
            )

            if not mental_health_case:
                # return None, 'No case details found for given data'
                return None
            return dict(
                msg='Success',
                data=latest_case_data
            )

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(e)
            # return None, f'Error occurred as {str(e)} while getting case details'
            return None

    def get_mental_health_case_details(self, data: GetMentalHealthCaseDetails):
        try:
            # self.is_valid_patient_id(patient_id=data.patient_id)
            valid_patient = self.get_patient_details(patientid=data.patient_id)
            if valid_patient is None:
                raise Exception(f'Patient does not exists with patient ID: {data.patient_id}')

            self.is_valid_case_id(patient_id=data.patient_id, case_id=data.case_id)

            existing_case_type = self.get_mental_health_case_type(patient_id=data.patient_id, case_id=data.case_id)
            if existing_case_type is None:
                return dict(
                    msg='Success',
                    case_type='New',
                    data=[]
                )
                # raise Exception('The case id provided is not a mental health case id')
            else:
                mental_health_case = {}
                if existing_case_type == 'therapist_prescriptions':
                    mental_health_case = self.get_therapy_case_details(
                        data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

                if existing_case_type == 'psychiatrist_prescriptions':
                    mental_health_case = self.get_psychiatry_case_details(
                        data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

                if existing_case_type == 'medical_health_prescriptions':
                    mental_health_case = self.get_medical_health_case_details(
                        data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

                if not mental_health_case:
                    # return None, 'No case details found for given data'
                    return None

                case_type = ''
                if existing_case_type == 'psychiatrist_prescriptions':
                    case_type = 'Psychiatry'

                if existing_case_type == 'therapist_prescriptions':
                    case_type = 'Therapy'

                if existing_case_type == 'medical_health_prescriptions':
                    case_type = 'Medical Health'

                return dict(
                    msg='Success',
                    case_type=case_type,
                    data=mental_health_case['data']
                )

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            return None

    def get_mental_health_case_history(self, data: GetMentalHealthCaseDetails):
        try:
            # self.is_valid_patient_id(patient_id=data.patient_id)
            valid_patient = self.get_patient_details(patientid=data.patient_id)
            if valid_patient is None:
                raise Exception(f'Patient does not exists with patient ID: {data.patient_id}')

            self.is_valid_case_id(patient_id=data.patient_id, case_id=data.case_id)

            existing_case_type = self.get_mental_health_case_type(patient_id=data.patient_id, case_id=data.case_id)
            if existing_case_type is None:
                return "No data"

            mental_health_case = {}
            if existing_case_type == 'therapist_prescriptions':
                mental_health_case = self.get_updated_therapy_case_record(patient_id=data.patient_id,
                                                                          case_id=data.case_id)

            if existing_case_type == 'psychiatrist_prescriptions':
                mental_health_case = self.get_updated_psychiatry_case_record(patient_id=data.patient_id,
                                                                             case_id=data.case_id)

            if existing_case_type == 'medical_health_prescriptions':
                mental_health_case = self.get_updated_medical_health_case_record(patient_id=data.patient_id,
                                                                                 case_id=data.case_id)

            if not mental_health_case:
                raise Exception('No case details found for given data')

            case_type = ''
            if existing_case_type == 'psychiatrist_prescriptions':
                case_type = 'Psychiatry'

            if existing_case_type == 'therapist_prescriptions':
                case_type = 'Therapy'

            if existing_case_type == 'medical_health_prescriptions':
                case_type = 'Medical Health'

            doctor_ctrl = DoctorController(db=self.db, mongo=self.mongo)

            doctor_details = self.mongo_db['Appointments'].find_one({"$and": [
                {"patient_id": str(data.patient_id)},
                {"caseid": str(data.case_id)},
                {"case_open": True}
            ]}, sort=[('appointment_slot', -1)])

            doctor_specialization_data = doctor_ctrl.get_specialization_field_of_doctor(
                doctor_id=doctor_details['doctorid'])

            return dict(
                msg='Success',
                session_no=mental_health_case['session_no'],
                case_type=case_type,
                doctor_specialization=doctor_specialization_data.specialization if doctor_specialization_data is not None else None,
                doctor_specialization_field=doctor_specialization_data.specialization_field if doctor_specialization_data is not None else None,
                data=mental_health_case
            )

        except Exception as e:
            loggers['logger5'].error(f"status_code=409, error occurred as : {str(e)}")
            raise HTTPException(status_code=409, detail=f'Error occurred as {str(e)} while getting case details')

    def get_doctors_all_cases(self, data: GetDoctorCases, logged_in_user: str, page_number: int = 1,
                              page_size: int = 15):
        try:
            # from datetime import datetime
            # t1 = datetime.now()

            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')

            # t2 = datetime.now()

            doctor_ctrl = DoctorController(db=self.db, mongo=self.mongo)
            doctor_specialization_data = doctor_ctrl.get_specialization_field_of_doctor(doctor_id=data.doctor_id)

            data_to_send = []

            # t3 = datetime.now()
            page_number = page_number
            page_size = page_size

            skip = (page_number - 1) * page_size

            if data.patient_name is not None and data.patient_name != "":
                resp_user_list = self.db.query(DBUser).filter(
                    and_(
                        or_(
                            DBUser.firstname.op('~*')(r'' + data.patient_name),
                            DBUser.lastname.op('~*')(r'' + data.patient_name)
                        ),
                        DBUser.is_deleted == False
                    )
                ).all()
                patient_filter = [user.userid for user in resp_user_list]

                resp_relative_list = self.db.query(DBRelatives).filter(or_(
                    DBRelatives.firstname.op('~*')(r'' + data.patient_name),
                    DBRelatives.lastname.op('~*')(r'' + data.patient_name))).all()
                patient_filter.extend([relative.relativeid for relative in resp_relative_list])

                match_query = {
                    '$and': [
                        {'patient_id': {'$in': patient_filter}},
                        {'doctorid': str(data.doctor_id)},
                        {'status.status': {'$in': ['Booked', 'Completed']}}
                    ]
                }

            else:
                match_query = {'$and': [
                    {"doctorid": str(data.doctor_id)},
                    {'status.status': {'$in': ['Booked', 'Completed']}}
                ]}

            pipeline = [
                {"$match": match_query},
                {"$sort": {"_id": -1}},
                {"$group": {
                    "_id": "$caseid",
                    "appointment_details": {"$first": "$$ROOT"},
                    "latest_appointment": {"$max": "$appointment_slot"}
                }},
                {"$sort": {"latest_appointment": -1}},
                {"$skip": skip},
                {"$limit": page_size}
            ]

            appointments = self.mongo_db['Appointments'].aggregate(pipeline)

            # t4 = datetime.now()

            for appointment in appointments:
                # print(appointment)
                case_id = appointment['_id']
                patient_id = appointment['appointment_details']['patient_id']
                case_type = self.get_case_type(patient_id=patient_id, case_id=case_id)
                case_open = appointment['appointment_details']['case_open']
                patient_details = self.get_patient_details(patientid=patient_id)

                care_taker_id = None
                care_taker_name = None
                ayoo_id = ''

                if patient_details:

                    is_minor = get_age_in_years(dob=str(patient_details.get('dob')))
                    if is_minor < 18:
                        care_taker_details: DBRelatives = self.db.query(DBRelatives).filter(
                            DBRelatives.caretaker_id == patient_id).first()
                        if care_taker_details is not None:
                            care_taker_id = care_taker_details.relativeid
                            care_taker_name = care_taker_details.firstname + ' ' + care_taker_details.lastname

                    ayoo_id = patient_details.get('ayoo_id', '')

                    patient_details = dict(
                        ayoo_id=ayoo_id,
                        name=patient_details['firstname'] + ' ' + patient_details['lastname'],
                        email=patient_details['email'],
                        gender=patient_details['gender'],
                        dob=patient_details['dob'],
                        family_doctor=patient_details['family_doctor'] if 'family_doctor' in patient_details else None,
                        care_taker_id=care_taker_id,
                        care_taker_name=care_taker_name
                    )
                data_to_send.append({
                    'patient_id': patient_id,
                    'ayoo_id': ayoo_id,
                    'case_type': case_type if case_type is not None else 'New',
                    'case_id': case_id,
                    'case_status': 'Active' if case_open else 'Closed',
                    'patient_details': patient_details,
                    'doctor_specialization': doctor_specialization_data.specialization if doctor_specialization_data is not None else None,
                    'doctor_specialization_field': doctor_specialization_data.specialization_field if doctor_specialization_data is not None else None
                })

            # t5 = datetime.now()
            # print("\n".join([str(t) for t in [t1, t2, t3, t4, t5]]))

            return {
                'msg': 'Case found',
                'case_details': data_to_send
            }
        except Exception as e:
            loggers['logger5'].error(f"status_code=409, error occurred as : {str(e)}")
            raise Exception(f'Error occurred as {str(e)} while finding cases for doctor.')

    def doctor_case_info(self, data: GetDoctorCaseInfo, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')

            doctor_ctrl = DoctorController(db=self.db, mongo=self.mongo)
            doctor_specialization_data = doctor_ctrl.get_specialization_field_of_doctor(doctor_id=data.doctor_id)

            appointment = self.mongo_db['Appointments'].find_one({'caseid': data.case_id, 'doctorid': data.doctor_id})

            data_to_send = None
            if appointment is not None:
                patient_id = appointment['patient_id']
                case_type = self.get_case_type(patient_id=patient_id, case_id=data.case_id)
                case_open = appointment['case_open']
                patient_details = self.get_patient_details(patientid=patient_id)
                ayoo_id = ''

                if patient_details:
                    care_taker_id = None
                    care_taker_name = None
                    is_minor = get_age_in_years(dob=str(patient_details.get('dob')))
                    if is_minor < 18:
                        care_taker_details: DBRelatives = self.db.query(DBRelatives).filter(
                            DBRelatives.caretaker_id == patient_id).first()
                        if care_taker_details is not None:
                            care_taker_id = care_taker_details.relativeid
                            care_taker_name = care_taker_details.firstname + ' ' + care_taker_details.lastname

                    ayoo_id = patient_details.get('ayoo_id', '')

                    patient_details = dict(
                        ayoo_id=ayoo_id,
                        name=patient_details['firstname'] + ' ' + patient_details['lastname'],
                        email=patient_details['email'],
                        gender=patient_details['gender'],
                        dob=patient_details['dob'],
                        family_doctor=patient_details['family_doctor'] if 'family_doctor' in patient_details else None,
                        care_taker_id=care_taker_id,
                        care_taker_name=care_taker_name,
                    )
                data_to_send = {
                    'patient_id': patient_id,
                    'ayoo_id': ayoo_id,
                    'case_type': case_type if case_type is not None else 'New',
                    'case_id': data.case_id,
                    'case_status': 'Active' if case_open else 'Closed',
                    'patient_details': patient_details,
                    'doctor_specialization': doctor_specialization_data.specialization if doctor_specialization_data is not None else None,
                    'doctor_specialization_field': doctor_specialization_data.specialization_field if doctor_specialization_data is not None else None
                }

            return {
                'msg': 'Case found' if data_to_send is not None else 'Case details not found',
                'case_details': data_to_send
            }
        except Exception as e:
            loggers['logger5'].error(f"status_code=409, error occurred as : {str(e)}")
            raise Exception(f'Error occurred as {str(e)} while finding case info for doctor.')

    # def get_doctors_all_cases(self, data: GetDoctorCases, logged_in_user: str):
    #     try:
    #         from datetime import datetime
    #
    #         t1 = datetime.now()
    #
    #         has_write_access = self.has_write_access(logged_in_user)
    #         if not has_write_access:
    #             raise Exception('User is not authorized for this action')
    #
    #         t2 = datetime.now()
    #
    #         doctor_ctrl = DoctorController(db=self.db, mongo=self.mongo)
    #         doctor_specialization_data = doctor_ctrl.get_specialization_field_of_doctor(doctor_id=data.doctor_id)
    #
    #         data_to_send = []
    #
    #         t3 = datetime.now()
    #
    #         active_appointments = self.mongo_db['Appointments'].distinct("caseid",
    #                                                                      {'doctorid': data.doctor_id}
    #                                                                      )
    #
    #         t4 = datetime.now()
    #
    #         for case_id in active_appointments:
    #             # get last appointment record from mongodb
    #             last_appointment_record = self.mongo_db['Appointments'].find_one({'caseid': case_id},
    #                                                                              sort=[('_id', -1)])
    #             # print("Queried Appointment for:", case_id, datetime.now())
    #             patient_id = last_appointment_record['patient_id']
    #             case_type = self.get_case_type(patient_id=patient_id, case_id=case_id)
    #             # print("Queried Case Type for: ", case_id, datetime.now())
    #             case_open = True
    #             if case_type is not None:
    #                 case_open = self.is_case_open(patient_id=patient_id, case_id=case_id)
    #
    #                 if case_open is None:
    #                     case_open = last_appointment_record['is_active']
    #
    #             patient_details = self.get_patient_details(patientid=patient_id)
    #             # print("Obtained patient details", datetime.now())
    #
    #             if patient_details:
    #                 patient_details = dict(
    #                     name=patient_details['firstname'] + ' ' + patient_details['lastname'],
    #                     email=patient_details['email'],
    #                     gender=patient_details['gender'],
    #                     dob=patient_details['dob'],
    #                     family_doctor=patient_details['family_doctor'] if 'family_doctor' in patient_details else None
    #                 )
    #             data_to_send.append({
    #                 'patient_id': patient_id,
    #                 'case_type': case_type if case_type is not None else 'New',
    #                 'case_id': case_id,
    #                 'case_status': 'Active' if case_open else 'Closed',
    #                 'patient_details': patient_details,
    #                 'doctor_specialization': doctor_specialization_data.specialization if doctor_specialization_data is not None else None,
    #                 'doctor_specialization_field': doctor_specialization_data.specialization_field if doctor_specialization_data is not None else None
    #             })
    #
    #
    #         t5 = datetime.now()
    #
    #         # print("\n".join([str(t) for t in [t1, t2, t3, t4, t5]]))
    #
    #         return {
    #             'msg': 'Case found',
    #             'case_details': data_to_send
    #         }
    #     except Exception as e:
    #         loggers['logger5'].error(f"status_code=409, error occurred as : {str(e)}")
    #         raise Exception(f'Error occurred as {str(e)} while finding cases for doctor.')

    ####################################################################################################
    # not used anywhere ->
    # get all cases including those which are referred and their appointment is not booked
    def get_doctors_all_cases1(self, data: GetDoctorCases, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')

            from ayoo_backend.api.view_controller import UserController
            from datetime import datetime

            t1 = datetime.now()

            ctrl = UserController(db=self.db, otp_generator=OTP_GENERATOR)

            data_to_send = []
            all_case_ids = []

            psychiatrist_cases = self.mongo_db['UserCollection'].find(
                {"psychiatrist_prescriptions.case_doctor": data.doctor_id})
            all_cases = list(psychiatrist_cases.clone())

            t2 = datetime.now()

            for case in all_cases:
                case_details = case['psychiatrist_prescriptions']
                for c in case_details:
                    if (c['case_doctor'] == data.doctor_id):
                        get_details = ctrl.get_user_details(userid=case['userid'], mongo=self.mongo)
                        patient_details = get_details
                        if get_details:
                            patient_details = dict(
                                name=get_details['firstname'] + ' ' + get_details['lastname'],
                                email=get_details['email'],
                                gender=get_details['gender'],
                                dob=get_details['birthdate'],
                                family_doctor=get_details['family_doctor'] if 'family_doctor' in get_details else None
                            )
                        data_to_send.append({
                            'patient_id': case['userid'],
                            'case_type': 'Psychiatry',
                            'case_id': c['case_id'],
                            'case_status': 'Active' if c['is_open'] else 'Closed',
                            'patient_details': patient_details
                        })
                        all_case_ids.append(c['case_id'])

            t3 = datetime.now()

            therapist_cases = self.mongo_db['UserCollection'].find(
                {"therapist_prescriptions.case_doctor": data.doctor_id})
            all_cases = list(therapist_cases.clone())
            for case in all_cases:
                case_details = case['therapist_prescriptions']
                for c in case_details:
                    if (c['case_doctor'] == data.doctor_id):
                        get_details = ctrl.get_user_details(userid=case['userid'], mongo=self.mongo)
                        patient_details = get_details
                        if get_details:
                            patient_details = dict(
                                name=get_details['firstname'] + ' ' + get_details['lastname'],
                                email=get_details['email'],
                                gender=get_details['gender'],
                                dob=get_details['birthdate'],
                                family_doctor=get_details['family_doctor'] if 'family_doctor' in get_details else None)
                        data_to_send.append({
                            'patient_id': case['userid'],
                            'case_type': 'Therapy',
                            'case_id': c['case_id'],
                            'case_status': 'Active' if c['is_open'] else 'Closed',
                            'patient_details': patient_details
                        })
                        all_case_ids.append(c['case_id'])

            t4 = datetime.now()

            medical_health_cases = self.mongo_db['UserCollection'].find(
                {"medical_health_prescriptions.case_doctor": data.doctor_id})
            all_cases = list(medical_health_cases.clone())
            for case in all_cases:
                case_details = case['medical_health_prescriptions']
                for c in case_details:
                    if (c['case_doctor'] == data.doctor_id):
                        get_details = ctrl.get_user_details(userid=case['userid'], mongo=self.mongo)
                        patient_details = get_details
                        if get_details:
                            patient_details = dict(
                                name=get_details['firstname'] + ' ' + get_details['lastname'],
                                email=get_details['email'],
                                gender=get_details['gender'],
                                dob=get_details['birthdate'],
                                family_doctor=get_details['family_doctor'] if 'family_doctor' in get_details else None
                            )
                        data_to_send.append({
                            'patient_id': case['userid'],
                            'case_type': 'Medical Health',
                            'case_id': c['case_id'],
                            'case_status': 'Active' if c['is_open'] else 'Closed',
                            'patient_details': patient_details
                        })
                        all_case_ids.append(c['case_id'])

            t5 = datetime.now()

            active_appointments = self.mongo_db['Appointments'].distinct("caseid",
                                                                         {'doctorid': data.doctor_id}
                                                                         )

            t6 = datetime.now()

            for case_id in active_appointments:
                if case_id not in all_case_ids:
                    patient = self.mongo_db['Appointments'].find_one(
                        {'doctorid': data.doctor_id, 'caseid': case_id}
                    )
                    get_details = ctrl.get_user_details(userid=patient['patient_id'], mongo=self.mongo)
                    patient_details = get_details
                    if get_details:
                        patient_details = dict(
                            name=get_details['firstname'] + ' ' + get_details['lastname'],
                            email=get_details['email'],
                            gender=get_details['gender'],
                            dob=get_details['birthdate'],
                            family_doctor=get_details['family_doctor'] if 'family_doctor' in get_details else None
                        )
                    data_to_send.append({
                        'patient_id': patient['patient_id'],
                        'case_type': 'New',
                        'case_id': case_id,
                        'case_status': 'New',
                        'patient_details': patient_details
                    })

            t7 = datetime.now()

            print("\n".join([str(t) for t in [t1, t2, t3, t4, t5, t6, t7]]))

            return {
                'msg': 'Case found',
                'case_details': data_to_send
            }



        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            raise Exception(f'Error occurred as {str(e)} while finding cases for doctor.')

    ####################################################################################################

    def update_referral_data(self, data: {}, refer_key: str, patient_id: str, case_id: str, prescription_type: str):
        try:
            self.mongo_db['UserCollection'].find_one_and_update(
                {"userid": patient_id,
                 f'{prescription_type}.case_id': case_id},
                {
                    "$push": {
                        f'{prescription_type}.$.referral_data.{refer_key}': data
                    }
                }
            )
        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise Exception(str(e))

    def close_case(self, patient_id: str, case_id: str, prescription_type: str):
        try:
            # Step 1: Deactivate Appointments
            self.mongo_db['Appointments'].update_many(dict(caseid=case_id),
                                                      {"$set": dict(case_open=False, is_active=False)})

            # Step 2: Close Case
            self.mongo_db['UserCollection'].find_one_and_update(
                {"userid": patient_id,
                 f'{prescription_type}.case_id': case_id},
                {
                    "$set": {
                        f'{prescription_type}.$.is_open': False,
                        f'{prescription_type}.$.date_closed': datetime.now(),
                        f'{prescription_type}.$.allow_clinical_history_access': False
                    }
                }
            )

            # Step 3: remove custom fees from db
            remove_custom_fees(patient_id, case_id)

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise Exception(str(e))

    def new_referral_function(self, data: ReferCase, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')

            # self.is_valid_patient_id(patient_id=data.patient_id)
            valid_patient = self.get_patient_details(patientid=data.patient_id)
            if valid_patient is None:
                raise Exception(f'Patient does not exists with patient ID: {data.patient_id}')

            self.is_valid_case_id(patient_id=data.patient_id, case_id=data.case_id)

            case_exists = self.get_open_case_id(patient_id=data.patient_id,
                                                doctor_id=data.referred_to)
            if case_exists is not None:
                raise Exception(f'A case with case id: {case_exists} already exists with doctor')

            referred_case_id = self.generate_case_id()

            valid_referred_to = self.get_doctor_details(doctor_id=data.referred_to)
            if valid_referred_to is None or valid_referred_to == {}:
                raise Exception('Invalid referred to ID')

            valid_referred_by = self.get_doctor_details(doctor_id=data.referred_by)
            if valid_referred_by is None or valid_referred_by == {}:
                raise Exception('Invalid referred by ID')

            # dict to store in the case_referral 'from' where the case has been referred
            referred_to_dict = {
                'referred_to': data.referred_to,
                'case_id': referred_case_id,
                # 'treatment_name': data.treatment_name,
                'referral_comments': data.referral_comments,
                'referral_date': datetime.now()
            }

            # dict to store in the case_referral 'to' where the case has been referred
            referred_from_dict = {
                'referred_by': data.referred_by,
                'case_id': data.case_id,
                # 'treatment_name': data.treatment_name,
                'referral_comments': data.referral_comments,
                'referral_date': datetime.now()
            }

            existing_case_type = self.get_mental_health_case_type(patient_id=data.patient_id, case_id=data.case_id)
            if existing_case_type is None:
                raise Exception('The case id provided is not a mental health case id')

            # 1/3 update the existing case with referred_to_dict
            self.update_referral_data(data=referred_to_dict, refer_key='referred_to', patient_id=data.patient_id,
                                      case_id=data.case_id,
                                      prescription_type=existing_case_type)

            if data.close_case is True:
                self.close_case(patient_id=data.patient_id, case_id=data.case_id, prescription_type=existing_case_type)

            # 2/3 insert new record for the referred case
            refer_case_type = ''
            if data.referral_for == 'Therapy':
                # check if there is existing data for the patient in UserCollection
                self.if_user_exists_with_therapy_case_record(patient_id=data.patient_id, case_id=referred_case_id,
                                                             doctor_id=data.referred_to)

                # # if mental_health_case key found, check if given case id exists
                self.if_therapist_case_data_exists(patient_id=data.patient_id, case_id=referred_case_id,
                                                   doctor_id=data.referred_to)

                refer_case_type = 'therapist_prescriptions'

            if data.referral_for == 'Psychiatry':
                # check if there is existing data for the patient in UserCollection
                self.if_user_exists_with_psychiatry_case_record(patient_id=data.patient_id, case_id=referred_case_id,
                                                                doctor_id=data.referred_to)

                # # if mental_health_case key found, check if given case id exists
                self.if_psychiatrist_case_data_exists(patient_id=data.patient_id, case_id=referred_case_id,
                                                      doctor_id=data.referred_to)

                refer_case_type = 'psychiatrist_prescriptions'

            self.update_referral_data(data=referred_from_dict, refer_key='referred_by', patient_id=data.patient_id,
                                      case_id=referred_case_id, prescription_type=refer_case_type)

            # 3/3 send appointment booking link in chat support
            chat_ctrl = ChatController(db=self.db, mongo=self.mongo)
            get_support_id, msg = chat_ctrl.get_ayoo_support_id()
            support_id = get_support_id[0]['admin_id']
            appointment_link = f"book_appointment/?case_id='{referred_case_id}'&doctor_id='{data.referred_to}'"
            resp, msg = chat_ctrl.save_chat_with_ayoo_support(chat_data=SaveChatModel(sender_id=support_id,
                                                                                      recipient_id=data.patient_id,
                                                                                      message=appointment_link))
            return {'msg': 'Case referred and appointment link sent', 'referred_case_id': referred_case_id,
                    'ayoo_support_chat_id': resp['chat_id'], 'appointment_link': appointment_link}
        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise Exception(f'{str(e)}')

    def refer_case(self, data: ReferCase, logged_in_user: str):
        try:
            if data.referred_to == "" or data.referred_to is None or data.referred_by == "" or data.referred_by is None:
                raise Exception('Refer to and Refer by fields are required')
            refer_resp = self.new_referral_function(data=data, logged_in_user=logged_in_user)
            return refer_resp
        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409, detail=f'Error occurred as {str(e)} while referring the case')

    def delete_referral_function(self, data: ReferCaseDelete, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')

            # self.is_valid_patient_id(patient_id=data.patient_id)
            valid_patient = self.get_patient_details(patientid=data.patient_id)
            if valid_patient is None:
                raise Exception(f'Patient does not exists with patient ID: {data.patient_id}')

            self.is_valid_case_id(patient_id=data.patient_id, case_id=data.case_id)
            self.is_valid_case_id(patient_id=data.patient_id, case_id=data.previous_referred_case_id)

            # case_exists = self.get_open_case_id(patient_id=data.patient_id,
            #                                     doctor_id=data.referred_to)

            # if case_exists is not None:
            #     raise Exception(f'A case with case id: {case_exists} already exists with doctor')
            existing_case_type = self.get_mental_health_case_type(patient_id=data.patient_id, case_id=data.case_id)
            if existing_case_type is None:
                raise Exception('The case id provided is not a mental health case id')

            # 1/3 delete old referral

            prev_referred_case_details = self.get_mental_health_case_history(
                GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.previous_referred_case_id))

            if prev_referred_case_details is None:
                raise Exception('No data found for previously referred case id')

            prev_ref_case_doctor = prev_referred_case_details['data']['case_doctor']

            psychiatrist_query = {"userid": data.patient_id,
                                  "psychiatrist_prescriptions.case_id": data.previous_referred_case_id}
            self.mongo_db['UserCollection'].find_one_and_update(psychiatrist_query,
                                                                {"$pull": {"psychiatrist_prescriptions": {
                                                                    "case_id": data.previous_referred_case_id}}})

            therapist_query = {"userid": data.patient_id,
                               "therapist_prescriptions.case_id": data.previous_referred_case_id}
            self.mongo_db['UserCollection'].find_one_and_update(therapist_query,
                                                                {"$pull": {"therapist_prescriptions": {
                                                                    "case_id": data.previous_referred_case_id}}})

            # 2/3 delete referral data from current case record i.e. from where the case was referred
            self.mongo_db['UserCollection'].find_one_and_update(
                {"userid": data.patient_id,
                 f'{existing_case_type}.case_id': data.case_id},
                {
                    "$pull": {
                        f'{existing_case_type}.$.referral_data.referred_to': {
                            'case_id': data.previous_referred_case_id
                        }
                    }
                }
            )

            # 3/3 delete referral link from user chat msg

            msg = f"book_appointment/?case_id='{data.previous_referred_case_id}'&doctor_id='{prev_ref_case_doctor}'"

            self.mongo_db['ChatMessages'].find_one_and_update(
                {"$and": [
                    {"$or": [
                        {"user_one_id": data.patient_id},
                        {"user_two_id": data.patient_id}
                    ]},
                    {"chat_meta.chat_type": "Ayoo Support"},
                    {"chat_open": True}
                ]},
                {
                    "$pull": {
                        "chat_history": {
                            "message": msg
                        }
                    }
                }
            )

            return {'msg': 'Case referral deleted'}

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise Exception(f'{str(e)}')

    def delete_referred_case(self, data: ReferCaseDelete, logged_in_user: str):
        try:
            delete_referral_resp = self.delete_referral_function(data=data, logged_in_user=logged_in_user)
            return delete_referral_resp

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409, detail=f'Error occurred as {str(e)} while deleting referral case')

    def check_if_referral_data_exists(self, data: ReferCaseUpdate):
        try:
            updated_case_data = self.get_mental_health_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))
            if updated_case_data is None:
                raise Exception(f'Could not find given case details of case id:{data.case_id}')

            all_referrals = updated_case_data['data']['referral_data']['referred_to']
            for ref in all_referrals:
                if ref['referred_to'] == data.referred_to and ref['case_id'] == data.previous_referred_case_id:
                    return True

            return False

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise Exception(str(e))

    def update_referral_comments(self, data: ReferCaseUpdate):
        try:
            existing_case_type = self.get_mental_health_case_type(patient_id=data.patient_id, case_id=data.case_id)
            if existing_case_type is None:
                raise Exception('The case id provided is not a mental health case id')

            self.mongo_db['UserCollection'].find_one_and_update(
                {"userid": data.patient_id,
                 f'{existing_case_type}.case_id': data.case_id},
                {
                    "$set": {
                        f'{existing_case_type}.$.referral_data.referred_to.$[elem].referral_comments': data.referral_comments
                    }
                },
                upsert=False,
                array_filters=[{"elem.referred_to": data.referred_to}]
            )

            existing_case_type = self.get_mental_health_case_type(patient_id=data.patient_id,
                                                                  case_id=data.previous_referred_case_id)
            if existing_case_type is None:
                raise Exception('The case id provided is not a mental health case id')

            self.mongo_db['UserCollection'].find_one_and_update(
                {"userid": data.patient_id,
                 f'{existing_case_type}.case_id': data.previous_referred_case_id},
                {
                    "$set": {
                        f'{existing_case_type}.$.referral_data.referred_by.$[elem].referral_comments': data.referral_comments
                    }
                },
                upsert=False,
                array_filters=[{"elem.referred_by": data.referred_by}]
            )

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise Exception(str(e))

    def update_referred_case(self, data: ReferCaseUpdate, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')

            # self.is_valid_patient_id(patient_id=data.patient_id)
            valid_patient = self.get_patient_details(patientid=data.patient_id)
            if valid_patient is None:
                raise Exception(f'Patient does not exists with patient ID: {data.patient_id}')

            self.is_valid_case_id(patient_id=data.patient_id, case_id=data.case_id)
            self.is_valid_case_id(patient_id=data.patient_id, case_id=data.previous_referred_case_id)

            case_exists = self.get_open_case_id(patient_id=data.patient_id,
                                                doctor_id=data.referred_to)

            if case_exists is not None:
                # check if comments are required to update
                refer_data_exists = self.check_if_referral_data_exists(data=data)
                if refer_data_exists is True:
                    self.update_referral_comments(data=data)
                    return {'msg': 'Referral comments updated', 'referred_case_id': data.previous_referred_case_id}

                # else raise exception
                else:
                    raise Exception(f'A case with case id: {case_exists} already exists with doctor')

            else:

                existing_case_type = self.get_mental_health_case_type(patient_id=data.patient_id, case_id=data.case_id)
                if existing_case_type is None:
                    raise Exception('The case id provided is not a mental health case id')

                # 1/2 delete old referral

                self.delete_referral_function(data=ReferCaseDelete(patient_id=data.patient_id, case_id=data.case_id,
                                                                   previous_referred_case_id=data.previous_referred_case_id),
                                              logged_in_user=logged_in_user)

                # 2/2 Create new referral
                new_referral = self.new_referral_function(data=ReferCase(patient_id=data.patient_id,
                                                                         case_id=data.case_id,
                                                                         referred_by=data.referred_by,
                                                                         referred_to=data.referred_to,
                                                                         referral_for=data.referral_for,
                                                                         referral_comments=data.referral_comments,
                                                                         close_case=data.close_case),
                                                          logged_in_user=logged_in_user)

                return new_referral

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409, detail=f'Error occurred as {str(e)} while updating the referral case')

    def update_care_assessment_mongo_key_by_patient(self, data: {}, care_assessment_key: str, patient_id: str,
                                                    case_id: str, prescription_type: str):
        try:
            self.mongo_db['UserCollection'].find_one_and_update(
                {"userid": patient_id,
                 f'{prescription_type}.case_id': case_id},
                {
                    "$push": {
                        f'{prescription_type}.$.care_assessment.{care_assessment_key}': data
                    }
                }
            )
        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise Exception(str(e))

    def care_assessment_patient_feedback(self, data: CareAssessmentPatientFeedback):
        try:

            self.is_valid_case_id(patient_id=data.patient_id, case_id=data.case_id)

            existing_case_type = self.get_mental_health_case_type(patient_id=data.patient_id, case_id=data.case_id)
            if existing_case_type is None:
                raise Exception('The case id provided is not a mental health case id')

            mental_health_case = {}
            if existing_case_type == 'therapist_prescriptions':
                mental_health_case = self.get_therapy_case_details(
                    data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            if existing_case_type == 'psychiatrist_prescriptions':
                mental_health_case = self.get_psychiatry_case_details(
                    data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            care_assessment_attributes = mental_health_case['data']['care_assessment'].keys() if 'care_assessment' in \
                                                                                                 mental_health_case[
                                                                                                     'data'] else {}

            for feedback in data.feedback:
                if feedback.attribute in care_assessment_attributes:
                    data_to_update = {
                        'feedback': feedback.feedback,
                        'date_recorded': datetime.now()
                    }
                    self.update_care_assessment_mongo_key_by_patient(data=data_to_update,
                                                                     care_assessment_key=feedback.attribute,
                                                                     patient_id=data.patient_id, case_id=data.case_id,
                                                                     prescription_type=existing_case_type)

            return {'msg': 'Data updated'}

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409,
                                detail=f'Error occurred as {str(e)} while updating the patient feedback')

    def get_latest_form_assessment_results(self, case_id: str, patient_id: str):
        try:

            existing_case_type = self.get_mental_health_case_type(patient_id=patient_id, case_id=case_id)
            if existing_case_type is None:
                raise Exception('The case id provided is not a mental health case id')

            if existing_case_type == 'therapist_prescriptions':
                self.is_valid_therapy_case_conditions(patient_id=patient_id, case_id=case_id)

            if existing_case_type == 'psychiatrist_prescriptions':
                self.is_valid_psychiatry_case_conditions(patient_id=patient_id, case_id=case_id)

            query = {
                "userid": patient_id,
                f"{existing_case_type}": {
                    "$elemMatch": {
                        "case_id": case_id
                    }
                }
            }

            # Retrieve the document matching the query
            document = self.mongo_db['UserCollection'].find_one(query)

            # Extract the latest form_responses from assessment_forms
            form_responses = []
            if document:
                for prescription in document[f"{existing_case_type}"]:
                    if prescription["case_id"] == case_id:
                        assessment_forms = prescription.get("assessment_forms", [])
                        for form in assessment_forms:
                            form_responses.append(dict(
                                form_id=form['form_id'],
                                form_name=form['form_name'],
                                form_responses=form['form_responses'][-1]
                            ))
            return form_responses

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise Exception(f'{str(e)}')

    def add_form_assessment_results(self, data: AssessmentFormsSubmit, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')

            existing_case_type = self.get_mental_health_case_type(patient_id=data.patient_id, case_id=data.case_id)
            if existing_case_type is None:
                raise Exception('The case id provided is not a mental health case id')

            if existing_case_type == 'therapist_prescriptions':
                self.is_valid_therapy_case_conditions(patient_id=data.patient_id, case_id=data.case_id)

            if existing_case_type == 'psychiatrist_prescriptions':
                self.is_valid_psychiatry_case_conditions(patient_id=data.patient_id, case_id=data.case_id)

            form_details = self.db.query(dbmodels.DBFormTable).filter_by(
                form_id=data.form_id).one_or_none()

            if form_details is None:
                raise Exception('Invalid form id')

            questions_and_answers = []
            total_form_points = 0

            for qna in data.q_n_a:
                question_details = self.db.query(dbmodels.DBQuestionTable).filter_by(
                    question_id=qna.question_id).one_or_none()
                if question_details is None:
                    raise Exception(f'Invalid question id: {qna.question_id}')

                if question_details.form_id != data.form_id:
                    raise Exception(f'Question id: {qna.question_id} does not exists in form id: {data.form_id}')

                dict_question_details = dict(
                    question_id=question_details.question_id,
                    form_id=question_details.form_id,
                    question_type=question_details.question_type,
                    question_text=question_details.question_text,
                    option1={"option_text": question_details.option1,
                             "option_point": question_details.option1_point},
                    option2={"option_text": question_details.option2,
                             "option_point": question_details.option2_point},
                    option3={"option_text": question_details.option3,
                             "option_point": question_details.option3_point},
                    option4={"option_text": question_details.option4,
                             "option_point": question_details.option4_point},
                    option5={"option_text": question_details.option5,
                             "option_point": question_details.option5_point},
                    option6={"option_text": question_details.option6,
                             "option_point": question_details.option6_point},
                    option7={"option_text": question_details.option7,
                             "option_point": question_details.option7_point},
                    option8={"option_text": question_details.option8,
                             "option_point": question_details.option8_point},
                    option9={"option_text": question_details.option9,
                             "option_point": question_details.option9_point},
                    option10={"option_text": question_details.option10,
                              "option_point": question_details.option10_point}
                )
                points = 0
                for ans in qna.answer:
                    for option_key, option_value in dict_question_details.items():
                        if isinstance(option_value, dict) and option_value.get("option_text") == ans.title():
                            option_point = option_value.get("option_point")
                            if isinstance(option_point, int):
                                points += option_point
                            break
                total_form_points += points

                if question_details.question_type == 'MULTISELECT':
                    questions_and_answers.append({
                        'question_id': qna.question_id,
                        'question': question_details.question_text,
                        'question_type': question_details.question_type,
                        'answer': qna.answer,
                        'answer_points': points
                    })

                if question_details.question_type == 'TEXT' or question_details.question_type == 'MCQ':
                    if len(qna.answer) > 1:
                        raise Exception(
                            f'Question id: {qna.question_id} should have one answer as it is of {question_details.question_type} type')

                    questions_and_answers.append({
                        'question_id': qna.question_id,
                        'question': question_details.question_text,
                        'question_type': question_details.question_type,
                        'answer': qna.answer[0],
                        'answer_points': points
                    })
            response_range = self.db.query(dbmodels.DBFormResponseTable).filter_by(form_id=data.form_id).one_or_none()
            response_text = None

            if response_range is not None:

                dict_response_range = dict(
                    response_id=response_range.response_id,
                    form_id=response_range.form_id,
                    range1=dict(
                        lower_limit=response_range.range1_lower_limit,
                        upper_limit=response_range.range1_upper_limit,
                        response_text=response_range.range1_response_text
                    ),
                    range2=dict(
                        lower_limit=response_range.range2_lower_limit,
                        upper_limit=response_range.range2_upper_limit,
                        response_text=response_range.range2_response_text
                    ),
                    range3=dict(
                        lower_limit=response_range.range3_lower_limit,
                        upper_limit=response_range.range3_upper_limit,
                        response_text=response_range.range3_response_text
                    ),
                    range4=dict(
                        lower_limit=response_range.range4_lower_limit,
                        upper_limit=response_range.range4_upper_limit,
                        response_text=response_range.range4_response_text
                    ),
                    range5=dict(
                        lower_limit=response_range.range5_lower_limit,
                        upper_limit=response_range.range5_upper_limit,
                        response_text=response_range.range5_response_text
                    ),
                    range6=dict(
                        lower_limit=response_range.range6_lower_limit,
                        upper_limit=response_range.range6_upper_limit,
                        response_text=response_range.range6_response_text
                    )
                )
                for range_key, range_value in dict_response_range.items():
                    if isinstance(range_value, dict):
                        lower_limit = range_value.get("lower_limit")
                        upper_limit = range_value.get("upper_limit")

                        if lower_limit <= total_form_points <= upper_limit:
                            response_text = range_value.get("response_text")
                            break

            check_if_form_exists = self.mongo_db['UserCollection'].find_one({
                "userid": data.patient_id,
                f"{existing_case_type}": {
                    "$elemMatch": {"case_id": data.case_id,
                                   "assessment_forms.form_id": data.form_id}}
            })

            form_response = dict(
                questions_and_answers=questions_and_answers,
                form_result=response_text,
                date_recorded=datetime.now()
            )

            if check_if_form_exists is None:
                data_to_push_in_db = dict(
                    form_id=data.form_id,
                    form_name=form_details.form_name,
                    form_responses=[form_response]
                )
                self.mongo_db['UserCollection'].find_one_and_update(
                    {"userid": data.patient_id,
                     f'{existing_case_type}.case_id': data.case_id},
                    {
                        "$push": {
                            f'{existing_case_type}.$.assessment_forms': data_to_push_in_db
                        }
                    }
                )
            else:
                self.mongo_db['UserCollection'].find_one_and_update(
                    {"userid": data.patient_id,
                     f'{existing_case_type}.case_id': data.case_id},
                    {
                        "$push": {
                            f'{existing_case_type}.$.assessment_forms.$[elem].form_responses': form_response
                        }
                    },
                    upsert=False,
                    array_filters=[{"elem.form_id": data.form_id}]
                )

            mental_health_case = self.get_mental_health_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id,
                                                case_id=data.case_id))

            return {'msg': 'Form data added',
                    'case_details': mental_health_case['data']}

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409,
                                detail=f'Error occurred as {str(e)} while adding mental health assessment form data')

    def allow_permission_for_clinical_history(self, data: ClinicalHistoryPermission, logged_in_user: str):
        try:

            existing_case_type = self.get_mental_health_case_type(patient_id=data.patient_id, case_id=data.case_id)
            if existing_case_type is None:
                raise Exception('The case id provided is not a mental health case id')

            if existing_case_type == 'therapist_prescriptions':
                self.is_valid_therapy_case_conditions(patient_id=data.patient_id, case_id=data.case_id)

            if existing_case_type == 'psychiatrist_prescriptions':
                self.is_valid_psychiatry_case_conditions(patient_id=data.patient_id, case_id=data.case_id)

            self.mongo_db['UserCollection'].find_one_and_update(
                {"userid": data.patient_id,
                 f'{existing_case_type}.case_id': data.case_id},
                {
                    "$set": {
                        f'{existing_case_type}.$.allow_clinical_history_access': data.allow_access
                    }
                }
            )

            mental_health_case = self.get_mental_health_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id,
                                                case_id=data.case_id))

            return {'msg': 'Clinical history access changed',
                    'case_details': mental_health_case['data']}

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409,
                                detail=f'Error occurred as {str(e)} while getting clinical history of the patient')

    def is_admin_or_patient(self, user_id: str):
        try:
            user_ctrl = UserController(db=self.db, otp_generator=None)
            admin_ctrl = AdminController(db=self.db, mongo=self.mongo)

            user_check = user_ctrl.get_user_by_id(userid=user_id)
            if user_check is not None:
                return True
            admin_check = admin_ctrl.get_admin_by_id(userid=user_id)
            if admin_check is not None:
                return True
            relative_check = user_ctrl.get_relative_details(userid=user_id)
            if relative_check is not None:
                return True

            return True
        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            err = str(e)
            return False

    def get_case_chats(self, case_id: str):
        try:
            chat_data = self.mongo_db['ChatMessages'].find({
                'chat_meta.case_id': case_id
            })
            chat_ids = []
            for chat in chat_data:
                chat_ids.append({'chat_id': chat['chat_id'], 'isActive': chat['chat_open']})

            return chat_ids
        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            raise Exception('Error while getting doctor details')

    def get_all_cases_of_user(self, patient_id: str):
        try:
            # has_access = self.is_admin_or_patient(user_id=logged_in_user)
            # if not has_access:
            #     return None, 'Logged in user is not authenticated for this action'

            from ayoo_backend.api.view_controller import UserController
            data_to_send = []
            active_appointments = self.mongo_db['Appointments'].distinct("caseid",
                                                                         {'patient_id': patient_id}
                                                                         )
            for case_id in active_appointments:
                # get last appointment record from mongodb - sort on date ascending
                last_appointment_record = self.mongo_db['Appointments'].find_one({'caseid': case_id},
                                                                                 sort=[('appointment_slot', -1)])
                # #logger.info("\n\n", last_appointment_record)
                case_type = self.get_case_type(patient_id=patient_id, case_id=case_id)

                # #logger.info(case_open)
                case_open = True
                if case_type is not None:
                    case_open = self.is_case_open(patient_id=patient_id, case_id=case_id)

                    if case_open is None:
                        case_open = last_appointment_record['is_active']

                doctor_id = last_appointment_record['doctorid']
                service_provider = self.get_doctor_details(doctor_id=doctor_id)
                chat_ids = self.get_case_chats(case_id=case_id)

                data_to_send.append({
                    'doctor_id': doctor_id,
                    'doctor_details': service_provider,
                    'case_id': case_id,
                    'is_open': case_open,
                    'consultation_date': last_appointment_record['appointment_slot'].date(),
                    'consultation_time': last_appointment_record['appointment_slot'].strftime("%I:%M %p"),
                    'case_type': case_type,
                    'chat_ids': chat_ids
                })

            return {
                'msg': 'Case found',
                'case_details': data_to_send
            }

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            err = str(e)
            raise Exception(f'{err}')

    # get user cases with referred cases, i.e. this api includes those case details also, which are referred but no appointment has been booked till now.
    def get_all_cases_of_user_including_referrals(self, user_id: str):
        try:

            user_data = self.mongo_db['UserCollection'].find_one({"userid": user_id})

            all_cases = []
            data_to_send = []

            if user_data is not None:
                if 'psychiatrist_prescriptions' in user_data:
                    prescription_data = user_data['psychiatrist_prescriptions']
                    for ele in prescription_data:
                        doctor_id = ele['case_doctor']
                        if ele['case_id'] not in all_cases:
                            service_provider = self.get_doctor_details(doctor_id=doctor_id)
                            chat_ids = self.get_case_chats(case_id=ele['case_id'])

                            data_to_send.append({
                                'doctor_id': doctor_id,
                                'doctor_details': service_provider,
                                'case_id': ele['case_id'],
                                'is_open': ele['is_open'],
                                'date_open': ele['date_open'],
                                'date_closed': ele['date_closed'],
                                'case_type': 'Psychiatry',
                                'referrals': ele['referral_data'],
                                'case_summary': ele['case_summary']['current_record'],
                                'chat_ids': chat_ids
                            })
                            all_cases.append(ele['case_id'])

                if 'therapist_prescriptions' in user_data:
                    prescription_data = user_data['therapist_prescriptions']
                    for ele in prescription_data:
                        doctor_id = ele['case_doctor']
                        if ele['case_id'] not in all_cases:
                            service_provider = self.get_doctor_details(doctor_id=doctor_id)
                            # chat_ids = self.get_case_chats(case_id=ele['case_id'])

                            data_to_send.append({
                                'doctor_id': doctor_id,
                                'doctor_details': service_provider,
                                'case_id': ele['case_id'],
                                'is_open': ele['is_open'],
                                'date_open': ele['date_open'],
                                'date_closed': ele['date_closed'],
                                'case_type': 'Therapy',
                                'referrals': ele['referral_data'],
                                'case_summary': ele['case_summary']['current_record']
                                # 'chat_ids': chat_ids

                            })
                            all_cases.append(ele['case_id'])

                if 'medical_health_prescriptions' in user_data:
                    prescription_data = user_data['psychiatrist_prescriptions']
                    for ele in prescription_data:
                        doctor_id = ele['case_doctor']
                        if ele['case_id'] not in all_cases:
                            service_provider = self.get_doctor_details(doctor_id=doctor_id)
                            # chat_ids = self.get_case_chats(case_id=ele['case_id'])

                            data_to_send.append({
                                'doctor_id': doctor_id,
                                'doctor_details': service_provider,
                                'case_id': ele['case_id'],
                                'is_open': ele['is_open'],
                                'date_open': ele['date_open'],
                                'date_closed': ele['date_closed'],
                                'case_type': 'Psychiatry',
                                'referrals': ele['referral_data'],
                                'case_summary': ele['case_summary']['current_record']
                                # 'chat_ids': chat_ids
                            })
                            all_cases.append(ele['case_id'])

            active_appointments = self.mongo_db['Appointments'].distinct("caseid",
                                                                         {'patient_id': user_id}
                                                                         )
            for case_id in active_appointments:
                if case_id not in all_cases:
                    appointment_cases = self.mongo_db['Appointments'].find_one(
                        {'patient_id': user_id, 'caseid': case_id}
                    )
                    if appointment_cases:
                        doctor_id = appointment_cases['doctorid']
                        service_provider = self.get_doctor_details(doctor_id=doctor_id)
                        # chat_ids = self.get_case_chats(case_id=appointment_cases['caseid'])

                        data_to_send.append({
                            'doctor_id': doctor_id,
                            'doctor_details': service_provider,
                            'case_id': appointment_cases['caseid'],
                            'is_open': appointment_cases['is_active'],
                            'date_open': appointment_cases['appointment_slot'],
                            'date_closed': None,
                            'case_type': 'New',
                            'referrals': None,
                            'case_summary': None
                            # 'chat_ids': chat_ids
                        })
                        all_cases.append(appointment_cases['caseid'])

            return data_to_send

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            err = str(e)
            # logger.info(str(e))
            raise Exception(f'{err}')

    def get_user_cases(self, logged_in_user: str, user_id: str):
        try:
            has_access = self.is_admin_or_patient(user_id=logged_in_user)
            if not has_access:
                return None, 'Logged in user is not authenticated for this action'

            user_cases = self.get_all_cases_of_user(patient_id=user_id)
            #
            # data_to_send = []
            #
            # for case in user_cases:
            #     data_to_send.append({
            #         'doctor_id': case['doctor_id'],
            #         'doctor_details': case['doctor_details'],
            #         'case_id': case['case_id'],
            #         'is_open': case['is_open'],
            #         # 'date_closed': case['date_closed'],
            #         'case_type': case['case_type'],
            #         # 'referrals': case['referrals'],
            #         # 'case_summary': case['case_summary'],
            #         'chat_ids': case['chat_ids']
            #     })
            #
            # if len(data_to_send) == 0:
            #     return data_to_send, 'No data found'

            return user_cases, 'Data found'

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            err = str(e)
            # logger.info(str(e))
            return None, f'Internal Error code {err} for getting case ids'

    def get_clinical_history(self, data: GetClinicalHistory, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')

            user = self.mongo_db['UserCollection'].find_one({"userid": data.patient_id})

            case_details = next(
                (case for case in user["therapist_prescriptions"] if
                 case["case_doctor"] == data.doctor_id and case['is_open'] == True),
                None
            )

            if case_details is None:
                case_details = next(
                    (case for case in user["psychiatrist_prescriptions"] if
                     case["case_doctor"] == data.doctor_id and case['is_open'] == True),
                    None
                )

            if case_details is None:
                raise Exception('No case found for given doctor and patient id')

            patients_cases = self.get_all_cases_of_user(patient_id=data.patient_id)
            data_to_send = []

            if case_details['allow_clinical_history_access'] == True:
                for case in patients_cases:
                    data_to_send.append({
                        'doctor_id': case['doctor_id'],
                        'doctor_details': case['doctor_details'],
                        'case_id': case['case_id'],
                        'is_open': case['is_open'],
                        'date_closed': case['date_closed'],
                        'case_type': case['case_type'],
                        'referrals': case['referrals'],
                        'case_summary': case['case_summary'],
                        'chat_ids': case['chat_ids']
                    })
            else:
                for case in patients_cases:
                    data_to_send.append({
                        'doctor_id': case['doctor_id'],
                        'doctor_details': case['doctor_details'],
                        'case_id': case['case_id'],
                        'is_open': case['is_open'],
                        'date_closed': case['date_closed'],
                        'case_type': case['case_type'],
                        'referrals': case['referrals'],
                        # 'case_summary': case['case_summary'],
                        'chat_ids': case['chat_ids']
                    })

            return {'msg': 'Clinical history fetched',
                    'case_details': data_to_send
                    }

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409,
                                detail=f'Error occurred as {str(e)} while getting clinical history of the patient')

    def get_reports_list(self, data: GetReportsList, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')

            get_users = self.mongo_db['UserCollection'].find()

            start_date = datetime.strptime(data.start_date + ' 12:00AM', '%Y-%m-%d %I:%M%p')
            end_date = datetime.strptime(data.end_date + ' 11:59PM', '%Y-%m-%d %I:%M%p')

            report_list = []

            for user in list(get_users.clone()):
                if 'psychiatrist_prescriptions' in user:
                    lab_tests = []

                    for case in user['psychiatrist_prescriptions']:
                        test_list_of_a_case = []

                        if case['is_open']:
                            if 'lab_tests' in case:
                                all_records = case['lab_tests']['all_records']
                                if len(all_records) > 0:
                                    for rec in all_records:
                                        date_recorded = rec['date_recorded']

                                        if start_date <= date_recorded and end_date >= date_recorded:
                                            for test in rec['test_details']:
                                                test_list_of_a_case.append({
                                                    'test_id': test['test_id'],
                                                    'test_name': test['test_name'],
                                                    'test_report_link': test['test_report_link'],
                                                    'test_date': test['test_date'],
                                                    'lab_test_prescribed_date': date_recorded
                                                })

                        if len(test_list_of_a_case) > 0:
                            lab_tests.append({
                                'case_id': case['case_id'],
                                'lab_tests': test_list_of_a_case
                            })
                    if len(lab_tests) > 0:
                        report_list.append({
                            'patient_id': user['userid'],
                            'test_details': lab_tests
                        })

            return {'msg': 'Reports found',
                    'report_list': report_list}
        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409,
                                detail=f'Error occurred as {str(e)} while getting reports for patients')

    def update_lab_test_link_in_psychiatrist_prescriptions(self, patient_id: str, case_id: str, test_id: str,
                                                           report_url: str = None, test_date: str = None):
        try:
            self.mongo_db['UserCollection'].update_one(
                {"userid": patient_id, "psychiatrist_prescriptions.case_id": case_id},
                {"$set": {
                    "psychiatrist_prescriptions.$[].lab_tests.all_records.$[].test_details.$[elem].test_report_link":
                        report_url,
                    "psychiatrist_prescriptions.$[].lab_tests.all_records.$[].test_details.$[elem].test_date":
                        test_date
                }
                },
                upsert=False,
                array_filters=[{"elem.test_id": test_id}]

            )
            get_last_record = self.get_updated_psychiatry_case_record(patient_id=patient_id,
                                                                      case_id=case_id)

            last_record = get_last_record['lab_tests']['all_records'][-1]

            self.mongo_db['UserCollection'].find_one_and_update(
                {"$and": [{"userid": patient_id}, {"psychiatrist_prescriptions.case_id": case_id}]},
                {
                    "$set": {
                        "psychiatrist_prescriptions.$[case].lab_tests.current_record.test_details": last_record[
                            'test_details']
                    }
                },
                upsert=False,
                array_filters=[{"case.case_id": case_id}]
            )
        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise Exception(str(e))

    def upload_report(self, data: UploadReport, logged_in_user: str, report_id: str = None):
        try:
            if report_id is None:
                # if report is already uploaded, send message to update it. (check only at the time of uploading, not at the time of updating)
                user_reports = self.mongo_db['PatientReports'].find_one({"$and": [
                    {'patient_id': data.patient_id},
                    {'case_id': data.case_id}
                ]})
                if user_reports is not None:
                    # raise Exception('Reports for given user and case not found')
                    report_exist = next(
                        (rep for rep in user_reports["lab_test_reports"] if rep["test_id"] == data.test_id),
                        None
                    )

                    if report_exist is not None:
                        raise Exception(
                            'Report for this test has already been uploaded. Use update function to modify the report.')

            # upload report to S3
            report_id = str(uuid.uuid4()) if report_id is None else report_id
            s3_instance = AWSS3Client()
            generated_url, msg = s3_instance.upload_report_to_s3(patient_id=data.patient_id, case_id=data.case_id,
                                                                 image_str=str(data.report_encoded),
                                                                 image_id=f'{str(data.case_id)}_{report_id}')
            if generated_url is None:
                raise Exception(msg)

            # add report info to DB after successful upload of test report on S3
            add_report_info = {
                'report_id': report_id,
                'test_id': data.test_id,  # will be assigned only if doctor has prescribed the test
                'test_name': data.test_name,
                # 'lab_name': data.lab_name,
                'test_date': data.test_date,
                'report_url': generated_url,
                'upload_date': datetime.now(),
                'uploaded_by': logged_in_user
            }

            patient_report_res = self.mongo_db['PatientReports'].find_one({
                "$and": [
                    {'patient_id': data.patient_id},
                    {'case_id': data.case_id}
                ]
            })

            if patient_report_res:
                self.mongo_db['PatientReports'].find_one_and_update({
                    "$and": [
                        {'patient_id': data.patient_id},
                        {'case_id': data.case_id}
                    ]
                }, {
                    "$push": {'lab_test_reports': add_report_info}
                })
            else:
                self.mongo_db['PatientReports'].insert_one({
                    'patient_id': data.patient_id,
                    'case_id': data.case_id,
                    'case_type': data.case_type,
                    'lab_test_reports': [add_report_info],
                    'documents': []
                })

            return add_report_info

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            raise Exception(f'{str(e)}')

    def upload_document(self, data: UploadDocument, logged_in_user: str, document_id: str = None):
        try:

            # upload extra document other than lab test reports to S3
            document_id = str(uuid.uuid4()) if document_id is None else document_id
            s3_instance = AWSS3Client()
            generated_url, msg = s3_instance.upload_document_to_s3(patient_id=data.patient_id, case_id=data.case_id,
                                                                   image_str=str(data.document_encoded),
                                                                   image_id=f'{str(data.case_id)}_{document_id}')
            if generated_url is None:
                raise Exception(msg)

            # add report info to DB after successful upload of test report on S3
            add_document_info = {
                'document_id': document_id,
                'document_name': data.document_name,
                'document_url': generated_url,
                'upload_date': datetime.now(),
                'uploaded_by': logged_in_user
            }

            patient_report_res = self.mongo_db['PatientReports'].find_one({
                "$and": [
                    {'patient_id': data.patient_id},
                    {'case_id': data.case_id}
                ]
            })

            if patient_report_res:
                self.mongo_db['PatientReports'].find_one_and_update({
                    "$and": [
                        {'patient_id': data.patient_id},
                        {'case_id': data.case_id}
                    ]
                }, {
                    "$push": {'documents': add_document_info}
                })
            else:
                self.mongo_db['PatientReports'].insert_one({
                    'patient_id': data.patient_id,
                    'case_id': data.case_id,
                    'case_type': data.case_type,
                    'lab_test_reports': [],
                    'documents': [add_document_info]
                })

            return add_document_info

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            raise Exception(f'{str(e)}')

    def get_test_name_of_psychiatry_case(self, data: UploadPrescribedReport):
        try:
            test_name = None
            if data.test_id is not None:
                user = self.mongo_db['UserCollection'].find_one({"userid": data.patient_id})
                if 'psychiatrist_prescriptions' not in user:
                    raise Exception('No record found')
                mental_health_case = next(
                    (case for case in user["psychiatrist_prescriptions"] if case["case_id"] == data.case_id),
                    None
                )

                if mental_health_case is None:
                    raise Exception('No case data found')

                lab_tests = mental_health_case['lab_tests']['all_records'] if len(
                    mental_health_case['lab_tests']['all_records']) > 0 else []

                if len(lab_tests) == 0:
                    raise Exception('Check the case and patient ID, no lab test data found')

                for elem in lab_tests:
                    test_elem = elem['test_details']
                    for e in test_elem:
                        if e['test_id'] == data.test_id:
                            test_name = e['test_name']
                            break

            if test_name is None:
                raise Exception('Invalid test ID')

            return test_name
        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise Exception(str(e))

    def upload_prescribed_report(self, data: UploadPrescribedReport, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                self.is_valid_patient_id(patient_id=logged_in_user)

            # check if case id is valid
            self.is_valid_case_id(patient_id=data.patient_id, case_id=data.case_id)

            # check if psychiatry case is open
            case_open = self.is_psychiatry_case_open(patient_id=data.patient_id, case_id=data.case_id)
            if case_open is False:
                raise Exception('Case is not active')

            # get the test name for given test id
            test_name = self.get_test_name_of_psychiatry_case(data=data)

            add_report_info = self.upload_report(data=UploadReport(
                patient_id=data.patient_id,
                case_id=data.case_id,
                case_type='Psychiatry',
                test_id=data.test_id,
                test_name=test_name,
                # lab_name=data.lab_name,
                test_date=data.test_date,
                report_encoded=data.report_encoded
            ), logged_in_user=logged_in_user)

            # update link of test report in psychiatrist_prescriptions if tests are prescribed by doctor
            if data.test_id is not None:
                self.update_lab_test_link_in_psychiatrist_prescriptions(patient_id=data.patient_id,
                                                                        case_id=data.case_id, test_id=data.test_id,
                                                                        report_url=add_report_info['report_url'],
                                                                        test_date=data.test_date)

            return add_report_info

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409,
                                detail=f'Error occurred as {str(e)} while uploading reports of the patient')

    def upload_unprescribed_reports_and_documents(self, data: UploadExtraDocument, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                self.is_valid_patient_id(patient_id=logged_in_user)

            # check if case id is valid
            self.is_valid_case_id(patient_id=data.patient_id, case_id=data.case_id)

            # check if case is open
            is_open_case = self.check_existing_open_case_by_case_id(patient_id=data.patient_id, case_id=data.case_id)
            if not is_open_case:
                raise Exception('Case is not active')

            get_case_type = self.get_case_type(patient_id=data.patient_id, case_id=data.case_id)

            add_report_info = self.upload_document(data=UploadDocument(
                patient_id=data.patient_id,
                case_id=data.case_id,
                case_type=get_case_type,
                document_name=data.document_name,
                document_encoded=data.document_encoded
            ), logged_in_user=logged_in_user)

            return add_report_info

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409,
                                detail=f'Error occurred as {str(e)} while uploading documents of the patient')

    def delete_uploaded_report(self, data: DeleteReport, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                self.is_valid_patient_id(patient_id=logged_in_user)

            # check if case id is valid
            self.is_valid_case_id(patient_id=data.patient_id, case_id=data.case_id)

            # check if case is open
            case_open = self.is_psychiatry_case_open(patient_id=data.patient_id, case_id=data.case_id)
            if case_open is False:
                raise Exception('Case is not active')

            user_reports = self.mongo_db['PatientReports'].find_one({"$and": [
                {'patient_id': data.patient_id},
                {'case_id': data.case_id}
            ]})
            if user_reports is None:
                raise Exception('Reports for given user and case not found')

            # delete report from s3 if found
            doc_type = 'lab_test_reports'
            report = next(
                (rep for rep in user_reports["lab_test_reports"] if rep["report_id"] == data.report_id),
                None
            )

            if report is None:
                raise Exception('Invalid report ID')

            s3_report_url = report['report_url'].split('/')
            image_key = f'patient/{data.patient_id}/{data.case_id}/{doc_type}/{s3_report_url[-1]}'

            s3_instance = AWSS3Client()
            s3_instance.delete_object_from_s3(image_key=image_key)

            if 'test_id' in report and report['test_id'] is not None:
                # delete the url from psychiatrist_prescriptions of user collection
                self.update_lab_test_link_in_psychiatrist_prescriptions(patient_id=data.patient_id,
                                                                        case_id=data.case_id, test_id=report['test_id'],
                                                                        report_url=None, test_date=None)

            self.mongo_db['PatientReports'].find_one_and_update({
                "$and": [
                    {'patient_id': data.patient_id},
                    {'case_id': data.case_id}
                ]
            }, {
                "$pull": {'lab_test_reports': {'test_id': report['test_id']}}
            })

            return {
                'msg': 'Report Deleted'
            }

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise Exception(f'{str(e)}')

    def delete_uploaded_document(self, data: DeleteDocument, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                self.is_valid_patient_id(patient_id=logged_in_user)

            # check if case id is valid
            self.is_valid_case_id(patient_id=data.patient_id, case_id=data.case_id)

            # check if case is open
            case_open = self.is_psychiatry_case_open(patient_id=data.patient_id, case_id=data.case_id)
            if case_open is False:
                raise Exception('Case is not active')

            user_reports = self.mongo_db['PatientReports'].find_one({"$and": [
                {'patient_id': data.patient_id},
                {'case_id': data.case_id}
            ]})
            if user_reports is None:
                raise Exception('Reports for given user and case not found')

            # delete report from s3 if found
            doc_type = 'documents'
            doc = next(
                (rep for rep in user_reports["documents"] if rep["document_id"] == data.document_id),
                None
            )

            if doc is None:
                raise Exception('Invalid document ID')

            s3_report_url = doc['document_url'].split('/')
            image_key = f'patient/{data.patient_id}/{data.case_id}/{doc_type}/{s3_report_url[-1]}'

            s3_instance = AWSS3Client()
            s3_instance.delete_object_from_s3(image_key=image_key)

            self.mongo_db['PatientReports'].find_one_and_update({
                "$and": [
                    {'patient_id': data.patient_id},
                    {'case_id': data.case_id}
                ]
            }, {
                "$pull": {'documents': {'document_id': data.document_id}}
            })

            return {
                'msg': 'Document Deleted'
            }

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise Exception(f'{str(e)}')

    def update_prescribed_report(self, data: UpdatePrescribedReport, logged_in_user: str):
        try:

            self.delete_uploaded_report(data=DeleteReport(patient_id=data.patient_id,
                                                          case_id=data.case_id,
                                                          report_id=data.report_id), logged_in_user=logged_in_user)

            # get the test name for given test id
            test_name = self.get_test_name_of_psychiatry_case(data=UploadPrescribedReport(
                patient_id=data.patient_id,
                case_id=data.case_id,
                test_id=data.test_id,
                test_date=data.test_date,
                report_encoded=data.report_encoded
            ))

            add_report_info = self.upload_report(data=UploadReport(
                patient_id=data.patient_id,
                case_id=data.case_id,
                case_type='Psychiatry',
                test_id=data.test_id,
                test_name=test_name,
                # lab_name=data.lab_name,
                test_date=data.test_date,
                report_encoded=data.report_encoded
            ), logged_in_user=logged_in_user, report_id=data.report_id)

            # update link of test report in psychiatrist_prescriptions if tests are prescribed by doctor
            if data.test_id is not None:
                self.update_lab_test_link_in_psychiatrist_prescriptions(patient_id=data.patient_id,
                                                                        case_id=data.case_id, test_id=data.test_id,
                                                                        report_url=add_report_info['report_url'],
                                                                        test_date=data.test_date)

            return add_report_info

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409,
                                detail=f'Error occurred as {str(e)} while updating reports of the patient')

    def update_unprescribed_reports_and_documents(self, data: UpdateExtraDocument, logged_in_user: str):
        try:

            self.delete_uploaded_document(data=DeleteDocument(patient_id=data.patient_id,
                                                              case_id=data.case_id,
                                                              document_id=data.document_id),
                                          logged_in_user=logged_in_user)

            get_case_type = self.get_case_type(patient_id=data.patient_id, case_id=data.case_id)

            add_report_info = self.upload_document(data=UploadDocument(
                patient_id=data.patient_id,
                case_id=data.case_id,
                case_type=get_case_type,
                document_name=data.document_name,
                document_encoded=data.document_encoded
            ), logged_in_user=logged_in_user, document_id=data.document_id)

            return add_report_info

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409,
                                detail=f'Error occurred as {str(e)} while updating documents of the patient')

    def delete_report(self, data: DeleteReport, logged_in_user: str):
        try:
            delete_report = self.delete_uploaded_report(data=data, logged_in_user=logged_in_user)
            return delete_report

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409,
                                detail=f'Error occurred as {str(e)} while deleting report of the patient')

    def delete_document(self, data: DeleteDocument, logged_in_user: str):
        try:
            delete_document = self.delete_uploaded_document(data=data, logged_in_user=logged_in_user)
            return delete_document

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409,
                                detail=f'Error occurred as {str(e)} while deleting document of the patient')

    def get_all_reports_of_patient(self, data: GetPatientReports, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                self.is_valid_patient_id(patient_id=logged_in_user)

            user_reports = self.mongo_db['PatientReports'].find({'patient_id': data.patient_id})
            if user_reports is None:
                raise Exception('Reports for given user and case not found')

            reports = []
            for report in list(user_reports.clone()):
                # check if case is open
                case_open = self.check_existing_open_case_by_case_id(patient_id=data.patient_id,
                                                                     case_id=report['case_id'])
                if 'lab_test_reports' in report and len(report['lab_test_reports']) > 0:
                    last_appointment_record = self.mongo_db['Appointments'].find_one(
                        {'caseid': report['case_id'], 'patient_id': data.patient_id},
                        sort=[('appointment_slot', -1)])
                    doctor_id = last_appointment_record['doctorid']
                    service_provider = self.get_doctor_details(doctor_id=doctor_id)
                    reports.append({
                        'case_id': report['case_id'],
                        'case_type': report['case_type'],
                        'case_open': case_open,
                        'case_doctor': doctor_id,
                        'doctor_name': service_provider['doctor_firstname'] + ' ' + service_provider['doctor_lastname'],
                        # 'doctor_details': service_provider,
                        'lab_test_reports': report['lab_test_reports'] if 'lab_test_reports' in report else []
                        # 'documents': report['documents'] if 'documents' in report else [],
                    }
                    )

            return {
                'msg': 'Reports Found',
                'patient_id': data.patient_id,
                'report_details': reports
            }

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409,
                                detail=f'Error occurred as {str(e)} while getting reports of the patient')

    def get_all_documents_of_patient(self, data: GetPatientReports, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                self.is_valid_patient_id(patient_id=logged_in_user)

            user_reports = self.mongo_db['PatientReports'].find({'patient_id': data.patient_id})
            if user_reports is None:
                raise Exception('Reports for given user and case not found')

            reports = []
            for report in list(user_reports.clone()):
                # check if case is open
                case_open = self.check_existing_open_case_by_case_id(patient_id=data.patient_id,
                                                                     case_id=report['case_id'])

                if 'documents' in report and len(report['documents']) > 0:
                    last_appointment_record = self.mongo_db['Appointments'].find_one(
                        {'caseid': report['case_id'], 'patient_id': data.patient_id},
                        sort=[('appointment_slot', -1)])
                    doctor_id = last_appointment_record['doctorid']
                    service_provider = self.get_doctor_details(doctor_id=doctor_id)
                    reports.append({
                        'case_id': report['case_id'],
                        'case_type': report['case_type'],
                        'case_open': case_open,
                        'case_doctor': doctor_id,
                        'doctor_name': service_provider['doctor_firstname'] + ' ' + service_provider['doctor_lastname'],
                        # 'doctor_details': service_provider,
                        # 'lab_test_reports': report['lab_test_reports'] if 'lab_test_reports' in report else []
                        'documents': report['documents'] if 'documents' in report else []
                    }
                    )

            return {
                'msg': 'Reports Found',
                'patient_id': data.patient_id,
                'report_details': reports
            }

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409,
                                detail=f'Error occurred as {str(e)} while getting documents of the patient')

    def get_patient_report_list_prescribed_by_doctor(self, logged_in_user: str):
        try:
            patient_id = logged_in_user
            self.is_valid_patient_id(patient_id=patient_id)
            user = self.mongo_db['UserCollection'].find_one(dict(userid=patient_id))
            if user is None:
                raise Exception('No case and lab test data found for this user')
            lab_tests = []

            if 'psychiatrist_prescriptions' in user:
                for case in user['psychiatrist_prescriptions']:
                    if case['is_open']:
                        test_list_of_a_case = [
                            {
                                'test_id': test['test_id'],
                                'test_name': test['test_name'],
                                'test_report_link': test['test_report_link'],
                                'test_date': test['test_date'],
                                'lab_test_prescribed_date': rec['date_recorded']
                            }
                            for rec in case.get('lab_tests', {}).get('all_records', [])
                            for test in rec['test_details']
                        ]

                        service_provider = self.get_doctor_details(doctor_id=case['case_doctor'])
                        last_appointment_record = self.mongo_db['Appointments'].find_one(
                            {'caseid': case['case_id'], 'doctorid': case['case_doctor']},
                            sort=[('appointment_slot', -1)])
                        # Add blank lists for psyhiatry cases, so that the function gives a complete list of user cases (include case only if appointment has been booked for a case)
                        if last_appointment_record is not None:
                            lab_tests.append({
                                'case_id': case['case_id'],
                                'case_type': 'Psychiatry',
                                'case_doctor': case['case_doctor'],
                                'doctor_details': service_provider,
                                'consultation_date': last_appointment_record['appointment_slot'].date(),
                                'consultation_time': last_appointment_record['appointment_slot'].strftime("%I:%M %p"),
                                'lab_tests': test_list_of_a_case
                            })

            if 'therapist_prescriptions' in user:
                for case in user['therapist_prescriptions']:
                    if case['is_open']:
                        service_provider = self.get_doctor_details(doctor_id=case['case_doctor'])
                        last_appointment_record = self.mongo_db['Appointments'].find_one(
                            {'caseid': case['case_id'], 'doctorid': case['case_doctor']},
                            sort=[('appointment_slot', -1)])

                        # Add blank lists for therapy cases, so that the function gives a complete list of user cases (include case only if appointment has been booked for a case)
                        if last_appointment_record is not None:
                            lab_tests.append({
                                'case_id': case['case_id'],
                                'case_type': 'Therapy',
                                'case_doctor': case['case_doctor'],
                                'doctor_details': service_provider,
                                'consultation_date': last_appointment_record['appointment_slot'].date(),
                                'consultation_time': last_appointment_record['appointment_slot'].strftime("%I:%M %p"),
                                'lab_tests': []
                            })

            return {
                'msg': 'Reports Found',
                'patient_id': patient_id,
                'documents_and_reports': lab_tests
            }

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            raise HTTPException(status_code=409,
                                detail=f'Error occurred as {str(e)} while getting reports of the patient')

    # Check if the user is attempting to update a record that was added today.
    # Users are not allowed to edit any record one day after its insertion.
    def check_if_update_allowed(self, patient_id: str, case_id: str, key_name: str):
        try:
            current_time = datetime.now()

            get_last_record = self.get_mental_health_case_details(
                GetMentalHealthCaseDetails(patient_id=patient_id, case_id=case_id))

            # #logger.info(get_last_record)
            if get_last_record is None:
                return False
            last_record = get_last_record['data']

            if key_name not in last_record:
                return False

            last_submit_time = last_record[key_name]['date_recorded']

            if (current_time.date() == last_submit_time.date()):
                return True

            if key_name == 'doctor_notes' or key_name == 'notes' or key_name == 'notes_for_doctor':
                if (current_time.date() <= last_submit_time.date() + timedelta(days=1)):
                    return True
                else:
                    return False

            # Check for recently submitted prescription
            check_if_prescription_is_submitted = self.get_mental_health_case_history(
                GetMentalHealthCaseDetails(patient_id=patient_id, case_id=case_id))
            if 'patients_prescription_view' in check_if_prescription_is_submitted['data']:

                last_prescription_data = check_if_prescription_is_submitted['data']['patients_prescription_view'][-1]

                last_submit_time = last_prescription_data['date_recorded']

                if (current_time.date() == last_submit_time.date()):
                    return True
                else:
                    raise Exception('Last prescription is finally submitted therefore no update allowed')

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise Exception(str(e))

    def get_doctor_list_for_referral(self, data: GetReferralDoctorsList):
        try:
            doctor_ctrl = DoctorController(db=self.db, mongo=self.mongo)
            doctor_list, msg = doctor_ctrl.get_all_specialist_doctors(
                SearchDoctorsBasedOnSpecialization(specialization=data.specialization))

            doctor_list_to_send = []

            if len(doctor_list) > 0:
                patient_ongoing_cases = self.get_all_cases_of_user_including_referrals(user_id=data.patient_id)

                # get the doctors with whom, either some case is going on or patient has already booked some appointment
                existing_doctor_list = []
                if patient_ongoing_cases:
                    for case_data in patient_ongoing_cases:
                        if case_data['is_open'] is True:
                            existing_doctor_list.append(case_data['doctor_id'])

                for doctor in doctor_list:
                    if doctor['doctorid'] not in existing_doctor_list:
                        doctor_list_to_send.append(doctor)

            return doctor_list_to_send
        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409, detail=f'Error occurred as {str(e)} while getting doctor list')

    def generate_prescription_pdf(self, data_to_export, doc_id):
        try:
            # logger.info('generating pdf')
            pg = PDF_Gen(db=self.db, mongo=self.mongo)
            # logger.info(data_to_export)
            c = pg.gen_pdf(dic=data_to_export, doc_id=doc_id)
            # logger.info(c)
            # #logger.info(data_to_export)
            # call the pdf gen file function and upload to s3
            # get the returned links to s3 and objet key
            # return the s3 link and objet key
            return dict(
                s3_object_key=c['s3_object_key'],
                s3_url=c['s3_url']
            )

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise Exception(str(e))

    def generate_patients_psychiatry_prescription(self, data: CaseElement1):
        try:
            get_psychiatry_case_history = self.get_mental_health_case_history(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))
            if get_psychiatry_case_history is None:
                raise Exception('No case data found, cannot submit prescription')

            date_now = datetime.now()
            time_threshold = timedelta(hours=48)

            patients_prescription_view = {}
            patients_prescription_view['patient_id'] = data.patient_id
            patients_prescription_view['case_id'] = data.case_id
            patients_prescription_view['case_type'] = "psychiatry"
            patients_prescription_view['appointment_id'] = data.appointment_id
            patients_prescription_view['appointment_type'] = data.appointment_type
            patients_prescription_view['appointment_slot'] = data.appointment_slot

            patients_prescription_view['preview'] = data.preview

            patient_details = self.get_patient_details(patientid=data.patient_id)
            patients_prescription_view['patient_details'] = patient_details

            doctor_details = self.get_doctor_details(doctor_id=get_psychiatry_case_history['data']['case_doctor'])
            patients_prescription_view['doctor_details'] = doctor_details

            prescription_count = 1
            if 'patients_prescription_view' in get_psychiatry_case_history['data']:
                prescription_count = len(get_psychiatry_case_history['data']['patients_prescription_view']) + 1

            assessment = get_psychiatry_case_history['data']['assessment']['current_record']
            if 'date_recorded' in assessment:
                date_recorded = assessment['date_recorded']

                time_difference = date_now - date_recorded
                if time_difference >= time_threshold:
                    # skip adding the record in patient's prescription
                    patients_prescription_view['assessment'] = None
                else:
                    # add the record in patient's prescription
                    patients_prescription_view['assessment'] = assessment
            else:
                patients_prescription_view['assessment'] = None

            # fetch Lab requests from userlocker collection for the current day
            user_locker = self.mongo_db["UserLocker"]
            start_of_today = datetime.combine(datetime.today(), datetime.min.time())
            end_of_today = datetime.combine(datetime.today(), datetime.max.time())
            pipeline = [
                {
                    "$match": {
                        "userid": data.patient_id,
                        "caseId": data.case_id,
                        "isRequested": True,
                        "fileTag": "578f236d-4533-4a48-b6d3-dbfbf8543823",
                        "requestDate": {"$gte": start_of_today, "$lt": end_of_today},
                    },
                },
                {
                    "$project": {
                        "test_name": "$fileName",
                        "_id": 0,
                    },
                },
                {
                    "$sort": {"requestDate": -1},
                },
            ]
            result = list(user_locker.aggregate(pipeline))
            if result:
                patients_prescription_view['lab_tests'] = {"test_details": result}
            else:
                patients_prescription_view['lab_tests'] = None

            medications = get_psychiatry_case_history['data']['medications']['current_record']

            if 'date_recorded' in medications:
                date_recorded = medications['date_recorded']

                time_difference = date_now - date_recorded
                if time_difference >= time_threshold:
                    # skip adding the record in patient's prescription
                    patients_prescription_view['medications'] = None
                else:
                    # add the record in patient's prescription
                    patients_prescription_view['medications'] = medications
            else:
                patients_prescription_view['medications'] = None

            treatment_plan = get_psychiatry_case_history['data']['treatment_plan']['current_record']
            if 'date_recorded' in treatment_plan:
                date_recorded = treatment_plan['date_recorded']

                time_difference = date_now - date_recorded
                if time_difference >= time_threshold:
                    # skip adding the record in patient's prescription
                    patients_prescription_view['recommendations'] = None
                else:
                    # add the record in patient's prescription
                    patients_prescription_view['recommendations'] = treatment_plan
            else:
                patients_prescription_view['recommendations'] = None

            follow_up = get_psychiatry_case_history['data']['follow_up']['current_record']
            if 'date_recorded' in follow_up:
                date_recorded = follow_up['date_recorded']

                time_difference = date_now - date_recorded
                if time_difference >= time_threshold:
                    # skip adding the record in patient's prescription
                    patients_prescription_view['follow_up'] = None
                else:
                    # add the record in patient's prescription
                    patients_prescription_view['follow_up'] = follow_up
            else:
                patients_prescription_view['follow_up'] = None

            if patients_prescription_view['assessment'] == None and patients_prescription_view['lab_tests'] == None and \
                    patients_prescription_view['medications'] == None and patients_prescription_view[
                'recommendations'] == None and patients_prescription_view['follow_up'] == None:
                raise Exception(
                    'Add at least one the fields: assessment, lab tests, medications, recommendations or follow up before submitting prescription')

            pres_id = "{}.pdf".format(datetime.now().strftime("%d_%m_%y"))
            if 'patients_prescription_view' in get_psychiatry_case_history['data']:

                if len(get_psychiatry_case_history['data']['patients_prescription_view']) > 0:
                    i = 0
                    m = 0
                    check = False
                    for res in get_psychiatry_case_history['data']['patients_prescription_view']:
                        if 'appointment_slot' in res:
                            if res['appointment_id'] == data.appointment_id:
                                last_patients_prescription = \
                                    get_psychiatry_case_history['data']['patients_prescription_view'][i]
                                check = True
                                m = i
                        i += 1
                    if check:

                        date_recorded = last_patients_prescription['date_recorded']
                        time_difference = date_now - date_recorded
                        try:
                            if 'preview' in last_patients_prescription:
                                if last_patients_prescription['preview'] == "true":
                                    patients_prescription_view['preview'] = "true"

                            else:
                                patients_prescription_view['preview'] = data.preview
                        except Exception as e:
                            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
                    else:
                        time_difference = timedelta(hours=25)

                    if time_difference <= timedelta(hours=24):
                        # update last prescription
                        # new key
                        pres_id_list = \
                            get_psychiatry_case_history['data']['patients_prescription_view'][m]['s3_object_key'][-1]
                        pres_id = pres_id_list.split("/")[-1]

                        self.mongo_db['UserCollection'].find_one_and_update(
                            {"userid": data.patient_id,
                             "psychiatrist_prescriptions.case_id": data.case_id},
                            {
                                "$pull": {
                                    "psychiatrist_prescriptions.$.patients_prescription_view": {
                                        "appointment_id": data.appointment_id
                                    }
                                }
                            }
                        )

                        prescription_count -= 1

            patients_prescription_view['prescription_serial'] = prescription_count

            patients_prescription_view['date_recorded'] = datetime.now()
            s3_data = self.generate_prescription_pdf(data_to_export=patients_prescription_view, doc_id=pres_id)

            try:
                if s3_data['s3_object_key'] not in patients_prescription_view['s3_object_key']:
                    patients_prescription_view['s3_object_key'].append(s3_data['s3_object_key'])
                    patients_prescription_view['s3_url'].append(s3_data['s3_url'])
            except:
                patients_prescription_view['s3_object_key'] = [s3_data['s3_object_key']]
                patients_prescription_view['s3_url'] = [s3_data['s3_url']]

            """patients_prescription_view['s3_object_key'] = s3_data['s3_object_key'] if s3_data[
                                                                                          's3_object_key'] != '' else '47782b41-8a7c-4a73-8098-746f8f1ddbd2'
            patients_prescription_view[
                's3_url'] = s3_data['s3_url'] if s3_data[
                                                     's3_url'] != '' else 'https://ayoo-web-bucket.s3.ap-south-1.amazonaws.com/patient/prescriptions/47782b41-8a7c-4a73-8098-746f8f1ddbd2.pdf'"""

            self.mongo_db['UserCollection'].find_one_and_update(
                {"userid": data.patient_id,
                 "psychiatrist_prescriptions.case_id": data.case_id},
                {
                    "$push": {
                        f'psychiatrist_prescriptions.$.patients_prescription_view': patients_prescription_view
                    }
                }
            )
        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise Exception(str(e))

    def generate_patients_therapy_prescription(self, data: CaseElement1):
        try:
            get_therapy_case_history = self.get_mental_health_case_history(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))
            if get_therapy_case_history is None:
                raise Exception('No case data found, cannot submit prescription')

            date_now = datetime.now()
            time_threshold = timedelta(hours=48)

            patients_prescription_view = {}

            patients_prescription_view['patient_id'] = data.patient_id
            patients_prescription_view['case_id'] = data.case_id
            patients_prescription_view['case_type'] = "therapy"
            patients_prescription_view['appointment_id'] = data.appointment_id
            patients_prescription_view['appointment_type'] = data.appointment_type
            patients_prescription_view['appointment_slot'] = data.appointment_slot

            patients_prescription_view['preview'] = data.preview
            patient_details = self.get_patient_details(patientid=data.patient_id)
            patients_prescription_view['patient_details'] = patient_details

            doctor_details = self.get_doctor_details(doctor_id=get_therapy_case_history['data']['case_doctor'])
            patients_prescription_view['doctor_details'] = doctor_details

            prescription_count = 1
            if 'patients_prescription_view' in get_therapy_case_history['data']:
                prescription_count = len(get_therapy_case_history['data']['patients_prescription_view']) + 1

            treatment_plan = get_therapy_case_history['data']['treatment_plan']['current_record']
            if 'date_recorded' in treatment_plan:
                date_recorded = treatment_plan['date_recorded']

                time_difference = date_now - date_recorded
                if time_difference >= time_threshold:
                    # skip adding the record in patient's prescription
                    patients_prescription_view['recommendations'] = None
                else:
                    # add the record in patient's prescription
                    patients_prescription_view['recommendations'] = treatment_plan
                    # treatment_plan_description = []
                    # for desc in treatment_plan['treatment_plan_data']:
                    #     treatment_plan_description.append(desc['description'])
                    #
                    # patients_prescription_view['recommendations'] = dict(descriptions=treatment_plan_description,
                    #                                                      date_recorded=treatment_plan['date_recorded'])
            else:
                patients_prescription_view['recommendations'] = None

            assessment = get_therapy_case_history['data']['assessment']['current_record']
            if 'date_recorded' in assessment:
                date_recorded = assessment['date_recorded']

                time_difference = date_now - date_recorded
                if time_difference >= time_threshold:
                    # skip adding the record in patient's prescription
                    patients_prescription_view['assessment'] = None
                else:
                    # add the record in patient's prescription
                    patients_prescription_view['assessment'] = assessment
            else:
                patients_prescription_view['assessment'] = None

            follow_up = get_therapy_case_history['data']['follow_up']['current_record']
            if 'date_recorded' in follow_up:
                date_recorded = follow_up['date_recorded']

                time_difference = date_now - date_recorded
                if time_difference >= time_threshold:
                    # skip adding the record in patient's prescription
                    patients_prescription_view['follow_up'] = None
                else:
                    # add the record in patient's prescription
                    patients_prescription_view['follow_up'] = follow_up
            else:
                patients_prescription_view['follow_up'] = None

            if patients_prescription_view['assessment'] == None and patients_prescription_view[
                'recommendations'] == None and patients_prescription_view['follow_up'] == None:
                raise Exception(
                    'Add at least one the fields: assessment, recommendations or follow up before submitting prescription')

            pres_id = "{}.pdf".format(datetime.now().strftime("%d_%m_%y"))
            if 'patients_prescription_view' in get_therapy_case_history['data']:
                if len(get_therapy_case_history['data']['patients_prescription_view']) > 0:
                    i = 0
                    m = 0
                    check = False
                    for res in get_therapy_case_history['data']['patients_prescription_view']:
                        if 'appointment_slot' in res:
                            if res['appointment_id'] == data.appointment_id:
                                last_patients_prescription = \
                                    get_therapy_case_history['data']['patients_prescription_view'][i]
                                check = True
                                m = i
                        i += 1
                    if check:
                        date_recorded = last_patients_prescription['date_recorded']
                        time_difference = date_now - date_recorded
                        try:
                            if 'preview' in last_patients_prescription:
                                if last_patients_prescription['preview'] == "true":
                                    patients_prescription_view['preview'] = "true"

                            else:
                                patients_prescription_view['preview'] = data.preview
                        except Exception as e:
                            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
                    else:
                        time_difference = timedelta(hours=25)

                    if time_difference <= timedelta(hours=24):
                        # update last prescription
                        # new key
                        pres_id_list = \
                            get_therapy_case_history['data']['patients_prescription_view'][-1]['s3_object_key'][-1]
                        pres_id = pres_id_list.split("/")[-1]

                        self.mongo_db['UserCollection'].find_one_and_update(
                            {"userid": data.patient_id,
                             "therapist_prescriptions.case_id": data.case_id},
                            {
                                "$pull": {
                                    "therapist_prescriptions.$.patients_prescription_view": {
                                        "appointment_id": data.appointment_id
                                    }
                                }
                            }
                        )

                        prescription_count -= 1

            patients_prescription_view['prescription_serial'] = prescription_count
            patients_prescription_view['date_recorded'] = datetime.now()

            s3_data = self.generate_prescription_pdf(data_to_export=patients_prescription_view, doc_id=pres_id)

            try:
                if s3_data['s3_object_key'] not in patients_prescription_view['s3_object_key']:
                    patients_prescription_view['s3_object_key'].append(s3_data['s3_object_key'])
                    patients_prescription_view['s3_url'].append(s3_data['s3_url'])
            except:
                patients_prescription_view['s3_object_key'] = [s3_data['s3_object_key']]
                patients_prescription_view['s3_url'] = [s3_data['s3_url']]

            self.mongo_db['UserCollection'].find_one_and_update(
                {"userid": data.patient_id,
                 "therapist_prescriptions.case_id": data.case_id},
                {
                    "$push": {
                        f'therapist_prescriptions.$.patients_prescription_view': patients_prescription_view
                    }
                }
            )

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise Exception(str(e))

    def generate_patients_medical_health_prescription(self, data: CaseElement1):
        try:
            get_medical_health_case_history = self.get_mental_health_case_history(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))
            if get_medical_health_case_history is None:
                raise Exception('No case data found, cannot submit prescription')

            date_now = datetime.now()
            time_threshold = timedelta(hours=48)

            patients_prescription_view = {}
            patients_prescription_view['patient_id'] = data.patient_id
            patients_prescription_view['case_id'] = data.case_id
            patients_prescription_view['case_type'] = "medical health"
            patients_prescription_view['appointment_id'] = data.appointment_id
            patients_prescription_view['appointment_type'] = data.appointment_type
            patients_prescription_view['appointment_slot'] = data.appointment_slot

            patients_prescription_view['preview'] = data.preview

            patient_details = self.get_patient_details(patientid=data.patient_id)
            patients_prescription_view['patient_details'] = patient_details

            doctor_details = self.get_doctor_details(doctor_id=get_medical_health_case_history['data']['case_doctor'])
            patients_prescription_view['doctor_details'] = doctor_details

            prescription_count = 1
            if 'patients_prescription_view' in get_medical_health_case_history['data']:
                prescription_count = len(get_medical_health_case_history['data']['patients_prescription_view']) + 1

            assessment = get_medical_health_case_history['data']['assessment']['current_record']
            if 'date_recorded' in assessment:
                date_recorded = assessment['date_recorded']

                time_difference = date_now - date_recorded
                if time_difference >= time_threshold:
                    # skip adding the record in patient's prescription
                    patients_prescription_view['assessment'] = None
                else:
                    # add the record in patient's prescription
                    patients_prescription_view['assessment'] = assessment
            else:
                patients_prescription_view['assessment'] = None

            # fetch Lab requests from userlocker collection for the current day
            user_locker = self.mongo_db["UserLocker"]
            start_of_today = datetime.combine(datetime.today(), datetime.min.time())
            end_of_today = datetime.combine(datetime.today(), datetime.max.time())
            pipeline = [
                {
                    "$match": {
                        "userid": data.patient_id,
                        "caseId": data.case_id,
                        "isRequested": True,
                        "fileTag": "578f236d-4533-4a48-b6d3-dbfbf8543823",
                        "requestDate": {"$gte": start_of_today, "$lt": end_of_today},
                    },
                },
                {
                    "$project": {
                        "test_name": "$fileName",
                        "_id": 0,
                    },
                },
                {
                    "$sort": {"requestDate": -1},
                },
            ]
            result = list(user_locker.aggregate(pipeline))
            if result:
                patients_prescription_view['lab_tests'] = {"test_details": result}
            else:
                patients_prescription_view['lab_tests'] = None

            medications = get_medical_health_case_history['data']['medications']['current_record']

            if 'date_recorded' in medications:
                date_recorded = medications['date_recorded']

                time_difference = date_now - date_recorded
                if time_difference >= time_threshold:
                    # skip adding the record in patient's prescription
                    patients_prescription_view['medications'] = None
                else:
                    # add the record in patient's prescription
                    patients_prescription_view['medications'] = medications
            else:
                patients_prescription_view['medications'] = None

            treatment_plan = get_medical_health_case_history['data']['work_up_plan']['current_record']
            if 'date_recorded' in treatment_plan:
                date_recorded = treatment_plan['date_recorded']

                time_difference = date_now - date_recorded
                if time_difference >= time_threshold:
                    # skip adding the record in patient's prescription
                    patients_prescription_view['recommendations'] = None
                else:
                    # add the record in patient's prescription
                    patients_prescription_view['recommendations'] = treatment_plan
            else:
                patients_prescription_view['recommendations'] = None

            follow_up = get_medical_health_case_history['data']['follow_up']['current_record']
            if 'date_recorded' in follow_up:
                date_recorded = follow_up['date_recorded']

                time_difference = date_now - date_recorded
                if time_difference >= time_threshold:
                    # skip adding the record in patient's prescription
                    patients_prescription_view['follow_up'] = None
                else:
                    # add the record in patient's prescription
                    patients_prescription_view['follow_up'] = follow_up
            else:
                patients_prescription_view['follow_up'] = None

            if patients_prescription_view['assessment'] == None and patients_prescription_view['lab_tests'] == None and \
                    patients_prescription_view['medications'] == None and patients_prescription_view[
                'recommendations'] == None and patients_prescription_view['follow_up'] == None:
                raise Exception(
                    'Add at least one the fields: assessment, lab tests, medications, recommendations or follow up before submitting prescription')

            pres_id = "{}.pdf".format(datetime.now().strftime("%d_%m_%y"))
            if 'patients_prescription_view' in get_medical_health_case_history['data']:

                if len(get_medical_health_case_history['data']['patients_prescription_view']) > 0:
                    i = 0
                    m = 0
                    check = False
                    for res in get_medical_health_case_history['data']['patients_prescription_view']:
                        if 'appointment_slot' in res:
                            if res['appointment_id'] == data.appointment_id:
                                last_patients_prescription = \
                                    get_medical_health_case_history['data']['patients_prescription_view'][i]
                                check = True
                                m = i
                        i += 1
                    if check:

                        date_recorded = last_patients_prescription['date_recorded']
                        time_difference = date_now - date_recorded
                        try:
                            if 'preview' in last_patients_prescription:
                                if last_patients_prescription['preview'] == "true":
                                    patients_prescription_view['preview'] = "true"

                            else:
                                patients_prescription_view['preview'] = data.preview
                        except Exception as e:
                            loggers['logger5'].error(f"status_code=409, error occurred as : {str(e)}")

                    else:
                        time_difference = timedelta(hours=25)

                    if time_difference <= timedelta(hours=24):
                        # update last prescription
                        # new key
                        pres_id_list = \
                            get_medical_health_case_history['data']['patients_prescription_view'][m]['s3_object_key'][
                                -1]
                        pres_id = pres_id_list.split("/")[-1]

                        self.mongo_db['UserCollection'].find_one_and_update(
                            {"userid": data.patient_id,
                             "medical_health_prescriptions.case_id": data.case_id},
                            {
                                "$pull": {
                                    "medical_health_prescriptions.$.patients_prescription_view": {
                                        "appointment_id": data.appointment_id
                                    }
                                }
                            }
                        )

                        prescription_count -= 1

            patients_prescription_view['prescription_serial'] = prescription_count

            patients_prescription_view['date_recorded'] = datetime.now()
            s3_data = self.generate_prescription_pdf(data_to_export=patients_prescription_view, doc_id=pres_id)

            try:
                if s3_data['s3_object_key'] not in patients_prescription_view['s3_object_key']:
                    patients_prescription_view['s3_object_key'].append(s3_data['s3_object_key'])
                    patients_prescription_view['s3_url'].append(s3_data['s3_url'])
            except:
                patients_prescription_view['s3_object_key'] = [s3_data['s3_object_key']]
                patients_prescription_view['s3_url'] = [s3_data['s3_url']]

            self.mongo_db['UserCollection'].find_one_and_update(
                {"userid": data.patient_id,
                 "medical_health_prescriptions.case_id": data.case_id},
                {
                    "$push": {
                        f'medical_health_prescriptions.$.patients_prescription_view': patients_prescription_view
                    }
                }
            )
        except Exception as e:
            loggers['logger5'].error(f"status_code=409, error occurred as : {str(e)}")
            raise Exception(str(e))

    def get_patients_prescriptions_old(self, patient_id: str, logged_in_user: str):
        try:
            s3_instance = AWSS3Client()
            valid_patient = self.get_patient_details(patientid=patient_id)
            if valid_patient is None:
                raise Exception(f'Patient does not exists with patient ID: {patient_id}')

            all_user_cases = self.get_all_cases_of_user(patient_id=patient_id)
            if all_user_cases is None:
                return dict(msg='No data found', case_details=[])
            data_to_return = []

            for case in all_user_cases['case_details']:
                doctor_name = case['doctor_details']['doctor_firstname'] + ' ' + case['doctor_details'][
                    'doctor_lastname']

                if case['case_type'] == 'Psychiatry':
                    mental_health_case = self.get_updated_psychiatry_case_record(patient_id=patient_id,
                                                                                 case_id=case['case_id'])
                    if 'patients_prescription_view' in mental_health_case:

                        for prescription in mental_health_case['patients_prescription_view']:
                            if 'appointment_slot' in prescription:
                                parsed_datetime = datetime.strptime(prescription['appointment_slot'],
                                                                    "%Y-%m-%dT%H:%M:%S")
                                formatted_date = parsed_datetime.strftime("%Y-%m-%d")
                                formatted_time = parsed_datetime.strftime("%I:%M %p")
                                consultation_date = formatted_date
                                consultation_time = formatted_time
                            else:
                                consultation_date = case['consultation_date']
                                consultation_time = case['consultation_time']
                            if 'preview' not in prescription or prescription['preview'] == "true":
                                data_to_return.append(
                                    dict(
                                        patient_id=patient_id,
                                        case_id=case['case_id'],
                                        case_type=case['case_type'],
                                        is_open=case['is_open'],
                                        consultation_date=consultation_date,
                                        consultation_time=consultation_time,
                                        # prescription=prescription,
                                        prescription_data=dict(
                                            prescription_file_key=str(prescription[
                                                                          's3_object_key'][
                                                                          -1]) if 's3_object_key' in prescription else '',
                                            prescription_url=s3_instance.get_presigned_url(str(
                                                prescription['s3_url'][-1])) if 's3_url' in prescription else ''),
                                        # doctor_id=case['doctor_id'],
                                        # doctor_details=case['doctor_details']
                                        doctor_name=doctor_name,
                                        preview=prescription['preview'] if 'preview' in prescription else "true",
                                        is_active=False
                                    ))
                        data_to_return[-1]['is_active'] = True

                if case['case_type'] == 'Medical Health':
                    mental_health_case = self.get_updated_medical_health_case_record(patient_id=patient_id,
                                                                                     case_id=case['case_id'])
                    if 'patients_prescription_view' in mental_health_case:

                        for prescription in mental_health_case['patients_prescription_view']:
                            if 'appointment_slot' in prescription:
                                parsed_datetime = datetime.strptime(prescription['appointment_slot'],
                                                                    "%Y-%m-%dT%H:%M:%S")
                                formatted_date = parsed_datetime.strftime("%Y-%m-%d")
                                formatted_time = parsed_datetime.strftime("%I:%M %p")
                                consultation_date = formatted_date
                                consultation_time = formatted_time
                            else:
                                consultation_date = case['consultation_date']
                                consultation_time = case['consultation_time']
                            if 'preview' not in prescription or prescription['preview'] == "true":
                                data_to_return.append(
                                    dict(
                                        patient_id=patient_id,
                                        case_id=case['case_id'],
                                        case_type=case['case_type'],
                                        is_open=case['is_open'],
                                        consultation_date=consultation_date,
                                        consultation_time=consultation_time,
                                        # prescription=prescription,
                                        prescription_data=dict(
                                            prescription_file_key=str(prescription[
                                                                          's3_object_key'][
                                                                          -1]) if 's3_object_key' in prescription else '',
                                            prescription_url=s3_instance.get_presigned_url(str(
                                                prescription['s3_url'][-1])) if 's3_url' in prescription else ''),
                                        # doctor_id=case['doctor_id'],
                                        # doctor_details=case['doctor_details']
                                        doctor_name=doctor_name,
                                        preview=prescription['preview'] if 'preview' in prescription else "true",
                                        is_active=False
                                    ))
                        data_to_return[-1]['is_active'] = True

                if case['case_type'] == 'Therapy':
                    mental_health_case = self.get_updated_therapy_case_record(patient_id=patient_id,
                                                                              case_id=case['case_id'])
                    if 'patients_prescription_view' in mental_health_case:

                        for prescription in mental_health_case['patients_prescription_view']:
                            if 'appointment_slot' in prescription:
                                parsed_datetime = datetime.strptime(prescription['appointment_slot'],
                                                                    "%Y-%m-%dT%H:%M:%S")
                                formatted_date = parsed_datetime.strftime("%Y-%m-%d")
                                formatted_time = parsed_datetime.strftime("%I:%M %p")
                                consultation_date = formatted_date
                                consultation_time = formatted_time
                            else:
                                consultation_date = case['consultation_date']
                                consultation_time = case['consultation_time']
                            if 'preview' not in prescription or prescription['preview'] == "true":
                                data_to_return.append(
                                    dict(
                                        patient_id=patient_id,
                                        case_id=case['case_id'],
                                        case_type=case['case_type'],
                                        is_open=case['is_open'],
                                        consultation_date=consultation_date,
                                        consultation_time=consultation_time,
                                        # prescription=prescription,
                                        prescription_data=dict(
                                            prescription_file_key=str(prescription[
                                                                          's3_object_key'][
                                                                          -1]) if 's3_object_key' in prescription else '',
                                            prescription_url=s3_instance.get_presigned_url(str(
                                                prescription['s3_url'][-1])) if 's3_url' in prescription else ''),
                                        # doctor_id=case['doctor_id'],
                                        # doctor_details=case['doctor_details']
                                        doctor_name=doctor_name,
                                        preview=prescription['preview'] if 'preview' in prescription else "true",
                                        is_active=False
                                    ))
                        data_to_return[-1]['is_active'] = True

            return data_to_return

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise Exception(f'Error occurred as {str(e)} while getting prescription for patient case')

    def get_patients_prescriptions(self, patient_id: str, logged_in_user: str):
        try:
            s3_instance = AWSS3Client()
            valid_patient = self.get_patient_details(patientid=patient_id)
            if valid_patient is None:
                raise Exception(f'Patient does not exists with patient ID: {patient_id}')

            # all_user_cases = self.get_all_cases_of_user(patient_id=patient_id)
            # if all_user_cases is None:
            #     return dict(msg='No data found', case_details=[])
            data_to_return = []

            all_case_sheets = list(self.mongo_db['CaseSheet'].find(
                {'patient_id': patient_id},
                sort=[('appointment_slot', -1), ('session_no', -1), ('prescription_count', -1)]).clone())
            # case_ids = []
            case_ids = set()

            for case_sheet in all_case_sheets:
                latest_prescription = False
                if case_sheet.get('case_id') not in case_ids:
                    latest_prescription = True

                case_ids.add(case_sheet.get('case_id'))

                parsed_datetime = case_sheet['appointment_slot']
                formatted_date = parsed_datetime.strftime("%Y-%m-%d")
                formatted_time = parsed_datetime.strftime("%I:%M %p")
                consultation_date = formatted_date
                consultation_time = formatted_time

                case_type = case_sheet.get('prescription_type')
                if case_type == 'MedicalHealth':
                    case_type = 'Medical Health'

                is_active = True
                medications = case_sheet.get('medication', [])
                for medication in medications:
                    if medication.get('end_date') not in ['', None]:
                        is_active = datetime.strptime(medication['end_date'],
                                                      '%Y-%m-%d').date() >= datetime.today().date()
                        if is_active is False:
                            break

                if case_sheet.get('prescription_s3_object_url') not in ["", None]:
                    data_to_return.append(
                        {
                            "patient_id": case_sheet.get('patient_id'),
                            "case_id": case_sheet.get('case_id'),
                            "case_type": case_type,
                            "is_open": case_sheet.get('is_open'),
                            "consultation_date": consultation_date,
                            "consultation_time": consultation_time,
                            "prescription_data": {
                                "prescription_file_key":
                                    s3_instance.get_presigned_url(
                                        str(case_sheet.get(
                                            'prescription_s3_object_key')) if 'prescription_s3_object_key' in case_sheet.keys() else ''),
                                "prescription_url":
                                    s3_instance.get_presigned_url(
                                        str(case_sheet.get(
                                            'prescription_s3_object_url')) if 'prescription_s3_object_url' in case_sheet.keys() else '')
                            },
                            "doctor_id": case_sheet.get('case_doctor'),
                            "doctor_name": case_sheet.get('doctor_name'),
                            "is_reissued_prescription": case_sheet.get('is_reissued_prescription'),
                            "preview": case_sheet.get('allow_to_patient'),
                            "is_active": is_active,
                            "is_case_open": case_sheet.get('is_open'),
                            "latest_prescription": latest_prescription
                        }
                    )
            return data_to_return

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise Exception(f'Error occurred as {str(e)} while getting prescription for patient case')

    def get_users_prescription(self, patient_id: str):
        try:
            data_to_return = self.get_patients_prescriptions(patient_id=patient_id, logged_in_user=patient_id)
            sorted_data = sorted(data_to_return, key=lambda x: str(x['is_active']) + x['consultation_date'],
                                 reverse=True)
            # sorted_data = sorted_data[skip:limit]
            return dict(
                msg='Success',
                prescription_data=sorted_data
            )

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409,
                                detail=f'{str(e)}')

    def get_relatives_prescription(self, care_taker_id: str, relative_id: str):
        try:
            relatives = self.db.query(dbmodels.DBRelatives).filter(DBRelatives.caretaker_id == care_taker_id).filter(
                DBRelatives.relativeid == relative_id).one_or_none()
            if relatives is None:
                raise Exception(f'Relative ID: {relative_id} is not mapped with {care_taker_id}')
            data_to_return = self.get_patients_prescriptions(patient_id=relative_id, logged_in_user=care_taker_id)
            return dict(
                msg='Success',
                prescription_data=data_to_return
            )

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409,
                                detail=f'{str(e)}')

    def get_users_latest_prescription(self, patient_id: str):
        try:
            data_to_return = self.get_patients_prescriptions(patient_id=patient_id, logged_in_user=patient_id)
            latest_prescriptions = [data for data in data_to_return if
                                    data.get('latest_prescription') is True and data.get('is_case_open') is True]
            return dict(
                msg='Success',
                prescription_data=latest_prescriptions
            )

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409,
                                detail=f'{str(e)}')

    def validation_chief_complain(self, data: AddChiefComplain):
        try:
            for complain_data in data.chief_complain:
                complain = dict(complain_data)
                is_blank_input_string = is_blank_string(input_string=complain['complain_text'])
                if is_blank_input_string is True:
                    raise Exception('Input some data in complain text')

                is_blank_input_string = is_blank_string(input_string=complain['onset'])
                if is_blank_input_string is True:
                    raise Exception('Input some data in onset')

                is_valid_input_text = string_data_type_validation(input_string=complain['complain_text'])
                if is_valid_input_text is False:
                    raise Exception(
                        'Invalid characters used in chief complain text. Only .,-/ +$& are allowed')

                is_valid_input_text = string_data_type_validation(input_string=complain['onset'])
                if is_valid_input_text is False:
                    raise Exception(
                        'Invalid characters used in chief complain onset. Only .,-/ +$& are allowed')

                is_valid_input_length = is_valid_length(input_string=complain['complain_text'],
                                                        allowed_length=300)
                if is_valid_input_length is False:
                    raise Exception('Character limit is 300 in chief complain text')

                is_valid_input_length = is_valid_length(input_string=complain['onset'], allowed_length=300)
                if is_valid_input_length is False:
                    raise Exception('Character limit is 300 in chief complain onset')

        except Exception as e:
            raise Exception(str(e))

    def validation_notes(self, data: DoctorNotes):
        try:
            is_blank_input_string = is_blank_string(input_string=data.notes)
            if is_blank_input_string is True:
                raise Exception('Input some data in notes')

            is_valid_input_length = is_valid_length(input_string=data.notes, allowed_length=20000)
            if is_valid_input_length is False:
                raise Exception('Character limit is 20000 in notes')

        except Exception as e:
            raise Exception(str(e))

    def validation_substance_use_abuse(self, data: SubstanceUseAdd):
        try:
            substance_data = data.substances_data
            for s_data in substance_data:
                input_substance_data = dict(s_data)

                is_blank_input_string = is_blank_string(input_string=input_substance_data['substance'])
                if is_blank_input_string is True:
                    raise Exception('Input some data in substance text')

                is_valid_input_text = string_data_type_validation(input_string=input_substance_data['substance'])
                if is_valid_input_text is False:
                    raise Exception(
                        'Invalid characters used in substance text. Only .,-/ +$& are allowed')

                is_blank_input_string = is_blank_string(input_string=input_substance_data['first_use'])
                if is_blank_input_string is True:
                    raise Exception('Input some data in first use text')

                is_valid_input_text = string_data_type_validation(input_string=input_substance_data['first_use'])
                if is_valid_input_text is False:
                    raise Exception(
                        'Invalid characters used in first use text. Only .,-/ +$& are allowed')

                is_blank_input_string = is_blank_string(input_string=input_substance_data['frequency'])
                if is_blank_input_string is True:
                    raise Exception('Input some data in frequency')

                is_valid_input_text = string_data_type_validation(input_string=input_substance_data['frequency'])
                if is_valid_input_text is False:
                    raise Exception(
                        'Invalid characters used in frequency. Only .,-/ +$& are allowed')

                is_blank_input_string = is_blank_string(input_string=input_substance_data['perceived_problem'])
                if is_blank_input_string is True:
                    raise Exception('Input some data in perceived problem')

                is_valid_input_text = string_data_type_validation(
                    input_string=input_substance_data['perceived_problem'])
                if is_valid_input_text is False:
                    raise Exception(
                        'Invalid characters used in perceived problem. Only .,-/ +$& are allowed')

        except Exception as e:
            raise Exception(str(e))

    def validation_assessment(self, data: Assessment):
        try:
            assessment_data = data.provisional_diagnosis
            for prov_diag in assessment_data:
                icd_data = dict(prov_diag)

                is_blank_input_string = is_blank_string(input_string=icd_data['title'])
                if is_blank_input_string is True:
                    raise Exception('Input some data in title')

                is_valid_input_text = string_data_type_validation(input_string=icd_data['title'])
                if is_valid_input_text is False:
                    raise Exception(
                        'Invalid characters used in title. Only .,-/ +$& are allowed')

                if len(icd_data['comments']) > 0:
                    is_valid_input_text = string_data_type_validation(input_string=icd_data['comments'])
                    if is_valid_input_text is False:
                        raise Exception(
                            'Invalid characters used in comments. Only .,-/ +$& are allowed')

                if len(icd_data['icd_code']) > 0:
                    is_valid_input_text = string_data_type_validation(input_string=icd_data['icd_code'])
                    if is_valid_input_text is False:
                        raise Exception('Invalid characters used in icd code')

            if len(data.rule_out_or_remarks) > 0:
                is_valid_input_text = string_data_type_validation(input_string=data.rule_out_or_remarks)
                if is_valid_input_text is False:
                    raise Exception('Invalid characters used in rule out or remarks code. Only .,-/ +$& are allowed')


        except Exception as e:
            raise Exception(str(e))

    def validation_lab_tests(self, data: LabTest):
        try:
            for test_name in data.lab_tests:
                is_blank_input_string = is_blank_string(input_string=test_name)
                if is_blank_input_string is True:
                    raise Exception('Input some data in test name')

                is_valid_input_text = string_data_type_validation(input_string=test_name)
                if is_valid_input_text is False:
                    raise Exception('Invalid characters used in test name. Only .,-/ +$& are allowed')

        except Exception as e:
            raise Exception(str(e))

    def validation_medicines(self, data: MentalHealthMedication):
        try:
            for meds in data.medications:
                medication = dict(meds)
                is_blank_input_string = is_blank_string(input_string=meds.medicine_salt)
                if is_blank_input_string is True:
                    raise Exception('Input some data in medicine salt')

                is_valid_input_text = string_data_type_validation(input_string=meds.medicine_salt)
                if is_valid_input_text is False:
                    raise Exception(
                        'Invalid characters used in medicine salt. Only .,-/ +$& are allowed')

                is_blank_input_string = is_blank_string(input_string=meds.medicine_brand)
                if is_blank_input_string is True:
                    raise Exception('Input some data in medicine brand')

                is_valid_input_text = string_data_type_validation(input_string=meds.medicine_brand)
                if is_valid_input_text is False:
                    raise Exception(
                        'Invalid characters used in medicine brand. Only .,-/ +$& are allowed')

                if meds.sos is False:
                    if (
                            meds.frequency.mor is None and meds.frequency.aft is None and meds.frequency.eve is None and meds.frequency.custom is None) or (
                            meds.frequency.mor == "" and meds.frequency.aft == "" and meds.frequency.eve == "" and meds.frequency.custom == ""):
                        raise Exception('Enter medicine frequency')

                is_blank_input_string = is_blank_string(input_string=meds.drug_form)
                if is_blank_input_string is True:
                    raise Exception('Input some data in drug form')

                is_valid_input_text = string_data_type_validation(input_string=meds.drug_form)
                if is_valid_input_text is False:
                    raise Exception(
                        'Invalid characters used in drug form. Only .,-/ +$& are allowed')

                if len(meds.instructions) > 0:
                    is_valid_input_text = string_data_type_validation(input_string=meds.instructions)
                    if is_valid_input_text is False:
                        raise Exception(
                            'Invalid characters used in instructions. Only .,-/ +$& are allowed')

        except Exception as e:
            raise Exception(str(e))

    def validation_treatment_plan(self, data: TreatmentPlanAdd):
        try:
            for plan in data.treatment_plan:
                if len(plan.problem) > 0:
                    is_valid_input_text = string_data_type_validation(input_string=plan.problem)
                    if is_valid_input_text is False:
                        raise Exception(
                            'Invalid characters used in problem. Only .,-/ +$& are allowed')

                if len(plan.goal) > 0:
                    is_valid_input_text = string_data_type_validation(input_string=plan.goal)
                    if is_valid_input_text is False:
                        raise Exception(
                            'Invalid characters used in goal. Only .,-/ +$& are allowed')

                if len(plan.activities) > 0:
                    is_valid_input_text = string_data_type_validation(input_string=plan.activities)
                    if is_valid_input_text is False:
                        raise Exception(
                            'Invalid characters used in activities. Only .,-/ +$& are allowed')

            if len(data.comments) > 0:
                is_valid_input_text = string_data_type_validation(input_string=data.comments)
                if is_valid_input_text is False:
                    raise Exception(
                        'Invalid characters used in comments. Only .,-/ +$& are allowed')



        except Exception as e:
            raise Exception(str(e))

    def validation_follow_up(self, data: FollowUp):
        try:
            is_blank_input_string = is_blank_string(input_string=data.next_appointment)
            if is_blank_input_string is True:
                raise Exception('Input some data in next appointment')

            is_valid_input_text = string_data_type_validation(input_string=data.next_appointment)
            if is_valid_input_text is False:
                raise Exception(
                    'Invalid characters used in next appointment. Only .,-/ +$& are allowed')

            is_blank_input_string = is_blank_string(input_string=data.ayoo_check_in_days)
            if is_blank_input_string is True:
                raise Exception('Input some data in ayoo check-in days')

            is_valid_input_text = string_data_type_validation(input_string=data.ayoo_check_in_days)
            if is_valid_input_text is False:
                raise Exception(
                    'Invalid characters used in ayoo check-in days. Only .,-/ +$& are allowed')

            for obj in data.objectives:
                is_blank_input_string = is_blank_string(input_string=obj)
                if is_blank_input_string is True:
                    raise Exception('Input some data in objectives')

                is_valid_input_text = string_data_type_validation(input_string=obj)
                if is_valid_input_text is False:
                    raise Exception(
                        'Invalid characters used in objectives. Only .,-/ +$& are allowed')

        except Exception as e:
            raise Exception(str(e))

    def validation_work_up_plan(self, data: WorkUpPlan):
        try:
            for plan in data.work_up_plan:
                is_blank_input_string = is_blank_string(input_string=plan.description)
                if is_blank_input_string is True:
                    raise Exception('Input some data in description')

                is_valid_input_text = string_data_type_validation(input_string=plan.goal)
                if is_valid_input_text is False:
                    raise Exception(
                        'Invalid characters used in description. Only .,-/ +$& are allowed')

                if len(plan.activities) > 0:
                    is_valid_input_text = string_data_type_validation(input_string=plan.activities)
                    if is_valid_input_text is False:
                        raise Exception(
                            'Invalid characters used in activities. Only .,-/ +$& are allowed')

                if len(plan.comments) > 0:
                    is_valid_input_text = string_data_type_validation(input_string=plan.comments)
                    if is_valid_input_text is False:
                        raise Exception(
                            'Invalid characters used in activities. Only .,-/ +$& are allowed')

        except Exception as e:
            raise Exception(str(e))

    def get_patient_complete_details(self, doctor_id: str, data: GetMentalHealthCaseDetails):
        try:
            valid_case = self.mongo_db['Appointments'].find_one({
                'patient_id': data.patient_id,
                'caseid': data.case_id,
                'doctorid': doctor_id
            })
            if valid_case is None:
                raise Exception('No case found for given details')

            user_ctrl = UserController(db=self.db, otp_generator=None)
            user_data = user_ctrl.get_user_details(userid=data.patient_id, mongo=self.mongo)
            if user_data is None:
                user_data = user_ctrl.get_relative_details(userid=data.patient_id, mongo=self.mongo)
                if user_data is None:
                    raise Exception('Invalid patient ID')
            user_data['language_preference'] = ''
            family_doctor_name = ''
            if user_data['family_doctor'] is not None:
                doctor_ctrl = DoctorController(db=self.db, mongo=self.mongo)
                doctor_details, msg = doctor_ctrl.get_by_id(doctorid=user_data['family_doctor'])
                if doctor_details is not None:
                    family_doctor_name = doctor_details['firstname'] + ' ' + doctor_details['lastname']

            user_data['family_doctor_name'] = family_doctor_name
            return user_data

        except Exception as e:
            raise HTTPException(status_code=409,
                                detail=f'Error occurred as {str(e)} while fetching full details for the patient')


class PsychiatryController(MentalHealthController):

    def __init__(self, db: scoped_session, mongo=None):
        super().__init__(db=db, mongo=mongo)

    def check_mongo_case_array_length(self, key_name: str, patient_id: str, case_id: str):
        try:
            mental_health_case = self.get_updated_psychiatry_case_record(patient_id=patient_id, case_id=case_id)
            return len(mental_health_case[f'{key_name}']['all_records'])

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise Exception(str(e))

    def delete_psychiatry_chief_complain_single_entity(self, data: DeleteChiefComplain, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')
            self.is_valid_psychiatry_case_conditions(patient_id=str(data.patient_id), case_id=str(data.case_id))
            i = int(data.index)
            # logger.info(i)
            res = self.mongo_db['UserCollection'].find_one(
                {"psychiatrist_prescriptions.case_id": data.case_id})
            for pres in res['psychiatrist_prescriptions']:

                if pres['case_id'] == data.case_id:
                    al = len(pres['chief_complain']['all_records']) - 1
                    m = len(pres['chief_complain']['all_records'][al]['chief_complain'])
                    # logger.info(m)
                    if m == 0:
                        raise Exception("chief complaint is empty")
                    if al == 0:
                        self.delete_data_from_mongo_case(key_name='chief_complain',
                                                         patient_id=data.patient_id,
                                                         case_id=data.case_id)

                    elif al < 0:
                        return

                    else:

                        del pres['chief_complain']['all_records'][al]['chief_complain'][i]
                        update = pres['chief_complain']['all_records'][al]

                        self.update_wrapper(key_name='chief_complain', data_to_update=update,
                                            patient_id=data.patient_id,
                                            case_id=data.case_id)
                        break

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409, detail=str(e))

    def pop_a_record_from_mongo_case_array(self, key_name: str, patient_id: str, case_id: str):
        try:
            self.mongo_db['UserCollection'].find_one_and_update({"userid": patient_id,
                                                                 "psychiatrist_prescriptions.case_id": case_id}, {
                                                                    "$pop": {
                                                                        f'psychiatrist_prescriptions.$.{key_name}.all_records': 1
                                                                    }
                                                                })

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise Exception(str(e))

    def delete_data_from_mongo_case(self, key_name: str, patient_id: str, case_id: str):
        try:
            get_array_length = self.check_mongo_case_array_length(key_name=key_name,
                                                                  patient_id=patient_id,
                                                                  case_id=case_id)
            if get_array_length == 0:
                raise Exception('No data found')

            is_update_allowed = self.check_if_update_allowed(patient_id=patient_id, case_id=case_id, key_name=key_name)
            if is_update_allowed is not True:
                raise Exception('Deletion not allowed')

            self.pop_a_record_from_mongo_case_array(key_name=key_name, patient_id=patient_id, case_id=case_id)

            if get_array_length == 1:

                self.mongo_db['UserCollection'].find_one_and_update(
                    {"userid": patient_id,
                     "psychiatrist_prescriptions.case_id": case_id},
                    {
                        "$set": {
                            f'psychiatrist_prescriptions.$.{key_name}.current_record':
                                {}
                        }
                    }
                )
            else:
                mental_health_case = self.get_updated_psychiatry_case_record(patient_id=patient_id, case_id=case_id)
                self.mongo_db['UserCollection'].find_one_and_update(
                    {"userid": patient_id,
                     "psychiatrist_prescriptions.case_id": case_id},
                    {
                        "$set": {
                            f'psychiatrist_prescriptions.$.{key_name}.current_record':
                                mental_health_case[f'{key_name}']['all_records'][-1]
                        }
                    }
                )

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise Exception(str(e))

    def update_wrapper(self, key_name: str, data_to_update: {}, patient_id: str, case_id: str):
        data_exists = self.check_mongo_case_array_length(key_name=key_name,
                                                         patient_id=patient_id,
                                                         case_id=case_id)

        if data_exists == 0:
            raise Exception('No data found to update')

        is_update_allowed = self.check_if_update_allowed(patient_id=patient_id, case_id=case_id, key_name=key_name)
        if is_update_allowed is not True:
            raise Exception('Update not allowed')

        self.pop_a_record_from_mongo_case_array(key_name=key_name,
                                                patient_id=patient_id,
                                                case_id=case_id)

        self.add_record_to_existing_psychiatry_case_key(key_name=key_name,
                                                        data_to_record=data_to_update,
                                                        patient_id=patient_id,
                                                        case_id=case_id)

    def add_chief_complain(self, data: AddChiefComplain, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')

            self.is_valid_psychiatry_case_conditions(patient_id=str(data.patient_id), case_id=str(data.case_id))
            self.validation_chief_complain(data=data)

            li = []
            for chief in data.chief_complain:
                chief = dict(chief)
                obj = {'complain_text': chief['complain_text'], 'onset': chief['onset']}
                li.append(obj)
            current_complain = {
                'chief_complain': li,
                'date_recorded': datetime.now()
            }

            self.add_record_to_existing_psychiatry_case_key(key_name='chief_complain',
                                                            data_to_record=current_complain,
                                                            patient_id=data.patient_id,
                                                            case_id=data.case_id)

            mental_health_case = self.get_psychiatry_case_details(data=GetMentalHealthCaseDetails(
                patient_id=data.patient_id, case_id=data.case_id))
            return {'msg': 'Chief complain recorded',
                    'case_details': mental_health_case['data']}

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409, detail=str(e))

    def update_latest_chief_complain(self, data: AddChiefComplain, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')

            self.is_valid_psychiatry_case_conditions(patient_id=data.patient_id, case_id=data.case_id)
            self.validation_chief_complain(data=data)
            li = []
            for chief in data.chief_complain:
                chief = dict(chief)
                obj = {'complain_text': chief['complain_text'], 'onset': chief['onset']}
                li.append(obj)
            current_record = {
                'chief_complain': li,
                'date_recorded': datetime.now()
            }

            self.update_wrapper(key_name='chief_complain', data_to_update=current_record, patient_id=data.patient_id,
                                case_id=data.case_id)

            mental_health_case = self.get_psychiatry_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            return {'msg': 'Chief complain updated',
                    'case_details': mental_health_case['data']}
        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409, detail=f'Error occurred as {str(e)} while updating chief complain')

    def delete_latest_chief_complain(self, data: GetMentalHealthCaseDetails, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')

            self.is_valid_psychiatry_case_conditions(patient_id=data.patient_id, case_id=data.case_id)

            self.delete_data_from_mongo_case(key_name='chief_complain',
                                             patient_id=data.patient_id,
                                             case_id=data.case_id)

            mental_health_case = self.get_psychiatry_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            return {'msg': 'Chief complain deleted',
                    'case_details': mental_health_case['data']}

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409, detail=f'Error occurred as {str(e)} while deleting chief complain')

    def record_sleep_and_appetite(self, data: SleepAndAppetite, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')

            self.is_valid_psychiatry_case_conditions(patient_id=str(data.patient_id), case_id=str(data.case_id))

            sleep_appetite = {
                'sleep': data.sleep,
                'appetite': data.appetite,
                data.other.key_name if data.other else 'other_data': data.other.value if data.other else None,
                'date_recorded': datetime.now()
            }
            self.add_record_to_existing_psychiatry_case_key(key_name='sleep_appetite',
                                                            data_to_record=sleep_appetite,
                                                            patient_id=data.patient_id,
                                                            case_id=data.case_id)

            mental_health_case = self.get_psychiatry_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            return {'msg': 'Sleep and appetite recorded',
                    'case_details': mental_health_case['data']}

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409,
                                detail=f'Error occurred as {str(e)} while recording sleep and appetite.')

    def update_sleep_appetite(self, data: SleepAndAppetite, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')

            self.is_valid_psychiatry_case_conditions(patient_id=str(data.patient_id), case_id=str(data.case_id))

            sleep_appetite = {
                'sleep': data.sleep,
                'appetite': data.appetite,
                data.other.key_name if data.other else 'other_data': data.other.value if data.other else None,
                'date_recorded': datetime.now()
            }

            self.update_wrapper(key_name='sleep_appetite', data_to_update=sleep_appetite, patient_id=data.patient_id,
                                case_id=data.case_id)

            mental_health_case = self.get_psychiatry_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            return {'msg': 'Last record of sleep and appetite updated',
                    'case_details': mental_health_case['data']}

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409,
                                detail=f'Error occurred as {str(e)} while updating sleep and appetite.')

    def delete_sleep_appetite(self, data: CaseElement, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')

            self.is_valid_psychiatry_case_conditions(patient_id=str(data.patient_id), case_id=str(data.case_id))

            self.delete_data_from_mongo_case(key_name='sleep_appetite',
                                             patient_id=data.patient_id,
                                             case_id=data.case_id)

            mental_health_case = self.get_psychiatry_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            return {'msg': 'Last record of sleep and appetite deleted',
                    'case_details': mental_health_case['data']}

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409,
                                detail=f'Error occurred as {str(e)} while deleting Sleep and appetite.')

    def add_doctor_notes(self, data: DoctorNotes, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')

            self.is_valid_psychiatry_case_conditions(patient_id=str(data.patient_id), case_id=str(data.case_id))
            self.validation_notes(data=data)
            mental_health_case = self.get_psychiatry_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            get_session = int(mental_health_case['data']['doctor_notes']['session']) if mental_health_case['data'][
                'doctor_notes'] else 0
            session_no = get_session + 1

            doctor_notes = {
                'note': data.notes,
                'session': session_no,
                'date_recorded': datetime.now()
            }
            self.add_record_to_existing_psychiatry_case_key(key_name='doctor_notes',
                                                            data_to_record=doctor_notes,
                                                            patient_id=data.patient_id,
                                                            case_id=data.case_id)

            mental_health_case = self.get_psychiatry_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            return {'msg': 'Doctor notes added',
                    'case_details': mental_health_case['data']}

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409, detail=f'Error occurred as {str(e)} while recording doctor notes.')

    def update_doctor_notes(self, data: DoctorNotes, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')

            self.is_valid_psychiatry_case_conditions(patient_id=str(data.patient_id), case_id=str(data.case_id))
            self.validation_notes(data=data)

            mental_health_case = self.get_psychiatry_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            session_no = int(mental_health_case['data']['doctor_notes']['session'])

            doctor_notes = {
                'note': data.notes,
                'session': session_no,
                'date_recorded': mental_health_case['data']['doctor_notes']['date_recorded']
            }

            self.update_wrapper(key_name='doctor_notes', data_to_update=doctor_notes, patient_id=data.patient_id,
                                case_id=data.case_id)

            mental_health_case = self.get_psychiatry_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            return {'msg': 'Last record of doctor notes updated',
                    'case_details': mental_health_case['data']}

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409, detail=f'Error occurred as {str(e)} while updating doctor notes.')

    def delete_doctor_notes(self, data: CaseElement, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')

            self.is_valid_psychiatry_case_conditions(patient_id=str(data.patient_id), case_id=str(data.case_id))

            self.delete_data_from_mongo_case(key_name='doctor_notes',
                                             patient_id=data.patient_id,
                                             case_id=data.case_id)

            mental_health_case = self.get_psychiatry_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            return {'msg': 'Last record of doctor notes deleted',
                    'case_details': mental_health_case['data']}

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409, detail=f'Error occurred as {str(e)} while deleting doctor notes.')

    def record_substance_use_abuse(self, data: SubstanceUseAdd, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')

            self.is_valid_psychiatry_case_conditions(patient_id=str(data.patient_id), case_id=str(data.case_id))
            self.validation_substance_use_abuse(data=data)
            substance_use = []
            for s in data.substances_data:
                substance_use.append(dict(s))

            substance_use_data = {
                'substance_data': substance_use,
                'date_recorded': datetime.now()
            }
            self.add_record_to_existing_psychiatry_case_key(key_name='substances',
                                                            data_to_record=substance_use_data,
                                                            patient_id=data.patient_id,
                                                            case_id=data.case_id)
            mental_health_case = self.get_psychiatry_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            return {'msg': 'Substance usage recorded',
                    'case_details': mental_health_case['data']}

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409,
                                detail=f'Error occurred as {str(e)} while recording substance use-abuse.')

    def update_substance_use_abuse(self, data: SubstanceUseAdd, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')

            self.is_valid_psychiatry_case_conditions(patient_id=str(data.patient_id), case_id=str(data.case_id))
            self.validation_substance_use_abuse(data=data)

            substance_use = []
            for s in data.substances_data:
                substance_use.append(dict(s))

            substance_use_data = {
                'substance_data': substance_use,
                'date_recorded': datetime.now()
            }

            self.update_wrapper(key_name='substances', data_to_update=substance_use_data, patient_id=data.patient_id,
                                case_id=data.case_id)

            mental_health_case = self.get_psychiatry_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            return {'msg': 'Last record of substance use-abuse updated',
                    'case_details': mental_health_case['data']}

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409,
                                detail=f'Error occurred as {str(e)} while updating substance use-abuse.')

    def delete_substance_use_abuse(self, data: CaseElement, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')

            self.is_valid_psychiatry_case_conditions(patient_id=str(data.patient_id), case_id=str(data.case_id))

            self.delete_data_from_mongo_case(key_name='substances',
                                             patient_id=data.patient_id,
                                             case_id=data.case_id)

            mental_health_case = self.get_psychiatry_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            return {'msg': 'Last record of substance use-abuse deleted',
                    'case_details': mental_health_case['data']}

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409,
                                detail=f'Error occurred as {str(e)} while deleting substance use-abuse')

    def record_assessment(self, data: Assessment, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')

            self.is_valid_psychiatry_case_conditions(patient_id=str(data.patient_id), case_id=str(data.case_id))
            self.validation_assessment(data=data)

            p_d = []
            for pd in data.provisional_diagnosis:
                p_d.append(dict(pd))

            assessment_data = {
                'provisional_diagnosis': p_d,
                'rule_out_or_remarks': data.rule_out_or_remarks,
                'date_recorded': datetime.now()
            }
            self.add_record_to_existing_psychiatry_case_key(key_name='assessment',
                                                            data_to_record=assessment_data,
                                                            patient_id=data.patient_id,
                                                            case_id=data.case_id)

            mental_health_case = self.get_psychiatry_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            return {'msg': 'Assessment recorded',
                    'case_details': mental_health_case['data']}

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409, detail=f'Error occurred as {str(e)} while recording assessment.')

    def update_assessment(self, data: Assessment, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')

            self.is_valid_psychiatry_case_conditions(patient_id=str(data.patient_id), case_id=str(data.case_id))
            self.validation_assessment(data=data)

            p_d = []
            for pd in data.provisional_diagnosis:
                p_d.append(dict(pd))

            assessment_data = {
                'provisional_diagnosis': p_d,
                'rule_out_or_remarks': data.rule_out_or_remarks,
                'date_recorded': datetime.now()
            }

            self.update_wrapper(key_name='assessment', data_to_update=assessment_data, patient_id=data.patient_id,
                                case_id=data.case_id)

            mental_health_case = self.get_psychiatry_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            return {'msg': 'Last record of assessment updated',
                    'case_details': mental_health_case['data']}

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409, detail=f'Error occurred as {str(e)} while updating assessment.')

    def delete_assessment(self, data: CaseElement, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')

            self.is_valid_psychiatry_case_conditions(patient_id=str(data.patient_id), case_id=str(data.case_id))

            self.delete_data_from_mongo_case(key_name='assessment',
                                             patient_id=data.patient_id,
                                             case_id=data.case_id)

            mental_health_case = self.get_psychiatry_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            return {'msg': 'Last record of assessment deleted',
                    'case_details': mental_health_case['data']}

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409, detail=f'Error occurred as {str(e)} while deleting assessment')

    def record_lab_tests(self, data: LabTest, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')

            self.is_valid_psychiatry_case_conditions(patient_id=str(data.patient_id), case_id=str(data.case_id))
            self.validation_lab_tests(data=data)

            lab_tests = []
            for lt in data.lab_tests:
                # if lt == "":
                #     raise Exception('Test name cannot be blank')

                lab_tests.append({
                    'test_id': str(uuid.uuid4()),
                    'test_name': lt,
                    'test_report_link': None,
                    'test_date': None
                })

            lab_tests_data = {
                'test_details': lab_tests,
                'date_recorded': datetime.now()
            }
            self.add_record_to_existing_psychiatry_case_key(key_name='lab_tests',
                                                            data_to_record=lab_tests_data,
                                                            patient_id=data.patient_id,
                                                            case_id=data.case_id)

            mental_health_case = self.get_psychiatry_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            return {'msg': 'Lab tests recorded',
                    'case_details': mental_health_case['data']}

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409, detail=f'Error occurred as {str(e)} while recording lab tests.')

    def update_lab_tests(self, data: LabTest, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')

            self.is_valid_psychiatry_case_conditions(patient_id=str(data.patient_id), case_id=str(data.case_id))
            self.validation_lab_tests(data=data)

            mental_health_case = self.get_psychiatry_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            existing_lab_test_data = mental_health_case['data']['lab_tests']
            if existing_lab_test_data is None:
                raise Exception('No lab test found to update')

            lab_test_data = mental_health_case['data']['lab_tests']
            if lab_test_data is None:
                raise Exception('No lab test found to update')
            existing_lab_test_data = lab_test_data['test_details']

            lab_tests = []
            for lt in data.lab_tests:
                # Check if the test name exists in the existing_lab_test_data
                matched_test = next((test for test in existing_lab_test_data if test['test_name'] == lt), None)
                if matched_test:
                    # If the test exists, update the test_report_link and test_date using the previous data
                    lab_tests.append({
                        'test_id': matched_test['test_id'],
                        'test_name': lt,
                        'test_report_link': matched_test.get('test_report_link'),
                        'test_date': matched_test.get('test_date')
                    })
                else:
                    # If the test does not exist, add it with default values for test_report_link and test_date
                    lab_tests.append({
                        'test_id': str(uuid.uuid4()),
                        'test_name': lt,
                        'test_report_link': None,
                        'test_date': None
                    })

            lab_tests_data = {
                'test_details': lab_tests,
                'date_recorded': datetime.now()
            }

            self.update_wrapper(key_name='lab_tests', data_to_update=lab_tests_data, patient_id=data.patient_id,
                                case_id=data.case_id)

            mental_health_case = self.get_psychiatry_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            return {'msg': 'Last record of lab tests updated',
                    'case_details': mental_health_case['data']}

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409, detail=f'Error occurred as {str(e)} while updating lab tests.')

    def delete_lab_tests(self, data: CaseElement, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')

            self.is_valid_psychiatry_case_conditions(patient_id=str(data.patient_id), case_id=str(data.case_id))

            self.delete_data_from_mongo_case(key_name='lab_tests',
                                             patient_id=data.patient_id,
                                             case_id=data.case_id)

            mental_health_case = self.get_psychiatry_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            return {'msg': 'Last record of lab tests deleted',
                    'case_details': mental_health_case['data']}

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409, detail=f'Error occurred as {str(e)} while deleting lab tests')

    def list_appointments(self, data: MentalHealthMedication, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')

            # self.is_valid_psychiatry_case_conditions(patient_id=str(data.patient_id), case_id=str(data.case_id))

            apps = []

            seven_days_ago = datetime.now() - timedelta(hours=24)
            resp = list(self.mongo_db["Appointments"].find({
                "caseid": data.case_id,
                "appointment_slot": {"$lte": datetime.now()},
                "is_active": True,
                "is_confirmed": True}).sort("appointment_slot", DESCENDING))

            dr = {}
            for key, values in resp[0].items():
                if key == 'appointment_id':
                    dr[key] = values
                if key == 'appointment_type':
                    dr[key] = values
                if key == 'appointment_slot':
                    dr[key] = values
            apps.append(dr)

            return {'msg': 'Appointment List',
                    'appointment_details': apps}

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409,
                                detail=f'Error occurred as {str(e)} while listing patient appointments.')

    def record_medications(self, data: MentalHealthMedication, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')

            self.is_valid_psychiatry_case_conditions(patient_id=str(data.patient_id), case_id=str(data.case_id))
            self.validation_medicines(data=data)

            meds = []
            for m in data.medications:
                medication = dict(m)
                medication['frequency'] = dict(m.frequency)  # Convert MedsFrequency object to dictionary
                if m.sos is False:
                    end_date = datetime.strptime(medication['start_date'], "%Y-%m-%d") + timedelta(
                        days=medication['duration_in_days'])
                    medication['end_date'] = end_date.strftime("%Y-%m-%d")
                else:
                    medication['end_date'] = None

                meds.append(medication)

            meds = sorted(meds, key=lambda x: (x['start_date'], x['duration_in_days']))

            medications_data = {
                'meds': meds,
                'date_recorded': datetime.now()
            }

            self.add_record_to_existing_psychiatry_case_key(key_name='medications',
                                                            data_to_record=medications_data,
                                                            patient_id=data.patient_id,
                                                            case_id=data.case_id)

            mental_health_case = self.get_psychiatry_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            return {'msg': 'Medication recorded',
                    'case_details': mental_health_case['data']}

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409, detail=f'Error occurred as {str(e)} while recording medications.')

    def update_medications(self, data: MentalHealthMedication, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')

            self.is_valid_psychiatry_case_conditions(patient_id=str(data.patient_id), case_id=str(data.case_id))
            self.validation_medicines(data=data)

            meds = []

            for m in data.medications:
                medication = dict(m)
                medication['frequency'] = dict(m.frequency)  # Convert MedsFrequency object to dictionary
                if m.sos is False:
                    end_date = datetime.strptime(medication['start_date'], "%Y-%m-%d") + timedelta(
                        days=medication['duration_in_days'])
                    medication['end_date'] = end_date.strftime("%Y-%m-%d")
                else:
                    medication['end_date'] = None

                meds.append(medication)

            meds = sorted(meds, key=lambda x: (x['start_date'], x['duration_in_days']))

            medications_data = {
                'meds': meds,
                'date_recorded': datetime.now()
            }

            self.update_wrapper(key_name='medications', data_to_update=medications_data,
                                patient_id=data.patient_id,
                                case_id=data.case_id)

            mental_health_case = self.get_psychiatry_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            return {'msg': 'Last record of medications updated',
                    'case_details': mental_health_case['data']}

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409, detail=f'Error occurred as {str(e)} while updating medications.')

    def delete_medications(self, data: CaseElement, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')

            self.is_valid_psychiatry_case_conditions(patient_id=str(data.patient_id), case_id=str(data.case_id))

            self.delete_data_from_mongo_case(key_name='medications',
                                             patient_id=data.patient_id,
                                             case_id=data.case_id)

            mental_health_case = self.get_psychiatry_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            return {'msg': 'Last record of medications deleted',
                    'case_details': mental_health_case['data']}

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409, detail=f'Error occurred as {str(e)} while deleting medications')

    def record_treatment_plan(self, data: TreatmentPlanAdd, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')
            self.is_valid_psychiatry_case_conditions(patient_id=str(data.patient_id), case_id=str(data.case_id))
            self.validation_treatment_plan(data=data)

            treatment_plan = []
            for tp in data.treatment_plan:
                dict_tp = dict(tp)
                dict_tp['plan_id'] = str(uuid.uuid4())
                treatment_plan.append(dict_tp)

            treatment_plan_data = {
                'treatment_plan': treatment_plan,
                'comments': data.comments,
                'date_recorded': datetime.now()
            }
            self.add_record_to_existing_psychiatry_case_key(key_name='treatment_plan',
                                                            data_to_record=treatment_plan_data,
                                                            patient_id=data.patient_id,
                                                            case_id=data.case_id)

            mental_health_case = self.get_psychiatry_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            return {'msg': 'Treatment plan recorded',
                    'case_details': mental_health_case['data']}

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409, detail=f'Error occurred as {str(e)} while recording treatment plan.')

    def update_treatment_plan(self, data: TreatmentPlanAdd, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')

            self.is_valid_psychiatry_case_conditions(patient_id=str(data.patient_id), case_id=str(data.case_id))
            self.validation_treatment_plan(data=data)

            treatment_plan = []
            for tp in data.treatment_plan:
                dict_tp = dict(tp)
                dict_tp['plan_id'] = str(uuid.uuid4())
                treatment_plan.append(dict_tp)

            treatment_plan_data = {
                'treatment_plan': treatment_plan,
                'comments': data.comments,
                'date_recorded': datetime.now()
            }

            self.update_wrapper(key_name='treatment_plan', data_to_update=treatment_plan_data,
                                patient_id=data.patient_id,
                                case_id=data.case_id)

            mental_health_case = self.get_psychiatry_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            return {'msg': 'Last record of treatment plan updated',
                    'case_details': mental_health_case['data']}

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409, detail=f'Error occurred as {str(e)} while updating treatment plan.')

    def delete_treatment_plan(self, data: CaseElement, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')

            self.is_valid_psychiatry_case_conditions(patient_id=str(data.patient_id), case_id=str(data.case_id))

            self.delete_data_from_mongo_case(key_name='treatment_plan',
                                             patient_id=data.patient_id,
                                             case_id=data.case_id)

            mental_health_case = self.get_psychiatry_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            return {'msg': 'Last record of treatment plan deleted',
                    'case_details': mental_health_case['data']}

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409, detail=f'Error occurred as {str(e)} while deleting treatment plan')

    def record_therapy_recommendation(self, data: TherapyRecommendationAdd, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')
            self.is_valid_psychiatry_case_conditions(patient_id=str(data.patient_id), case_id=str(data.case_id))

            therapy_suggested = []
            for tr in data.therapy_recommendation:
                therapy_suggested.append(dict(tr))

            therapy_data = {
                'comments': data.comments,
                'suggested_therapy': therapy_suggested,
                'date_recorded': datetime.now()
            }
            self.add_record_to_existing_psychiatry_case_key(key_name='therapy_recommendation',
                                                            data_to_record=therapy_data,
                                                            patient_id=data.patient_id,
                                                            case_id=data.case_id)

            mental_health_case = self.get_psychiatry_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            return {'msg': 'Therapy recommendation recorded',
                    'case_details': mental_health_case['data']}

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409,
                                detail=f'Error occurred as {str(e)} while recording therapy recommendation.')

    def update_therapy_recommendation(self, data: TherapyRecommendationAdd, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')

            self.is_valid_psychiatry_case_conditions(patient_id=str(data.patient_id), case_id=str(data.case_id))

            therapy_suggested = []
            for tr in data.therapy_recommendation:
                therapy_suggested.append(dict(tr))

            therapy_data = {
                'comments': data.comments,
                'suggested_therapy': therapy_suggested,
                'date_recorded': datetime.now()
            }

            self.update_wrapper(key_name='therapy_recommendation', data_to_update=therapy_data,
                                patient_id=data.patient_id,
                                case_id=data.case_id)

            mental_health_case = self.get_psychiatry_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            return {'msg': 'Last record of therapy recommendation updated',
                    'case_details': mental_health_case['data']}

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409,
                                detail=f'Error occurred as {str(e)} while updating therapy recommendation.')

    def delete_therapy_recommendation(self, data: CaseElement, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')

            self.is_valid_psychiatry_case_conditions(patient_id=str(data.patient_id), case_id=str(data.case_id))

            self.delete_data_from_mongo_case(key_name='therapy_recommendation',
                                             patient_id=data.patient_id,
                                             case_id=data.case_id)

            mental_health_case = self.get_psychiatry_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            return {'msg': 'Last record of therapy_recommendation deleted',
                    'case_details': mental_health_case['data']}

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409,
                                detail=f'Error occurred as {str(e)} while deleting therapy recommendation')

    def record_follow_up(self, data: FollowUp, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')

            self.is_valid_psychiatry_case_conditions(patient_id=str(data.patient_id), case_id=str(data.case_id))
            self.validation_follow_up(data=data)

            follow_up = {
                'objectives': data.objectives,
                'next_appointment': data.next_appointment,
                'appointment_type': data.appointment_type,
                'ayoo_check_in_days': data.ayoo_check_in_days,
                'date_recorded': datetime.now()
            }
            self.add_record_to_existing_psychiatry_case_key(key_name='follow_up',
                                                            data_to_record=follow_up,
                                                            patient_id=data.patient_id,
                                                            case_id=data.case_id)

            mental_health_case = self.get_psychiatry_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            return {'msg': 'Follow up added',
                    'case_details': mental_health_case['data']}

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409, detail=f'Error occurred as {str(e)} while recording follow up.')

    def update_follow_up(self, data: FollowUp, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')

            self.is_valid_psychiatry_case_conditions(patient_id=str(data.patient_id), case_id=str(data.case_id))
            self.validation_follow_up(data=data)

            follow_up = {
                'objectives': data.objectives,
                'next_appointment': data.next_appointment,
                'appointment_type': data.appointment_type,
                'ayoo_check_in_days': data.ayoo_check_in_days,
                'date_recorded': datetime.now()
            }

            self.update_wrapper(key_name='follow_up', data_to_update=follow_up, patient_id=data.patient_id,
                                case_id=data.case_id)

            mental_health_case = self.get_psychiatry_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            return {'msg': 'Last record of follow up updated',
                    'case_details': mental_health_case['data']}

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409, detail=f'Error occurred as {str(e)} while updating follow up.')

    def delete_follow_up(self, data: CaseElement, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')

            self.is_valid_psychiatry_case_conditions(patient_id=str(data.patient_id), case_id=str(data.case_id))

            self.delete_data_from_mongo_case(key_name='follow_up',
                                             patient_id=data.patient_id,
                                             case_id=data.case_id)

            mental_health_case = self.get_psychiatry_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            return {'msg': 'Last record of follow up deleted',
                    'case_details': mental_health_case['data']}

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409, detail=f'Error occurred as {str(e)} while deleting follow up.')

    def add_client_education(self, data: ClientEducation, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')

            self.is_valid_psychiatry_case_conditions(patient_id=str(data.patient_id), case_id=str(data.case_id))
            client_education = {
                'client_education': data.client_education,
                'date_recorded': datetime.now()
            }
            self.add_record_to_existing_psychiatry_case_key(key_name='client_education',
                                                            data_to_record=client_education,
                                                            patient_id=data.patient_id,
                                                            case_id=data.case_id)

            mental_health_case = self.get_psychiatry_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            return {'msg': 'Client education added',
                    'case_details': mental_health_case['data']}

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409, detail=f'Error occurred as {str(e)} while recording client education.')

    def update_client_education(self, data: ClientEducation, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')

            self.is_valid_psychiatry_case_conditions(patient_id=str(data.patient_id), case_id=str(data.case_id))

            client_education = {
                'client_education': data.client_education,
                'date_recorded': datetime.now()
            }

            self.update_wrapper(key_name='client_education',
                                data_to_update=client_education,
                                patient_id=data.patient_id,
                                case_id=data.case_id)

            mental_health_case = self.get_psychiatry_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            return {'msg': 'Last record of client education updated',
                    'case_details': mental_health_case['data']}

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409, detail=f'Error occurred as {str(e)} while updating client education.')

    def delete_client_education(self, data: CaseElement, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')

            self.is_valid_psychiatry_case_conditions(patient_id=str(data.patient_id), case_id=str(data.case_id))

            self.delete_data_from_mongo_case(key_name='client_education',
                                             patient_id=data.patient_id,
                                             case_id=data.case_id)

            mental_health_case = self.get_psychiatry_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            return {'msg': 'Last record of client education deleted',
                    'case_details': mental_health_case['data']}

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409, detail=f'Error occurred as {str(e)} while deleting client education.')

    def add_care_assessment(self, data: CareAssessment, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')

            self.is_valid_psychiatry_case_conditions(patient_id=str(data.patient_id), case_id=str(data.case_id))

            mental_health_case = self.get_psychiatry_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            care_assessment_data = mental_health_case['data']['care_assessment'] if 'care_assessment' in \
                                                                                    mental_health_case['data'] else {}

            care_assessment = {}
            for attribute in care_assessment_data:
                care_assessment[attribute] = care_assessment_data[attribute]

            for attribute in data.attributes:
                if attribute not in care_assessment_data:
                    care_assessment[attribute] = []

            self.mongo_db['UserCollection'].find_one_and_update(
                {"userid": data.patient_id,
                 "psychiatrist_prescriptions.case_id": data.case_id},
                {
                    "$set": {
                        'psychiatrist_prescriptions.$.care_assessment': care_assessment
                    }
                }
            )

            mental_health_case = self.get_psychiatry_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            return {'msg': 'Care assessment added',
                    'case_details': mental_health_case['data']}

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409, detail=f'Error occurred as {str(e)} while recording care assessment.')

    def remove_care_assessment_attribute(self, data: CareAssessmentRemoveAttribute, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')

            self.is_valid_psychiatry_case_conditions(patient_id=str(data.patient_id), case_id=str(data.case_id))

            mental_health_case = self.get_psychiatry_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            care_assessment_data = mental_health_case['data']['care_assessment'] if 'care_assessment' in \
                                                                                    mental_health_case['data'] else {}

            care_assessment = {}
            for attribute in care_assessment_data:
                care_assessment[attribute] = care_assessment_data[attribute]

            for attribute in data.attributes:
                if attribute in care_assessment_data:
                    del care_assessment[attribute]

            self.mongo_db['UserCollection'].find_one_and_update(
                {"userid": data.patient_id,
                 "psychiatrist_prescriptions.case_id": data.case_id},
                {
                    "$set": {
                        'psychiatrist_prescriptions.$.care_assessment': care_assessment
                    }
                }
            )

            mental_health_case = self.get_psychiatry_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            return {'msg': 'Care assessment removed',
                    'case_details': mental_health_case['data']}

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409, detail=f'Error occurred as {str(e)} while deleting care assessment.')

    def record_case_summary(self, data: CaseSummary, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')

            self.is_valid_psychiatry_case_conditions(patient_id=str(data.patient_id), case_id=str(data.case_id))

            case_summary = {
                'notes_for_patients': data.notes_for_patients,
                'date_recorded': datetime.now()
            }
            self.add_record_to_existing_psychiatry_case_key(key_name='case_summary',
                                                            data_to_record=case_summary,
                                                            patient_id=data.patient_id,
                                                            case_id=data.case_id)

            # close case
            existing_case_type = self.get_mental_health_case_type(patient_id=data.patient_id, case_id=data.case_id)
            if existing_case_type is None:
                raise Exception('The case id provided is not a mental health case id')

            self.close_case(patient_id=data.patient_id, case_id=data.case_id, prescription_type=existing_case_type)

            mental_health_case = self.get_psychiatry_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            return {'msg': 'Case summary added',
                    'case_details': mental_health_case['data']}

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409, detail=f'Error occurred as {str(e)} while recording case summary.')

    def update_case_summary(self, data: CaseSummary, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')

            self.is_valid_psychiatry_case_conditions(patient_id=str(data.patient_id), case_id=str(data.case_id))

            case_summary = {
                'notes_for_patients': data.notes_for_patients,
                'date_recorded': datetime.now()
            }

            self.update_wrapper(key_name='case_summary', data_to_update=case_summary, patient_id=data.patient_id,
                                case_id=data.case_id)

            mental_health_case = self.get_psychiatry_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            return {'msg': 'Last record of case summary updated',
                    'case_details': mental_health_case['data']}

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409, detail=f'Error occurred as {str(e)} while updating case summary.')

    def delete_case_summary(self, data: CaseElement, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')

            self.is_valid_psychiatry_case_conditions(patient_id=str(data.patient_id), case_id=str(data.case_id))

            self.delete_data_from_mongo_case(key_name='case_summary',
                                             patient_id=data.patient_id,
                                             case_id=data.case_id)

            mental_health_case = self.get_psychiatry_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            return {'msg': 'Last record of case summary deleted',
                    'case_details': mental_health_case['data']}

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409, detail=f'Error occurred as {str(e)} while deleting case summary.')

    def psychiatrist_prescription_submit(self, data: CaseElement1, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')
            try:
                self.is_valid_psychiatry_case_conditions(patient_id=str(data.patient_id), case_id=str(data.case_id))
            except Exception as e:
                raise Exception(f'Error occurred as {str(e)} while validating psychiatric case condition.')
            try:
                self.generate_patients_psychiatry_prescription(data=data)
            except Exception as e:
                raise Exception(f'Error occurred as {str(e)} while generating patient psychiatry prescription.')
            # SEND NOTIFICATION

            try:
                mental_health_case = self.get_psychiatry_case_details(
                    data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))
            except Exception as e:
                raise Exception('Error occurred as {str(e)} while getting psychiatry case details.')

            if mental_health_case['data']['doctor_notes']:
                frbs_ctrl = FireBaseNotificationController(db=self.db, mongo=self.mongo)

                patient_details = self.get_patient_details(patientid=data.patient_id)

                prescription_notif = PrescriptionEvent(case_id=data.case_id,
                                                       patient_id=data.patient_id,
                                                       patient_name=patient_details['firstname'],
                                                       upload_date=datetime.now(),
                                                       remarks="None")
                loggers['logger6'].info("upload notification")
                if data.preview in ['true', 'TRUE', 'True', True]:
                    frbs_ctrl.prescription_upload(prescription_event=prescription_notif)
            else:
                raise Exception('Add doctor notes before final submission')

            return {'msg': 'Prescription uploaded',
                    'case_details': mental_health_case['data']}

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409,
                                detail=f'Error occurred as {str(e)} while submitting psychiatrist prescription.')

    def med_notif_toggle_switch(self, data: CaseElement):
        try:
            self.is_valid_psychiatry_case_conditions(patient_id=str(data.patient_id), case_id=str(data.case_id))

            mental_health_case = self.get_psychiatry_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))
            toggle = False if mental_health_case['medicine_reminder'] else True

            self.mongo_db['UserCollection'].find_one_and_update(
                {"userid": data.patient_id,
                 "psychiatrist_prescriptions.case_id": data.case_id},
                {
                    "$set": {
                        'psychiatrist_prescriptions.$.medicine_reminder': toggle
                    }
                }
            )
            mental_health_case = self.get_psychiatry_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))
            return {
                       'msg': 'Toggle switched',
                       'case_details': mental_health_case['data']
                   }, 'Toggle switched'
        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            return None, f'Error occurred as {str(e)} while toggling notification for medicine.'


class TherapyController(MentalHealthController):

    def __init__(self, db: scoped_session, mongo=None):
        super().__init__(db=db, mongo=mongo)

    def check_mongo_case_array_length(self, key_name: str, patient_id: str, case_id: str):
        try:
            mental_health_case = self.get_updated_therapy_case_record(patient_id=patient_id, case_id=case_id)
            return len(mental_health_case[f'{key_name}']['all_records'])

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409, detail=str(e))

    def delete_therapy_chief_complain_single_entity(self, data: DeleteChiefComplain, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')
            self.is_valid_therapy_case_conditions(patient_id=str(data.patient_id), case_id=str(data.case_id))
            i = int(data.index)
            # logger.info(i)
            res = self.mongo_db['UserCollection'].find_one(
                {"therapist_prescriptions.case_id": data.case_id})
            for pres in res['therapist_prescriptions']:

                if pres['case_id'] == data.case_id:
                    al = len(pres['chief_complain']['all_records']) - 1

                    m = len(pres['chief_complain']['all_records'][al]['chief_complain'])

                    if m == 0:
                        raise Exception("therapy chief complaint is empty")
                    if al == 0:
                        self.delete_data_from_mongo_case(key_name='chief_complain',
                                                         patient_id=data.patient_id,
                                                         case_id=data.case_id)

                    elif al < 0:
                        return

                    else:

                        del pres['chief_complain']['all_records'][al]['chief_complain'][i]
                        update = pres['chief_complain']['all_records'][al]

                        self.update_wrapper(key_name='chief_complain', data_to_update=update,
                                            patient_id=data.patient_id,
                                            case_id=data.case_id)
                        return "succesfully deleted entity"

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409, detail=str(e))

    def pop_a_record_from_mongo_case_array(self, key_name: str, patient_id: str, case_id: str):
        try:
            self.mongo_db['UserCollection'].find_one_and_update({"userid": patient_id,
                                                                 "therapist_prescriptions.case_id": case_id}, {
                                                                    "$pop": {
                                                                        f'therapist_prescriptions.$.{key_name}.all_records': 1
                                                                    }
                                                                })

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409, detail=str(e))

    def delete_data_from_mongo_case(self, key_name: str, patient_id: str, case_id: str):
        try:
            get_array_length = self.check_mongo_case_array_length(key_name=key_name,
                                                                  patient_id=patient_id,
                                                                  case_id=case_id)
            if get_array_length == 0:
                raise Exception('No data found')

            is_update_allowed = self.check_if_update_allowed(patient_id=patient_id, case_id=case_id, key_name=key_name)
            if is_update_allowed is not True:
                raise Exception('Deletion not allowed')

            self.pop_a_record_from_mongo_case_array(key_name=key_name, patient_id=patient_id, case_id=case_id)

            if get_array_length == 1:

                self.mongo_db['UserCollection'].find_one_and_update(
                    {"userid": patient_id,
                     "therapist_prescriptions.case_id": case_id},
                    {
                        "$set": {
                            f'therapist_prescriptions.$.{key_name}.current_record':
                                {}
                        }
                    }
                )
            else:
                mental_health_case = self.get_updated_therapy_case_record(patient_id=patient_id, case_id=case_id)
                self.mongo_db['UserCollection'].find_one_and_update(
                    {"userid": patient_id,
                     "therapist_prescriptions.case_id": case_id},
                    {
                        "$set": {
                            f'therapist_prescriptions.$.{key_name}.current_record':
                                mental_health_case[f'{key_name}']['all_records'][-1]
                        }
                    }
                )

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise Exception(str(e))

    def update_wrapper(self, key_name: str, data_to_update: {}, patient_id: str, case_id: str):
        data_exists = self.check_mongo_case_array_length(key_name=key_name,
                                                         patient_id=patient_id,
                                                         case_id=case_id)

        if data_exists == 0:
            raise Exception('No data found to update')

        is_update_allowed = self.check_if_update_allowed(patient_id=patient_id, case_id=case_id, key_name=key_name)
        if is_update_allowed is not True:
            raise Exception('Update not allowed')

        self.pop_a_record_from_mongo_case_array(key_name=key_name,
                                                patient_id=patient_id,
                                                case_id=case_id)

        self.add_record_to_existing_therapist_case_key(key_name=key_name,
                                                       data_to_record=data_to_update,
                                                       patient_id=patient_id,
                                                       case_id=case_id)

    def add_therapy_chief_complain(self, data: AddChiefComplain, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')

            self.is_valid_therapy_case_conditions(patient_id=str(data.patient_id), case_id=str(data.case_id))
            self.validation_chief_complain(data=data)

            li = []
            for chief in data.chief_complain:
                chief = dict(chief)
                obj = {'complain_text': chief['complain_text'], 'onset': chief['onset']}
                li.append(obj)
            current_complain = {
                'chief_complain': li,
                'date_recorded': datetime.now()
            }

            self.add_record_to_existing_therapist_case_key(key_name='chief_complain',
                                                           data_to_record=current_complain,
                                                           patient_id=data.patient_id,
                                                           case_id=data.case_id)

            mental_health_case = self.get_therapy_case_details(data=GetMentalHealthCaseDetails(
                patient_id=data.patient_id, case_id=data.case_id))
            return {'msg': 'Chief complain recorded',
                    'case_details': mental_health_case['data']}

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409, detail=str(e))

    def update_therapy_chief_complain(self, data: AddChiefComplain, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')

            self.is_valid_therapy_case_conditions(patient_id=data.patient_id, case_id=data.case_id)
            self.validation_chief_complain(data=data)
            li = []
            for chief in data.chief_complain:
                chief = dict(chief)
                obj = {'complain_text': chief['complain_text'], 'onset': chief['onset']}
                li.append(obj)
            current_record = {
                'chief_complain': li,
                'date_recorded': datetime.now()
            }

            self.update_wrapper(key_name='chief_complain', data_to_update=current_record, patient_id=data.patient_id,
                                case_id=data.case_id)

            mental_health_case = self.get_therapy_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            return {'msg': 'Chief complain recorded',
                    'case_details': mental_health_case['data']}
        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409, detail=f'Error occurred as {str(e)} while updating chief complain')

    def delete_therapy_chief_complain(self, data: GetMentalHealthCaseDetails, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')

            self.is_valid_therapy_case_conditions(patient_id=data.patient_id, case_id=data.case_id)

            self.delete_data_from_mongo_case(key_name='chief_complain',
                                             patient_id=data.patient_id,
                                             case_id=data.case_id)

            mental_health_case = self.get_therapy_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            return {'msg': 'Chief complain deleted',
                    'case_details': mental_health_case['data']}

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409, detail=f'Error occurred as {str(e)} while deleting chief complain')

    def record_sleep_and_appetite(self, data: SleepAndAppetite, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')

            self.is_valid_therapy_case_conditions(patient_id=str(data.patient_id), case_id=str(data.case_id))

            sleep_appetite = {
                'sleep': data.sleep,
                'appetite': data.appetite,
                data.other.key_name if data.other else 'other_data': data.other.value if data.other else None,
                'date_recorded': datetime.now()
            }
            self.add_record_to_existing_therapist_case_key(key_name='sleep_appetite',
                                                           data_to_record=sleep_appetite,
                                                           patient_id=data.patient_id,
                                                           case_id=data.case_id)

            mental_health_case = self.get_therapy_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            return {'msg': 'Sleep and appetite recorded',
                    'case_details': mental_health_case['data']}

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409,
                                detail=f'Error occurred as {str(e)} while recording sleep and appetite.')

    def update_sleep_appetite(self, data: SleepAndAppetite, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')

            self.is_valid_therapy_case_conditions(patient_id=str(data.patient_id), case_id=str(data.case_id))

            sleep_appetite = {
                'sleep': data.sleep,
                'appetite': data.appetite,
                data.other.key_name if data.other else 'other_data': data.other.value if data.other else None,
                'date_recorded': datetime.now()
            }

            self.update_wrapper(key_name='sleep_appetite', data_to_update=sleep_appetite, patient_id=data.patient_id,
                                case_id=data.case_id)

            mental_health_case = self.get_therapy_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            return {'msg': 'Last record of sleep and appetite updated',
                    'case_details': mental_health_case['data']}

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409,
                                detail=f'Error occurred as {str(e)} while updating sleep and appetite.')

    def delete_sleep_appetite(self, data: CaseElement, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')

            self.is_valid_therapy_case_conditions(patient_id=str(data.patient_id), case_id=str(data.case_id))

            self.delete_data_from_mongo_case(key_name='sleep_appetite',
                                             patient_id=data.patient_id,
                                             case_id=data.case_id)

            mental_health_case = self.get_therapy_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            return {'msg': 'Last record of sleep and appetite deleted',
                    'case_details': mental_health_case['data']}

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409,
                                detail=f'Error occurred as {str(e)} while deleting Sleep and appetite.')

    def record_notes(self, data: TherapyNotes, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')

            self.is_valid_therapy_case_conditions(patient_id=str(data.patient_id), case_id=str(data.case_id))
            self.validation_notes(data=DoctorNotes(
                patient_id=data.patient_id,
                case_id=data.case_id,
                notes=data.notes))

            mental_health_case = self.get_therapy_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            get_session = int(mental_health_case['data']['notes']['session']) if mental_health_case['data'][
                'notes'] else 0
            session_no = get_session + 1

            notes_data = {
                'note': data.notes,
                'session': session_no,
                'date_recorded': datetime.now()
            }
            self.add_record_to_existing_therapist_case_key(key_name='notes',
                                                           data_to_record=notes_data,
                                                           patient_id=data.patient_id,
                                                           case_id=data.case_id)
            mental_health_case = self.get_therapy_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            return {'msg': 'Notes recorded',
                    'case_details': mental_health_case['data']}

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409,
                                detail=f'Error occurred as {str(e)} while recording notes.')

    def update_notes(self, data: TherapyNotes, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')

            self.is_valid_therapy_case_conditions(patient_id=str(data.patient_id), case_id=str(data.case_id))
            self.validation_notes(data=DoctorNotes(
                patient_id=data.patient_id,
                case_id=data.case_id,
                notes=data.notes))
            mental_health_case = self.get_therapy_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            session_no = int(mental_health_case['data']['notes']['session'])

            notes_data = {
                'note': data.notes,
                'session': session_no,
                'date_recorded': mental_health_case['data']['notes']['date_recorded']
            }

            self.update_wrapper(key_name='notes', data_to_update=notes_data, patient_id=data.patient_id,
                                case_id=data.case_id)

            mental_health_case = self.get_therapy_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            return {'msg': 'Last record of notes updated',
                    'case_details': mental_health_case['data']}

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409,
                                detail=f'Error occurred as {str(e)} while updating notes.')

    def delete_notes(self, data: CaseElement, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')

            self.is_valid_therapy_case_conditions(patient_id=str(data.patient_id), case_id=str(data.case_id))

            self.delete_data_from_mongo_case(key_name='notes',
                                             patient_id=data.patient_id,
                                             case_id=data.case_id)

            mental_health_case = self.get_therapy_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            return {'msg': 'Last record of notes deleted',
                    'case_details': mental_health_case['data']}

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409,
                                detail=f'Error occurred as {str(e)} while deleting notes')

    # def record_intervention(self, data: InterventionAdd, logged_in_user: str):
    #     try:
    #         has_write_access = self.has_write_access(logged_in_user)
    #         if not has_write_access:
    #             raise Exception('User is not authorized for this action')
    #
    #         self.is_valid_therapy_case_conditions(patient_id=str(data.patient_id), case_id=str(data.case_id))
    #
    #         interventions = []
    #         for goal in data.goals:
    #             interventions.append({
    #                 'goal_id': str(uuid.uuid4()),
    #                 'goal': goal,
    #                 'result': None,
    #                 'result_recorded_date': None
    #             })
    #
    #         intervention_data = {
    #             'interventions_data': interventions,
    #             'date_recorded': datetime.now()
    #         }
    #         self.add_record_to_existing_therapist_case_key(key_name='interventions',
    #                                                        data_to_record=intervention_data,
    #                                                        patient_id=data.patient_id,
    #                                                        case_id=data.case_id)
    #
    #         mental_health_case = self.get_therapy_case_details(
    #             data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))
    #
    #         return {'msg': 'Interventions recorded',
    #                 'case_details': mental_health_case['data']}
    #
    #     except Exception as e:
    #         raise HTTPException(status_code=409, detail=f'Error occurred as {str(e)} while recording interventions.')
    #
    # def record_intervention_results(self, data: InterventionResultUpdate, logged_in_user: str):
    #     try:
    #         has_write_access = self.has_write_access(logged_in_user)
    #         if not has_write_access:
    #             raise Exception('User is not authorized for this action')
    #
    #         self.is_valid_therapy_case_conditions(patient_id=str(data.patient_id), case_id=str(data.case_id))
    #
    #         for res in data.results:
    #             self.mongo_db['UserCollection'].update_one(
    #                 {"userid": data.patient_id, "therapist_prescriptions.case_id": data.case_id},
    #                 {"$set": {
    #                     "therapist_prescriptions.$[].interventions.all_records.$[].interventions_data.$[elem].result":
    #                         res.result,
    #                     "therapist_prescriptions.$[].interventions.all_records.$[].interventions_data.$[elem].result_recorded_date":
    #                         datetime.now()
    #
    #                 }
    #                 },
    #                 upsert=False,
    #                 array_filters=[{"elem.goal_id": res.goal_id}]
    #
    #             )
    #
    #         get_last_record = self.get_therapy_case_history(GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))
    #         last_record = get_last_record['data']['interventions']['all_records'][-1]
    #
    #         self.mongo_db['UserCollection'].find_one_and_update(
    #             {"userid": data.patient_id, "therapist_prescriptions.case_id": data.case_id},
    #             {
    #                 "$set":{
    #                     "therapist_prescriptions.$[].interventions.current_record.interventions_data":last_record['interventions_data']
    #                 }
    #             }
    #                                                             )
    #         mental_health_case = self.get_therapy_case_details(
    #             data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))
    #
    #         return {'msg': 'Intervention results recorded',
    #                 'case_details': mental_health_case['data']}
    #
    #     except Exception as e:
    #         raise HTTPException(status_code=409, detail=f'Error occurred as {str(e)} while recording interventions.')
    #
    # def update_intervention(self, data: InterventionAdd, logged_in_user: str):
    #     try:
    #         has_write_access = self.has_write_access(logged_in_user)
    #         if not has_write_access:
    #             raise Exception('User is not authorized for this action')
    #
    #         self.is_valid_therapy_case_conditions(patient_id=str(data.patient_id), case_id=str(data.case_id))
    #
    #         interventions = []
    #         for goal in data.goals:
    #             interventions.append({
    #                 'goal_id': str(uuid.uuid4()),
    #                 'goal': goal,
    #                 'result': None,
    #                 'result_recorded_date': None
    #             })
    #
    #         intervention_data = {
    #             'interventions_data': interventions,
    #             'date_recorded': datetime.now()
    #         }
    #
    #         self.update_wrapper(key_name='interventions', data_to_update=intervention_data, patient_id=data.patient_id,
    #                             case_id=data.case_id)
    #
    #         mental_health_case = self.get_therapy_case_details(
    #             data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))
    #
    #         return {'msg': 'Last record of interventions updated',
    #                 'case_details': mental_health_case['data']}
    #
    #     except Exception as e:
    #         raise HTTPException(status_code=409, detail=f'Error occurred as {str(e)} while updating interventions.')
    #
    # def delete_intervention(self, data: CaseElement, logged_in_user: str):
    #     try:
    #         has_write_access = self.has_write_access(logged_in_user)
    #         if not has_write_access:
    #             raise Exception('User is not authorized for this action')
    #
    #         self.is_valid_therapy_case_conditions(patient_id=str(data.patient_id), case_id=str(data.case_id))
    #
    #         self.delete_data_from_mongo_case(key_name='interventions',
    #                                          patient_id=data.patient_id,
    #                                          case_id=data.case_id)
    #
    #         mental_health_case = self.get_therapy_case_details(
    #             data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))
    #
    #         return {'msg': 'Last record of interventions deleted',
    #                 'case_details': mental_health_case['data']}
    #
    #     except Exception as e:
    #         raise HTTPException(status_code=409, detail=f'Error occurred as {str(e)} while deleting assessment')

    def record_assessment(self, data: Assessment, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')

            self.is_valid_therapy_case_conditions(patient_id=str(data.patient_id), case_id=str(data.case_id))
            self.validation_assessment(data=data)

            p_d = []
            for pd in data.provisional_diagnosis:
                p_d.append(dict(pd))

            assessment_data = {
                'provisional_diagnosis': p_d,
                'rule_out_or_remarks': data.rule_out_or_remarks,
                'date_recorded': datetime.now()
            }
            self.add_record_to_existing_therapist_case_key(key_name='assessment',
                                                           data_to_record=assessment_data,
                                                           patient_id=data.patient_id,
                                                           case_id=data.case_id)

            mental_health_case = self.get_therapy_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            return {'msg': 'Therapy assessment recorded',
                    'case_details': mental_health_case['data']}

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409, detail=f'Error occurred as {str(e)} while recording assessment.')

    def update_assessment(self, data: Assessment, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')

            self.is_valid_therapy_case_conditions(patient_id=str(data.patient_id), case_id=str(data.case_id))
            self.validation_assessment(data=data)

            p_d = []
            for pd in data.provisional_diagnosis:
                p_d.append(dict(pd))

            assessment_data = {
                'provisional_diagnosis': p_d,
                'rule_out_or_remarks': data.rule_out_or_remarks,
                'date_recorded': datetime.now()
            }

            self.update_wrapper(key_name='assessment', data_to_update=assessment_data, patient_id=data.patient_id,
                                case_id=data.case_id)

            mental_health_case = self.get_therapy_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            return {'msg': 'Last record of assessment updated',
                    'case_details': mental_health_case['data']}

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409, detail=f'Error occurred as {str(e)} while updating assessment.')

    def delete_assessment(self, data: CaseElement, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')

            self.is_valid_therapy_case_conditions(patient_id=str(data.patient_id), case_id=str(data.case_id))

            self.delete_data_from_mongo_case(key_name='assessment',
                                             patient_id=data.patient_id,
                                             case_id=data.case_id)

            mental_health_case = self.get_therapy_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            return {'msg': 'Last record of assessment deleted',
                    'case_details': mental_health_case['data']}

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409, detail=f'Error occurred as {str(e)} while deleting assessment')

    def record_treatment_plan(self, data: TherapyTreatmentPlan, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')
            self.is_valid_therapy_case_conditions(patient_id=str(data.patient_id), case_id=str(data.case_id))

            recommendation = {
                'comments': data.comments,
                'date_recorded': datetime.now()
            }
            self.add_record_to_existing_therapist_case_key(key_name='treatment_plan',
                                                           data_to_record=recommendation,
                                                           patient_id=data.patient_id,
                                                           case_id=data.case_id)

            mental_health_case = self.get_therapy_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            return {'msg': 'Treatment plan recorded',
                    'case_details': mental_health_case['data']}

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409, detail=f'Error occurred as {str(e)} while recording treatment plan.')

    def update_treatment_plan(self, data: TherapyTreatmentPlan, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')

            self.is_valid_therapy_case_conditions(patient_id=str(data.patient_id), case_id=str(data.case_id))

            recommendation = {
                'comments': data.comments,
                'date_recorded': datetime.now()
            }

            self.update_wrapper(key_name='treatment_plan', data_to_update=recommendation,
                                patient_id=data.patient_id,
                                case_id=data.case_id)

            mental_health_case = self.get_therapy_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            return {'msg': 'Last record of treatment plan updated',
                    'case_details': mental_health_case['data']}

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409, detail=f'Error occurred as {str(e)} while updating treatment plan.')

    def update_treatment_plan_comment(self, data: TherapyTreatmentPlanComment, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')

            self.is_valid_therapy_case_conditions(patient_id=str(data.patient_id), case_id=str(data.case_id))

            self.mongo_db['UserCollection'].update_one(
                {"userid": data.patient_id, "therapist_prescriptions.case_id": data.case_id},
                {"$set": {
                    "therapist_prescriptions.$[].treatment_plan.all_records.$[].treatment_plan_data.$[elem].comments":
                        data.comments,

                }
                },
                upsert=False,
                array_filters=[{"elem.plan_id": data.treatment_plan_id}]

            )
            get_last_record = self.get_updated_therapy_case_record(patient_id=data.patient_id, case_id=data.case_id)

            last_record = get_last_record['treatment_plan']['all_records'][-1]
            self.mongo_db['UserCollection'].find_one_and_update(
                {"userid": data.patient_id, "therapist_prescriptions.case_id": data.case_id},
                {
                    "$set": {
                        "therapist_prescriptions.$[case].treatment_plan.current_record.treatment_plan_data":
                            last_record[
                                'treatment_plan_data']
                    }
                },
                upsert=False,
                array_filters=[{"case.case_id": data.case_id}])
            mental_health_case = self.get_therapy_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            return {'msg': 'Comment of treatment plan id updated',
                    'case_details': mental_health_case['data']}

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409, detail=f'Error occurred as {str(e)} while updating treatment plan.')

    def delete_treatment_plan(self, data: CaseElement, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')

            self.is_valid_therapy_case_conditions(patient_id=str(data.patient_id), case_id=str(data.case_id))

            self.delete_data_from_mongo_case(key_name='treatment_plan',
                                             patient_id=data.patient_id,
                                             case_id=data.case_id)

            mental_health_case = self.get_therapy_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            return {'msg': 'Last record of treatment plan deleted',
                    'case_details': mental_health_case['data']}

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409, detail=f'Error occurred as {str(e)} while deleting treatment plan')

    def record_treatment_plan_response(self, data: TreatmentPlanResponse, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')

            self.is_valid_therapy_case_conditions(patient_id=str(data.patient_id), case_id=str(data.case_id))

            treatment_plan_resp_data = {
                'response': data.response,
                'date_recorded': datetime.now()
            }
            self.add_record_to_existing_therapist_case_key(key_name='treatment_plan_response',
                                                           data_to_record=treatment_plan_resp_data,
                                                           patient_id=data.patient_id,
                                                           case_id=data.case_id)

            mental_health_case = self.get_therapy_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            return {'msg': 'Treatment plan response recorded',
                    'case_details': mental_health_case['data']}

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409,
                                detail=f'Error occurred as {str(e)} while recording treatment plan response.')

    def update_treatment_plan_response(self, data: TreatmentPlanResponse, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')

            self.is_valid_therapy_case_conditions(patient_id=str(data.patient_id), case_id=str(data.case_id))

            treatment_plan_resp_data = {
                'response': data.response,
                'date_recorded': datetime.now()
            }

            self.update_wrapper(key_name='treatment_plan_response', data_to_update=treatment_plan_resp_data,
                                patient_id=data.patient_id,
                                case_id=data.case_id)

            mental_health_case = self.get_therapy_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            return {'msg': 'Last record of treatment plan response updated',
                    'case_details': mental_health_case['data']}

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409,
                                detail=f'Error occurred as {str(e)} while updating treatment plan response.')

    def delete_treatment_plan_response(self, data: CaseElement, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')

            self.is_valid_therapy_case_conditions(patient_id=str(data.patient_id), case_id=str(data.case_id))

            self.delete_data_from_mongo_case(key_name='treatment_plan_response',
                                             patient_id=data.patient_id,
                                             case_id=data.case_id)

            mental_health_case = self.get_therapy_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            return {'msg': 'Last record of treatment plan response deleted',
                    'case_details': mental_health_case['data']}

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409, detail=f'Error occurred as {str(e)} while deleting assessment')

    def record_therapy_recommendation(self, data: TherapyRecommendationAdd, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')
            self.is_valid_therapy_case_conditions(patient_id=str(data.patient_id), case_id=str(data.case_id))

            therapy_suggested = []
            for tr in data.therapy_recommendation:
                therapy_suggested.append(dict(tr))

            therapy_data = {
                'comments': data.comments,
                'suggested_therapy': therapy_suggested,
                'date_recorded': datetime.now()
            }
            self.add_record_to_existing_therapist_case_key(key_name='therapy_recommendation',
                                                           data_to_record=therapy_data,
                                                           patient_id=data.patient_id,
                                                           case_id=data.case_id)

            mental_health_case = self.get_therapy_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            return {'msg': 'Therapy recommendation recorded',
                    'case_details': mental_health_case['data']}

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409,
                                detail=f'Error occurred as {str(e)} while recording therapy recommendation.')

    def update_therapy_recommendation(self, data: TherapyRecommendationAdd, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')

            self.is_valid_therapy_case_conditions(patient_id=str(data.patient_id), case_id=str(data.case_id))

            therapy_suggested = []
            for tr in data.therapy_recommendation:
                therapy_suggested.append(dict(tr))

            therapy_data = {
                'comments': data.comments,
                'suggested_therapy': therapy_suggested,
                'date_recorded': datetime.now()
            }

            self.update_wrapper(key_name='therapy_recommendation', data_to_update=therapy_data,
                                patient_id=data.patient_id,
                                case_id=data.case_id)

            mental_health_case = self.get_therapy_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            return {'msg': 'Last record of therapy recommendation updated',
                    'case_details': mental_health_case['data']}

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409,
                                detail=f'Error occurred as {str(e)} while updating therapy recommendation.')

    def delete_therapy_recommendation(self, data: CaseElement, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')

            self.is_valid_therapy_case_conditions(patient_id=str(data.patient_id), case_id=str(data.case_id))

            self.delete_data_from_mongo_case(key_name='therapy_recommendation',
                                             patient_id=data.patient_id,
                                             case_id=data.case_id)

            mental_health_case = self.get_therapy_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            return {'msg': 'Last record of therapy_recommendation deleted',
                    'case_details': mental_health_case['data']}

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409,
                                detail=f'Error occurred as {str(e)} while deleting therapy recommendation')

    def record_follow_up(self, data: FollowUp, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')

            self.is_valid_therapy_case_conditions(patient_id=str(data.patient_id), case_id=str(data.case_id))
            self.validation_follow_up(data=data)

            follow_up = {
                'objectives': data.objectives,
                'next_appointment': data.next_appointment,
                'appointment_type': data.appointment_type,
                'ayoo_check_in_days': data.ayoo_check_in_days,
                'date_recorded': datetime.now()
            }
            self.add_record_to_existing_therapist_case_key(key_name='follow_up',
                                                           data_to_record=follow_up,
                                                           patient_id=data.patient_id,
                                                           case_id=data.case_id)

            mental_health_case = self.get_therapy_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            return {'msg': 'Follow up added',
                    'case_details': mental_health_case['data']}

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409, detail=f'Error occurred as {str(e)} while recording follow up.')

    def update_follow_up(self, data: FollowUp, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')

            self.is_valid_therapy_case_conditions(patient_id=str(data.patient_id), case_id=str(data.case_id))
            self.validation_follow_up(data=data)

            follow_up = {
                'objectives': data.objectives,
                'next_appointment': data.next_appointment,
                'appointment_type': data.appointment_type,
                'ayoo_check_in_days': data.ayoo_check_in_days,
                'date_recorded': datetime.now()
            }

            self.update_wrapper(key_name='follow_up', data_to_update=follow_up, patient_id=data.patient_id,
                                case_id=data.case_id)

            mental_health_case = self.get_therapy_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            return {'msg': 'Last record of follow up updated',
                    'case_details': mental_health_case['data']}

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409, detail=f'Error occurred as {str(e)} while updating follow up.')

    def delete_follow_up(self, data: CaseElement, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')

            self.is_valid_therapy_case_conditions(patient_id=str(data.patient_id), case_id=str(data.case_id))

            self.delete_data_from_mongo_case(key_name='follow_up',
                                             patient_id=data.patient_id,
                                             case_id=data.case_id)

            mental_health_case = self.get_therapy_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            return {'msg': 'Last record of follow up deleted',
                    'case_details': mental_health_case['data']}

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409, detail=f'Error occurred as {str(e)} while deleting follow up.')

    def add_notes_for_doctor(self, data: DoctorNotes, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')

            self.is_valid_therapy_case_conditions(patient_id=str(data.patient_id), case_id=str(data.case_id))
            self.validation_notes(data=data)

            doctor_notes = {
                'note': data.notes,
                'date_recorded': datetime.now()
            }
            self.add_record_to_existing_therapist_case_key(key_name='notes_for_doctor',
                                                           data_to_record=doctor_notes,
                                                           patient_id=data.patient_id,
                                                           case_id=data.case_id)

            mental_health_case = self.get_therapy_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            return {'msg': 'Doctor notes added',
                    'case_details': mental_health_case['data']}

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409, detail=f'Error occurred as {str(e)} while recording doctor notes.')

    def update_notes_for_doctor(self, data: DoctorNotes, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')

            self.is_valid_therapy_case_conditions(patient_id=str(data.patient_id), case_id=str(data.case_id))
            self.validation_notes(data=data)

            mental_health_case = self.get_therapy_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            doctor_notes = {
                'note': data.notes,
                'date_recorded': mental_health_case['data']['notes_for_doctor']['date_recorded']
            }

            self.update_wrapper(key_name='notes_for_doctor', data_to_update=doctor_notes, patient_id=data.patient_id,
                                case_id=data.case_id)

            mental_health_case = self.get_therapy_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            return {'msg': 'Last record of doctor notes updated',
                    'case_details': mental_health_case['data']}

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409, detail=f'Error occurred as {str(e)} while updating doctor notes.')

    def delete_notes_for_doctor(self, data: CaseElement, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')

            self.is_valid_therapy_case_conditions(patient_id=str(data.patient_id), case_id=str(data.case_id))

            self.delete_data_from_mongo_case(key_name='notes_for_doctor',
                                             patient_id=data.patient_id,
                                             case_id=data.case_id)

            mental_health_case = self.get_therapy_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            return {'msg': 'Last record of doctor notes deleted',
                    'case_details': mental_health_case['data']}

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409, detail=f'Error occurred as {str(e)} while deleting doctor notes.')

    def add_client_education(self, data: ClientEducation, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')

            self.is_valid_therapy_case_conditions(patient_id=str(data.patient_id), case_id=str(data.case_id))
            client_education = {
                'client_education': data.client_education,
                'date_recorded': datetime.now()
            }
            self.add_record_to_existing_therapist_case_key(key_name='client_education',
                                                           data_to_record=client_education,
                                                           patient_id=data.patient_id,
                                                           case_id=data.case_id)

            mental_health_case = self.get_therapy_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            return {'msg': 'Client education added',
                    'case_details': mental_health_case['data']}

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409, detail=f'Error occurred as {str(e)} while recording client education.')

    def update_client_education(self, data: ClientEducation, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')

            self.is_valid_therapy_case_conditions(patient_id=str(data.patient_id), case_id=str(data.case_id))

            client_education = {
                'client_education': data.client_education,
                'date_recorded': datetime.now()
            }

            self.update_wrapper(key_name='client_education',
                                data_to_update=client_education,
                                patient_id=data.patient_id,
                                case_id=data.case_id)

            mental_health_case = self.get_therapy_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            return {'msg': 'Last record of client education updated',
                    'case_details': mental_health_case['data']}

        except Exception as e:
            # logger.info(str(e))
            raise HTTPException(status_code=409, detail=f'Error occurred as {str(e)} while updating client education.')

    def delete_client_education(self, data: CaseElement, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')

            self.is_valid_therapy_case_conditions(patient_id=str(data.patient_id), case_id=str(data.case_id))

            self.delete_data_from_mongo_case(key_name='client_education',
                                             patient_id=data.patient_id,
                                             case_id=data.case_id)

            mental_health_case = self.get_therapy_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            return {'msg': 'Last record of client education deleted',
                    'case_details': mental_health_case['data']}

        except Exception as e:
            # logger.info(str(e))
            raise HTTPException(status_code=409, detail=f'Error occurred as {str(e)} while deleting client education.')

    def add_care_assessment(self, data: CareAssessment, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')

            self.is_valid_therapy_case_conditions(patient_id=str(data.patient_id), case_id=str(data.case_id))

            mental_health_case = self.get_therapy_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            care_assessment_data = mental_health_case['data']['care_assessment'] if 'care_assessment' in \
                                                                                    mental_health_case['data'] else {}

            care_assessment = {}
            for attribute in care_assessment_data:
                care_assessment[attribute] = care_assessment_data[attribute]

            for attribute in data.attributes:
                if attribute not in care_assessment_data:
                    care_assessment[attribute] = []

            self.mongo_db['UserCollection'].find_one_and_update(
                {"userid": data.patient_id,
                 "therapist_prescriptions.case_id": data.case_id},
                {
                    "$set": {
                        'therapist_prescriptions.$.care_assessment': care_assessment
                    }
                }
            )

            mental_health_case = self.get_therapy_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            return {'msg': 'Care assessment added',
                    'case_details': mental_health_case['data']}

        except Exception as e:
            # logger.info(str(e))
            raise HTTPException(status_code=409, detail=f'Error occurred as {str(e)} while recording care assessment.')

    def remove_care_assessment_attribute(self, data: CareAssessmentRemoveAttribute, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')

            self.is_valid_therapy_case_conditions(patient_id=str(data.patient_id), case_id=str(data.case_id))

            mental_health_case = self.get_therapy_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            care_assessment_data = mental_health_case['data']['care_assessment'] if 'care_assessment' in \
                                                                                    mental_health_case['data'] else {}

            care_assessment = {}
            for attribute in care_assessment_data:
                care_assessment[attribute] = care_assessment_data[attribute]

            for attribute in data.attributes:
                if attribute in care_assessment_data:
                    del care_assessment[attribute]

            self.mongo_db['UserCollection'].find_one_and_update(
                {"userid": data.patient_id,
                 "therapist_prescriptions.case_id": data.case_id},
                {
                    "$set": {
                        'therapist_prescriptions.$.care_assessment': care_assessment
                    }
                }
            )

            mental_health_case = self.get_therapy_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            return {'msg': 'Care assessment removed',
                    'case_details': mental_health_case['data']}

        except Exception as e:
            # logger.info(str(e))
            raise HTTPException(status_code=409, detail=f'Error occurred as {str(e)} while deleting care assessment.')

    def record_case_summary(self, data: CaseSummary, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')

            self.is_valid_therapy_case_conditions(patient_id=str(data.patient_id), case_id=str(data.case_id))

            case_summary = {
                'notes_for_patients': data.notes_for_patients,
                'date_recorded': datetime.now()
            }
            self.add_record_to_existing_therapist_case_key(key_name='case_summary',
                                                           data_to_record=case_summary,
                                                           patient_id=data.patient_id,
                                                           case_id=data.case_id)

            # close case
            existing_case_type = self.get_mental_health_case_type(patient_id=data.patient_id, case_id=data.case_id)
            if existing_case_type is None:
                raise Exception('The case id provided is not a mental health case id')

            mental_health_case = self.get_therapy_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            self.close_case(patient_id=data.patient_id, case_id=data.case_id, prescription_type=existing_case_type)

            mental_health_case['data']['is_open'] = False

            return {'msg': 'Case summary added',
                    'case_details': mental_health_case['data']}

        except Exception as e:
            # logger.info(str(e))
            raise HTTPException(status_code=409, detail=f'Error occurred as {str(e)} while recording case summary.')

    def update_case_summary(self, data: CaseSummary, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')

            self.is_valid_therapy_case_conditions(patient_id=str(data.patient_id), case_id=str(data.case_id))

            case_summary = {
                'notes_for_patients': data.notes_for_patients,
                'date_recorded': datetime.now()
            }

            self.update_wrapper(key_name='case_summary', data_to_update=case_summary, patient_id=data.patient_id,
                                case_id=data.case_id)

            mental_health_case = self.get_therapy_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            return {'msg': 'Last record of case summary updated',
                    'case_details': mental_health_case['data']}

        except Exception as e:
            # logger.info(str(e))
            raise HTTPException(status_code=409, detail=f'Error occurred as {str(e)} while updating case summary.')

    def delete_case_summary(self, data: CaseElement, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')

            self.is_valid_therapy_case_conditions(patient_id=str(data.patient_id), case_id=str(data.case_id))

            self.delete_data_from_mongo_case(key_name='case_summary',
                                             patient_id=data.patient_id,
                                             case_id=data.case_id)

            mental_health_case = self.get_therapy_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            return {'msg': 'Last record of case summary deleted',
                    'case_details': mental_health_case['data']}

        except Exception as e:
            # logger.info(str(e))
            raise HTTPException(status_code=409, detail=f'Error occurred as {str(e)} while deleting case summary.')

    def therapist_prescription_submit(self, data: CaseElement1, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')

            self.is_valid_therapy_case_conditions(patient_id=str(data.patient_id), case_id=str(data.case_id))
            self.generate_patients_therapy_prescription(data=data)

            # SEND NOTIFICATION

            mental_health_case = self.get_therapy_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            if mental_health_case['data']['notes']:
                frbs_ctrl = FireBaseNotificationController(db=self.db, mongo=self.mongo)

                patient_details = self.get_patient_details(patientid=data.patient_id)

                prescription_notif = PrescriptionEvent(case_id=data.case_id,
                                                       patient_id=data.patient_id,
                                                       patient_name=patient_details['firstname'],
                                                       upload_date=datetime.now(),
                                                       remarks="None")
                if data.preview in ['true', 'TRUE', 'True', True]:
                    frbs_ctrl.prescription_upload(prescription_event=prescription_notif)
            else:
                raise Exception('Add notes before final submission')

            return {'msg': 'Prescription uploaded',
                    'case_details': mental_health_case['data']}

        except Exception as e:
            # logger.info(str(e))
            raise HTTPException(status_code=409,
                                detail=f'Error occurred as {str(e)} while submitting therapist prescription.')


class MedicalHealthController(MentalHealthController):

    def __init__(self, db: scoped_session, mongo=None):
        super().__init__(db=db, mongo=mongo)

    def check_mongo_case_array_length(self, key_name: str, patient_id: str, case_id: str):
        try:
            mental_health_case = self.get_updated_medical_health_case_record(patient_id=patient_id, case_id=case_id)
            return len(mental_health_case[f'{key_name}']['all_records'])

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise Exception(str(e))

    def delete_medical_health_chief_complain_single_entity(self, data: DeleteChiefComplain, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')
            self.is_valid_medical_health_case_conditions(patient_id=str(data.patient_id), case_id=str(data.case_id))
            i = int(data.index)
            # logger.info(i)
            res = self.mongo_db['UserCollection'].find_one(
                {"medical_health_prescriptions.case_id": data.case_id})
            for pres in res['medical_health_prescriptions']:

                if pres['case_id'] == data.case_id:
                    al = len(pres['chief_complain']['all_records']) - 1
                    m = len(pres['chief_complain']['all_records'][al]['chief_complain'])
                    # logger.info(m)
                    if m == 0:
                        raise Exception("chief complaint is empty")
                    if al == 0:
                        self.delete_data_from_mongo_case(key_name='chief_complain',
                                                         patient_id=data.patient_id,
                                                         case_id=data.case_id)

                    elif al < 0:
                        return

                    else:

                        del pres['chief_complain']['all_records'][al]['chief_complain'][i]
                        update = pres['chief_complain']['all_records'][al]

                        self.update_wrapper(key_name='chief_complain', data_to_update=update,
                                            patient_id=data.patient_id,
                                            case_id=data.case_id)
                        break

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409, detail=str(e))

    def pop_a_record_from_mongo_case_array(self, key_name: str, patient_id: str, case_id: str):
        try:
            self.mongo_db['UserCollection'].find_one_and_update({"userid": patient_id,
                                                                 "medical_health_prescriptions.case_id": case_id}, {
                                                                    "$pop": {
                                                                        f'medical_health_prescriptions.$.{key_name}.all_records': 1
                                                                    }
                                                                })

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise Exception(str(e))

    def delete_data_from_mongo_case(self, key_name: str, patient_id: str, case_id: str):
        try:
            get_array_length = self.check_mongo_case_array_length(key_name=key_name,
                                                                  patient_id=patient_id,
                                                                  case_id=case_id)
            if get_array_length == 0:
                raise Exception('No data found')

            is_update_allowed = self.check_if_update_allowed(patient_id=patient_id, case_id=case_id, key_name=key_name)
            if is_update_allowed is not True:
                raise Exception('Deletion not allowed')

            self.pop_a_record_from_mongo_case_array(key_name=key_name, patient_id=patient_id, case_id=case_id)

            if get_array_length == 1:

                self.mongo_db['UserCollection'].find_one_and_update(
                    {"userid": patient_id,
                     "medical_health_prescriptions.case_id": case_id},
                    {
                        "$set": {
                            f'medical_health_prescriptions.$.{key_name}.current_record':
                                {}
                        }
                    }
                )
            else:
                mental_health_case = self.get_updated_medical_health_case_record(patient_id=patient_id, case_id=case_id)
                self.mongo_db['UserCollection'].find_one_and_update(
                    {"userid": patient_id,
                     "medical_health_prescriptions.case_id": case_id},
                    {
                        "$set": {
                            f'medical_health_prescriptions.$.{key_name}.current_record':
                                mental_health_case[f'{key_name}']['all_records'][-1]
                        }
                    }
                )

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise Exception(str(e))

    def update_wrapper(self, key_name: str, data_to_update: {}, patient_id: str, case_id: str):
        data_exists = self.check_mongo_case_array_length(key_name=key_name,
                                                         patient_id=patient_id,
                                                         case_id=case_id)

        if data_exists == 0:
            raise Exception('No data found to update')

        is_update_allowed = self.check_if_update_allowed(patient_id=patient_id, case_id=case_id, key_name=key_name)
        if is_update_allowed is not True:
            raise Exception('Update not allowed')

        self.pop_a_record_from_mongo_case_array(key_name=key_name,
                                                patient_id=patient_id,
                                                case_id=case_id)

        self.add_record_to_existing_medical_health_case_key(key_name=key_name,
                                                            data_to_record=data_to_update,
                                                            patient_id=patient_id,
                                                            case_id=case_id)

    def add_chief_complain(self, data: AddChiefComplain, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')

            self.is_valid_medical_health_case_conditions(patient_id=str(data.patient_id), case_id=str(data.case_id))
            self.validation_chief_complain(data=data)

            li = []
            for chief in data.chief_complain:
                chief = dict(chief)
                obj = {'complain_text': chief['complain_text'], 'onset': chief['onset']}
                li.append(obj)
            current_complain = {
                'chief_complain': li,
                'date_recorded': datetime.now()
            }

            self.add_record_to_existing_medical_health_case_key(key_name='chief_complain',
                                                                data_to_record=current_complain,
                                                                patient_id=data.patient_id,
                                                                case_id=data.case_id)

            mental_health_case = self.get_medical_health_case_details(data=GetMentalHealthCaseDetails(
                patient_id=data.patient_id, case_id=data.case_id))
            return {'msg': 'Chief complain recorded',
                    'case_details': mental_health_case['data']}

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409, detail=str(e))

    def update_latest_chief_complain(self, data: AddChiefComplain, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')

            self.is_valid_medical_health_case_conditions(patient_id=data.patient_id, case_id=data.case_id)
            self.validation_chief_complain(data=data)
            li = []
            for chief in data.chief_complain:
                chief = dict(chief)
                obj = {'complain_text': chief['complain_text'], 'onset': chief['onset']}
                li.append(obj)
            current_record = {
                'chief_complain': li,
                'date_recorded': datetime.now()
            }

            self.update_wrapper(key_name='chief_complain', data_to_update=current_record, patient_id=data.patient_id,
                                case_id=data.case_id)

            mental_health_case = self.get_medical_health_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            return {'msg': 'Chief complain updated',
                    'case_details': mental_health_case['data']}
        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409, detail=f'Error occurred as {str(e)} while updating chief complain')

    def delete_latest_chief_complain(self, data: GetMentalHealthCaseDetails, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')

            self.is_valid_medical_health_case_conditions(patient_id=data.patient_id, case_id=data.case_id)

            self.delete_data_from_mongo_case(key_name='chief_complain',
                                             patient_id=data.patient_id,
                                             case_id=data.case_id)

            mental_health_case = self.get_medical_health_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            return {'msg': 'Chief complain deleted',
                    'case_details': mental_health_case['data']}

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409, detail=f'Error occurred as {str(e)} while deleting chief complain')

    def record_sleep_and_appetite(self, data: SleepAndAppetite, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')

            self.is_valid_medical_health_case_conditions(patient_id=str(data.patient_id), case_id=str(data.case_id))

            sleep_appetite = {
                'sleep': data.sleep,
                'appetite': data.appetite,
                data.other.key_name if data.other else 'other_data': data.other.value if data.other else None,
                'date_recorded': datetime.now()
            }
            self.add_record_to_existing_medical_health_case_key(key_name='sleep_appetite',
                                                                data_to_record=sleep_appetite,
                                                                patient_id=data.patient_id,
                                                                case_id=data.case_id)

            mental_health_case = self.get_medical_health_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            return {'msg': 'Sleep and appetite recorded',
                    'case_details': mental_health_case['data']}

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409,
                                detail=f'Error occurred as {str(e)} while recording sleep and appetite.')

    def update_sleep_appetite(self, data: SleepAndAppetite, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')

            self.is_valid_medical_health_case_conditions(patient_id=str(data.patient_id), case_id=str(data.case_id))

            sleep_appetite = {
                'sleep': data.sleep,
                'appetite': data.appetite,
                data.other.key_name if data.other else 'other_data': data.other.value if data.other else None,
                'date_recorded': datetime.now()
            }

            self.update_wrapper(key_name='sleep_appetite', data_to_update=sleep_appetite, patient_id=data.patient_id,
                                case_id=data.case_id)

            mental_health_case = self.get_medical_health_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            return {'msg': 'Last record of sleep and appetite updated',
                    'case_details': mental_health_case['data']}

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409,
                                detail=f'Error occurred as {str(e)} while updating sleep and appetite.')

    def delete_sleep_appetite(self, data: CaseElement, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')

            self.is_valid_medical_health_case_conditions(patient_id=str(data.patient_id), case_id=str(data.case_id))

            self.delete_data_from_mongo_case(key_name='sleep_appetite',
                                             patient_id=data.patient_id,
                                             case_id=data.case_id)

            mental_health_case = self.get_medical_health_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            return {'msg': 'Last record of sleep and appetite deleted',
                    'case_details': mental_health_case['data']}

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409,
                                detail=f'Error occurred as {str(e)} while deleting Sleep and appetite.')

    def add_doctor_notes(self, data: DoctorNotes, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')

            self.is_valid_medical_health_case_conditions(patient_id=str(data.patient_id), case_id=str(data.case_id))
            self.validation_notes(data=data)
            mental_health_case = self.get_medical_health_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            get_session = int(mental_health_case['data']['doctor_notes']['session']) if mental_health_case['data'][
                'doctor_notes'] else 0
            session_no = get_session + 1

            doctor_notes = {
                'note': data.notes,
                'session': session_no,
                'date_recorded': datetime.now()
            }
            self.add_record_to_existing_medical_health_case_key(key_name='doctor_notes',
                                                                data_to_record=doctor_notes,
                                                                patient_id=data.patient_id,
                                                                case_id=data.case_id)

            mental_health_case = self.get_medical_health_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            return {'msg': 'Doctor notes added',
                    'case_details': mental_health_case['data']}

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409, detail=f'Error occurred as {str(e)} while recording doctor notes.')

    def update_doctor_notes(self, data: DoctorNotes, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')

            self.is_valid_medical_health_case_conditions(patient_id=str(data.patient_id), case_id=str(data.case_id))
            self.validation_notes(data=data)

            mental_health_case = self.get_medical_health_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            session_no = int(mental_health_case['data']['doctor_notes']['session'])

            doctor_notes = {
                'note': data.notes,
                'session': session_no,
                'date_recorded': mental_health_case['data']['doctor_notes']['date_recorded']
            }

            self.update_wrapper(key_name='doctor_notes', data_to_update=doctor_notes, patient_id=data.patient_id,
                                case_id=data.case_id)

            mental_health_case = self.get_medical_health_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            return {'msg': 'Last record of doctor notes updated',
                    'case_details': mental_health_case['data']}

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409, detail=f'Error occurred as {str(e)} while updating doctor notes.')

    def delete_doctor_notes(self, data: CaseElement, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')

            self.is_valid_medical_health_case_conditions(patient_id=str(data.patient_id), case_id=str(data.case_id))

            self.delete_data_from_mongo_case(key_name='doctor_notes',
                                             patient_id=data.patient_id,
                                             case_id=data.case_id)

            mental_health_case = self.get_medical_health_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            return {'msg': 'Last record of doctor notes deleted',
                    'case_details': mental_health_case['data']}

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409, detail=f'Error occurred as {str(e)} while deleting doctor notes.')

    def record_substance_use_abuse(self, data: SubstanceUseAdd, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')

            self.is_valid_medical_health_case_conditions(patient_id=str(data.patient_id), case_id=str(data.case_id))
            self.validation_substance_use_abuse(data=data)
            substance_use = []
            for s in data.substances_data:
                substance_use.append(dict(s))

            substance_use_data = {
                'substance_data': substance_use,
                'date_recorded': datetime.now()
            }
            self.add_record_to_existing_medical_health_case_key(key_name='substances',
                                                                data_to_record=substance_use_data,
                                                                patient_id=data.patient_id,
                                                                case_id=data.case_id)
            mental_health_case = self.get_medical_health_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            return {'msg': 'Substance usage recorded',
                    'case_details': mental_health_case['data']}

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409,
                                detail=f'Error occurred as {str(e)} while recording substance use-abuse.')

    def update_substance_use_abuse(self, data: SubstanceUseAdd, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')

            self.is_valid_medical_health_case_conditions(patient_id=str(data.patient_id), case_id=str(data.case_id))
            self.validation_substance_use_abuse(data=data)

            substance_use = []
            for s in data.substances_data:
                substance_use.append(dict(s))

            substance_use_data = {
                'substance_data': substance_use,
                'date_recorded': datetime.now()
            }

            self.update_wrapper(key_name='substances', data_to_update=substance_use_data, patient_id=data.patient_id,
                                case_id=data.case_id)

            mental_health_case = self.get_medical_health_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            return {'msg': 'Last record of substance use-abuse updated',
                    'case_details': mental_health_case['data']}

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409,
                                detail=f'Error occurred as {str(e)} while updating substance use-abuse.')

    def delete_substance_use_abuse(self, data: CaseElement, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')

            self.is_valid_medical_health_case_conditions(patient_id=str(data.patient_id), case_id=str(data.case_id))

            self.delete_data_from_mongo_case(key_name='substances',
                                             patient_id=data.patient_id,
                                             case_id=data.case_id)

            mental_health_case = self.get_medical_health_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            return {'msg': 'Last record of substance use-abuse deleted',
                    'case_details': mental_health_case['data']}

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409,
                                detail=f'Error occurred as {str(e)} while deleting substance use-abuse')

    def record_assessment(self, data: Assessment, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')

            self.is_valid_medical_health_case_conditions(patient_id=str(data.patient_id), case_id=str(data.case_id))
            self.validation_assessment(data=data)

            p_d = []
            for pd in data.provisional_diagnosis:
                p_d.append(dict(pd))

            assessment_data = {
                'provisional_diagnosis': p_d,
                'rule_out_or_remarks': data.rule_out_or_remarks,
                'date_recorded': datetime.now()
            }
            self.add_record_to_existing_medical_health_case_key(key_name='assessment',
                                                                data_to_record=assessment_data,
                                                                patient_id=data.patient_id,
                                                                case_id=data.case_id)

            mental_health_case = self.get_medical_health_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            return {'msg': 'Assessment recorded',
                    'case_details': mental_health_case['data']}

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409, detail=f'Error occurred as {str(e)} while recording assessment.')

    def update_assessment(self, data: Assessment, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')

            self.is_valid_medical_health_case_conditions(patient_id=str(data.patient_id), case_id=str(data.case_id))
            self.validation_assessment(data=data)

            p_d = []
            for pd in data.provisional_diagnosis:
                p_d.append(dict(pd))

            assessment_data = {
                'provisional_diagnosis': p_d,
                'rule_out_or_remarks': data.rule_out_or_remarks,
                'date_recorded': datetime.now()
            }

            self.update_wrapper(key_name='assessment', data_to_update=assessment_data, patient_id=data.patient_id,
                                case_id=data.case_id)

            mental_health_case = self.get_medical_health_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            return {'msg': 'Last record of assessment updated',
                    'case_details': mental_health_case['data']}

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409, detail=f'Error occurred as {str(e)} while updating assessment.')

    def delete_assessment(self, data: CaseElement, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')

            self.is_valid_medical_health_case_conditions(patient_id=str(data.patient_id), case_id=str(data.case_id))

            self.delete_data_from_mongo_case(key_name='assessment',
                                             patient_id=data.patient_id,
                                             case_id=data.case_id)

            mental_health_case = self.get_medical_health_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            return {'msg': 'Last record of assessment deleted',
                    'case_details': mental_health_case['data']}

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409, detail=f'Error occurred as {str(e)} while deleting assessment')

    def record_lab_tests(self, data: LabTest, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')

            self.is_valid_medical_health_case_conditions(patient_id=str(data.patient_id), case_id=str(data.case_id))
            self.validation_lab_tests(data=data)

            lab_tests = []
            for lt in data.lab_tests:
                # if lt == "":
                #     raise Exception('Test name cannot be blank')

                lab_tests.append({
                    'test_id': str(uuid.uuid4()),
                    'test_name': lt,
                    'test_report_link': None,
                    'test_date': None
                })

            lab_tests_data = {
                'test_details': lab_tests,
                'date_recorded': datetime.now()
            }
            self.add_record_to_existing_medical_health_case_key(key_name='lab_tests',
                                                                data_to_record=lab_tests_data,
                                                                patient_id=data.patient_id,
                                                                case_id=data.case_id)

            mental_health_case = self.get_medical_health_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            return {'msg': 'Lab tests recorded',
                    'case_details': mental_health_case['data']}

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409, detail=f'Error occurred as {str(e)} while recording lab tests.')

    def update_lab_tests(self, data: LabTest, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')

            self.is_valid_medical_health_case_conditions(patient_id=str(data.patient_id), case_id=str(data.case_id))
            self.validation_lab_tests(data=data)

            mental_health_case = self.get_medical_health_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            existing_lab_test_data = mental_health_case['data']['lab_tests']
            if existing_lab_test_data is None:
                raise Exception('No lab test found to update')

            lab_test_data = mental_health_case['data']['lab_tests']
            if lab_test_data is None:
                raise Exception('No lab test found to update')
            existing_lab_test_data = lab_test_data['test_details']

            lab_tests = []
            for lt in data.lab_tests:
                # Check if the test name exists in the existing_lab_test_data
                matched_test = next((test for test in existing_lab_test_data if test['test_name'] == lt), None)
                if matched_test:
                    # If the test exists, update the test_report_link and test_date using the previous data
                    lab_tests.append({
                        'test_id': matched_test['test_id'],
                        'test_name': lt,
                        'test_report_link': matched_test.get('test_report_link'),
                        'test_date': matched_test.get('test_date')
                    })
                else:
                    # If the test does not exist, add it with default values for test_report_link and test_date
                    lab_tests.append({
                        'test_id': str(uuid.uuid4()),
                        'test_name': lt,
                        'test_report_link': None,
                        'test_date': None
                    })

            lab_tests_data = {
                'test_details': lab_tests,
                'date_recorded': datetime.now()
            }

            self.update_wrapper(key_name='lab_tests', data_to_update=lab_tests_data, patient_id=data.patient_id,
                                case_id=data.case_id)

            mental_health_case = self.get_medical_health_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            return {'msg': 'Last record of lab tests updated',
                    'case_details': mental_health_case['data']}

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409, detail=f'Error occurred as {str(e)} while updating lab tests.')

    def delete_lab_tests(self, data: CaseElement, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')

            self.is_valid_medical_health_case_conditions(patient_id=str(data.patient_id), case_id=str(data.case_id))

            self.delete_data_from_mongo_case(key_name='lab_tests',
                                             patient_id=data.patient_id,
                                             case_id=data.case_id)

            mental_health_case = self.get_medical_health_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            return {'msg': 'Last record of lab tests deleted',
                    'case_details': mental_health_case['data']}

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409, detail=f'Error occurred as {str(e)} while deleting lab tests')

    def record_medications(self, data: MentalHealthMedication, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')

            self.is_valid_medical_health_case_conditions(patient_id=str(data.patient_id), case_id=str(data.case_id))
            self.validation_medicines(data=data)

            meds = []
            for m in data.medications:
                medication = dict(m)
                medication['frequency'] = dict(m.frequency)  # Convert MedsFrequency object to dictionary
                if m.sos is False:
                    end_date = datetime.strptime(medication['start_date'], "%Y-%m-%d") + timedelta(
                        days=medication['duration_in_days'])
                    medication['end_date'] = end_date.strftime("%Y-%m-%d")
                else:
                    medication['end_date'] = None
                meds.append(medication)

            meds = sorted(meds, key=lambda x: (x['start_date'], x['duration_in_days']))

            medications_data = {
                'meds': meds,
                'date_recorded': datetime.now()
            }

            self.add_record_to_existing_medical_health_case_key(key_name='medications',
                                                                data_to_record=medications_data,
                                                                patient_id=data.patient_id,
                                                                case_id=data.case_id)

            mental_health_case = self.get_medical_health_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            return {'msg': 'Medication recorded',
                    'case_details': mental_health_case['data']}

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409, detail=f'Error occurred as {str(e)} while recording medications.')

    def update_medications(self, data: MentalHealthMedication, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')

            self.is_valid_medical_health_case_conditions(patient_id=str(data.patient_id), case_id=str(data.case_id))
            self.validation_medicines(data=data)

            meds = []

            for m in data.medications:
                medication = dict(m)
                medication['frequency'] = dict(m.frequency)  # Convert MedsFrequency object to dictionary
                if m.sos is False:
                    end_date = datetime.strptime(medication['start_date'], "%Y-%m-%d") + timedelta(
                        days=medication['duration_in_days'])
                    medication['end_date'] = end_date.strftime("%Y-%m-%d")
                else:
                    medication['end_date'] = None
                meds.append(medication)

            meds = sorted(meds, key=lambda x: (x['start_date'], x['duration_in_days']))

            medications_data = {
                'meds': meds,
                'date_recorded': datetime.now()
            }

            self.update_wrapper(key_name='medications', data_to_update=medications_data,
                                patient_id=data.patient_id,
                                case_id=data.case_id)

            mental_health_case = self.get_medical_health_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            return {'msg': 'Last record of medications updated',
                    'case_details': mental_health_case['data']}

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409, detail=f'Error occurred as {str(e)} while updating medications.')

    def delete_medications(self, data: CaseElement, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')

            self.is_valid_medical_health_case_conditions(patient_id=str(data.patient_id), case_id=str(data.case_id))

            self.delete_data_from_mongo_case(key_name='medications',
                                             patient_id=data.patient_id,
                                             case_id=data.case_id)

            mental_health_case = self.get_medical_health_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            return {'msg': 'Last record of medications deleted',
                    'case_details': mental_health_case['data']}

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409, detail=f'Error occurred as {str(e)} while deleting medications')

    def record_work_up_plan(self, data: WorkUpPlanAdd, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')
            self.is_valid_medical_health_case_conditions(patient_id=str(data.patient_id), case_id=str(data.case_id))
            self.validation_treatment_plan(data=TreatmentPlanAdd(patient_id=data.patient_id,
                                                                 case_id=data.case_id,
                                                                 treatment_plan=data.work_up_plan,
                                                                 comments=data.comments))

            work_up_plan = []
            for tp in data.work_up_plan:
                dict_tp = dict(tp)
                dict_tp['plan_id'] = str(uuid.uuid4())
                work_up_plan.append(dict_tp)

            work_up_plan_data = {
                'work_up_plan': work_up_plan,
                'comments': data.comments,
                'date_recorded': datetime.now()
            }

            self.add_record_to_existing_medical_health_case_key(key_name='work_up_plan',
                                                                data_to_record=work_up_plan_data,
                                                                patient_id=data.patient_id,
                                                                case_id=data.case_id)

            mental_health_case = self.get_medical_health_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            return {'msg': 'work up plan recorded',
                    'case_details': mental_health_case['data']}

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409, detail=f'Error occurred as {str(e)} while recording work up plan.')

    def update_work_up_plan(self, data: WorkUpPlanAdd, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')

            self.is_valid_medical_health_case_conditions(patient_id=str(data.patient_id), case_id=str(data.case_id))
            self.validation_treatment_plan(data=TreatmentPlanAdd(patient_id=data.patient_id,
                                                                 case_id=data.case_id,
                                                                 treatment_plan=data.work_up_plan,
                                                                 comments=data.comments))

            work_up_plan = []
            for tp in data.work_up_plan:
                dict_tp = dict(tp)
                dict_tp['plan_id'] = str(uuid.uuid4())
                work_up_plan.append(dict_tp)

            work_up_plan_data = {
                'work_up_plan': work_up_plan,
                'comments': data.comments,
                'date_recorded': datetime.now()
            }

            self.update_wrapper(key_name='work_up_plan', data_to_update=work_up_plan_data,
                                patient_id=data.patient_id,
                                case_id=data.case_id)

            mental_health_case = self.get_medical_health_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            return {'msg': 'Last record of work up plan updated',
                    'case_details': mental_health_case['data']}

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409, detail=f'Error occurred as {str(e)} while updating work up plan.')

    def delete_work_up_plan(self, data: CaseElement, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')

            self.is_valid_medical_health_case_conditions(patient_id=str(data.patient_id), case_id=str(data.case_id))

            self.delete_data_from_mongo_case(key_name='work_up_plan',
                                             patient_id=data.patient_id,
                                             case_id=data.case_id)

            mental_health_case = self.get_medical_health_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            return {'msg': 'Last record of work up plan deleted',
                    'case_details': mental_health_case['data']}

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409, detail=f'Error occurred as {str(e)} while deleting work up plan')

    def record_follow_up(self, data: FollowUp, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')

            self.is_valid_medical_health_case_conditions(patient_id=str(data.patient_id), case_id=str(data.case_id))
            self.validation_follow_up(data=data)

            follow_up = {
                'objectives': data.objectives,
                'next_appointment': data.next_appointment,
                'appointment_type': data.appointment_type,
                'ayoo_check_in_days': data.ayoo_check_in_days,
                'date_recorded': datetime.now()
            }
            self.add_record_to_existing_medical_health_case_key(key_name='follow_up',
                                                                data_to_record=follow_up,
                                                                patient_id=data.patient_id,
                                                                case_id=data.case_id)

            mental_health_case = self.get_medical_health_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            return {'msg': 'Follow up added',
                    'case_details': mental_health_case['data']}

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409, detail=f'Error occurred as {str(e)} while recording follow up.')

    def update_follow_up(self, data: FollowUp, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')

            self.is_valid_medical_health_case_conditions(patient_id=str(data.patient_id), case_id=str(data.case_id))
            self.validation_follow_up(data=data)

            follow_up = {
                'objectives': data.objectives,
                'next_appointment': data.next_appointment,
                'appointment_type': data.appointment_type,
                'ayoo_check_in_days': data.ayoo_check_in_days,
                'date_recorded': datetime.now()
            }

            self.update_wrapper(key_name='follow_up', data_to_update=follow_up, patient_id=data.patient_id,
                                case_id=data.case_id)

            mental_health_case = self.get_medical_health_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            return {'msg': 'Last record of follow up updated',
                    'case_details': mental_health_case['data']}

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409, detail=f'Error occurred as {str(e)} while updating follow up.')

    def delete_follow_up(self, data: CaseElement, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')

            self.is_valid_medical_health_case_conditions(patient_id=str(data.patient_id), case_id=str(data.case_id))

            self.delete_data_from_mongo_case(key_name='follow_up',
                                             patient_id=data.patient_id,
                                             case_id=data.case_id)

            mental_health_case = self.get_medical_health_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            return {'msg': 'Last record of follow up deleted',
                    'case_details': mental_health_case['data']}

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409, detail=f'Error occurred as {str(e)} while deleting follow up.')

    def add_client_education(self, data: ClientEducation, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')

            self.is_valid_medical_health_case_conditions(patient_id=str(data.patient_id), case_id=str(data.case_id))
            client_education = {
                'client_education': data.client_education,
                'date_recorded': datetime.now()
            }
            self.add_record_to_existing_medical_health_case_key(key_name='client_education',
                                                                data_to_record=client_education,
                                                                patient_id=data.patient_id,
                                                                case_id=data.case_id)

            mental_health_case = self.get_medical_health_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            return {'msg': 'Client education added',
                    'case_details': mental_health_case['data']}

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409, detail=f'Error occurred as {str(e)} while recording client education.')

    def update_client_education(self, data: ClientEducation, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')

            self.is_valid_medical_health_case_conditions(patient_id=str(data.patient_id), case_id=str(data.case_id))

            client_education = {
                'client_education': data.client_education,
                'date_recorded': datetime.now()
            }

            self.update_wrapper(key_name='client_education',
                                data_to_update=client_education,
                                patient_id=data.patient_id,
                                case_id=data.case_id)

            mental_health_case = self.get_medical_health_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            return {'msg': 'Last record of client education updated',
                    'case_details': mental_health_case['data']}

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409, detail=f'Error occurred as {str(e)} while updating client education.')

    def delete_client_education(self, data: CaseElement, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')

            self.is_valid_medical_health_case_conditions(patient_id=str(data.patient_id), case_id=str(data.case_id))

            self.delete_data_from_mongo_case(key_name='client_education',
                                             patient_id=data.patient_id,
                                             case_id=data.case_id)

            mental_health_case = self.get_medical_health_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            return {'msg': 'Last record of client education deleted',
                    'case_details': mental_health_case['data']}

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409, detail=f'Error occurred as {str(e)} while deleting client education.')

    def add_care_assessment(self, data: CareAssessment, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')

            self.is_valid_medical_health_case_conditions(patient_id=str(data.patient_id), case_id=str(data.case_id))

            mental_health_case = self.get_medical_health_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            care_assessment_data = mental_health_case['data']['care_assessment'] if 'care_assessment' in \
                                                                                    mental_health_case['data'] else {}

            care_assessment = {}
            for attribute in care_assessment_data:
                care_assessment[attribute] = care_assessment_data[attribute]

            for attribute in data.attributes:
                if attribute not in care_assessment_data:
                    care_assessment[attribute] = []

            self.mongo_db['UserCollection'].find_one_and_update(
                {"userid": data.patient_id,
                 "medical_health_prescriptions.case_id": data.case_id},
                {
                    "$set": {
                        'medical_health_prescriptions.$.care_assessment': care_assessment
                    }
                }
            )

            mental_health_case = self.get_medical_health_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            return {'msg': 'Care assessment added',
                    'case_details': mental_health_case['data']}

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409, detail=f'Error occurred as {str(e)} while recording care assessment.')

    def remove_care_assessment_attribute(self, data: CareAssessmentRemoveAttribute, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')

            self.is_valid_medical_health_case_conditions(patient_id=str(data.patient_id), case_id=str(data.case_id))

            mental_health_case = self.get_medical_health_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            care_assessment_data = mental_health_case['data']['care_assessment'] if 'care_assessment' in \
                                                                                    mental_health_case['data'] else {}

            care_assessment = {}
            for attribute in care_assessment_data:
                care_assessment[attribute] = care_assessment_data[attribute]

            for attribute in data.attributes:
                if attribute in care_assessment_data:
                    del care_assessment[attribute]

            self.mongo_db['UserCollection'].find_one_and_update(
                {"userid": data.patient_id,
                 "medical_health_prescriptions.case_id": data.case_id},
                {
                    "$set": {
                        'medical_health_prescriptions.$.care_assessment': care_assessment
                    }
                }
            )

            mental_health_case = self.get_medical_health_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            return {'msg': 'Care assessment removed',
                    'case_details': mental_health_case['data']}

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409, detail=f'Error occurred as {str(e)} while deleting care assessment.')

    def record_case_summary(self, data: CaseSummary, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')

            self.is_valid_medical_health_case_conditions(patient_id=str(data.patient_id), case_id=str(data.case_id))

            case_summary = {
                'notes_for_patients': data.notes_for_patients,
                'date_recorded': datetime.now()
            }
            self.add_record_to_existing_medical_health_case_key(key_name='case_summary',
                                                                data_to_record=case_summary,
                                                                patient_id=data.patient_id,
                                                                case_id=data.case_id)

            # close case
            existing_case_type = self.get_mental_health_case_type(patient_id=data.patient_id, case_id=data.case_id)
            if existing_case_type is None:
                raise Exception('The case id provided is not a mental health case id')

            self.close_case(patient_id=data.patient_id, case_id=data.case_id, prescription_type=existing_case_type)

            mental_health_case = self.get_medical_health_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))

            return {'msg': 'Case summary added',
                    'case_details': mental_health_case['data']}

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409, detail=f'Error occurred as {str(e)} while recording case summary.')

    def medical_health_prescription_submit(self, data: CaseElement1, logged_in_user: str):
        try:
            has_write_access = self.has_write_access(logged_in_user)
            if not has_write_access:
                raise Exception('User is not authorized for this action')
            try:
                self.is_valid_medical_health_case_conditions(patient_id=str(data.patient_id), case_id=str(data.case_id))
            except Exception as e:
                raise Exception(f'Error occurred as {str(e)} while validating medical health case condition.')
            try:
                self.generate_patients_medical_health_prescription(data=data)
            except Exception as e:
                raise Exception(f'Error occurred as {str(e)} while generating patient medical_health prescription.')
            # SEND NOTIFICATION

            try:
                mental_health_case = self.get_medical_health_case_details(
                    data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))
            except Exception as e:
                raise Exception('Error occurred as {str(e)} while getting medical_health case details.')

            if mental_health_case['data']['doctor_notes']:
                frbs_ctrl = FireBaseNotificationController(db=self.db, mongo=self.mongo)

                patient_details = self.get_patient_details(patientid=data.patient_id)

                prescription_notif = PrescriptionEvent(case_id=data.case_id,
                                                       patient_id=data.patient_id,
                                                       patient_name=patient_details['firstname'],
                                                       upload_date=datetime.now(),
                                                       remarks="None")
                loggers['logger6'].info("upload notification")
                if data.preview in ['true', 'TRUE', 'True', True]:
                    frbs_ctrl.prescription_upload(prescription_event=prescription_notif)
            else:
                raise Exception('Add doctor notes before final submission')

            return {'msg': 'Prescription uploaded',
                    'case_details': mental_health_case['data']}

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            raise HTTPException(status_code=409,
                                detail=f'Error occurred as {str(e)} while submitting medical_health prescription.')

    def med_notif_toggle_switch(self, data: CaseElement):
        try:
            self.is_valid_medical_health_case_conditions(patient_id=str(data.patient_id), case_id=str(data.case_id))

            mental_health_case = self.get_medical_health_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))
            toggle = False if mental_health_case['medicine_reminder'] else True

            self.mongo_db['UserCollection'].find_one_and_update(
                {"userid": data.patient_id,
                 "medical_health_prescriptions.case_id": data.case_id},
                {
                    "$set": {
                        'medical_health_prescriptions.$.medicine_reminder': toggle
                    }
                }
            )
            mental_health_case = self.get_medical_health_case_details(
                data=GetMentalHealthCaseDetails(patient_id=data.patient_id, case_id=data.case_id))
            return {
                       'msg': 'Toggle switched',
                       'case_details': mental_health_case['data']
                   }, 'Toggle switched'
        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            # logger.info(str(e))
            return None, f'Error occurred as {str(e)} while toggling notification for medicine.'


class MedicalHistoryController(MedicalHealthController):
    def __init__(self, db: scoped_session, mongo=None):
        super().__init__(db=db, mongo=mongo)

    def add_key_value_to_mongo(self, key_to_add: str, patient_id: str, data: {}):
        try:
            self.mongo_db['UserCollection'].find_one_and_update(
                {"userid": patient_id},
                {
                    "$push": {
                        f'medical_history.{key_to_add}': data
                    }
                }
            )

        except Exception as e:
            raise Exception(str(e))

    def update_key_value_to_mongo(self, key_to_update: str, key_id: str, patient_id: str, data: {}):
        try:
            update_query = {
                "$set": {
                    f"medical_history.{key_to_update}.$[elem]": data
                }
            }
            array_filters = [{"elem.id": key_id}]
            self.mongo_db['UserCollection'].update_one(
                {"userid": patient_id},
                update_query,
                array_filters=array_filters
            )

            return {'msg': 'Medical history updated successfully'}
        except Exception as e:
            raise Exception(str(e))

    def get_medical_history_of_patient(self, patient_id: str):
        try:

            user_data = self.mongo_db['UserCollection'].find_one({"userid": patient_id})
            medical_history = user_data.get('medical_history', {})
            medical_history.get('vitals', []).sort(key=lambda x: x['created_at'], reverse=True)
            medical_history.get('known_allergies', []).sort(key=lambda x: x['created_at'], reverse=True)
            medical_history.get('prior_medical_history', []).sort(key=lambda x: x['created_at'], reverse=True)
            medical_history.get('current_or_past_medication', []).sort(key=lambda x: x['created_at'], reverse=True)
            medical_history.get('family_history', []).sort(key=lambda x: x['created_at'], reverse=True)
            medical_history.get('vaccination_record', []).sort(key=lambda x: x['created_at'], reverse=True)
            medical_history.get('obstetric_history', []).sort(key=lambda x: x['created_at'], reverse=True)

            return {
                'msg': 'Medical history found',
                'medical_history': medical_history
            }
        except Exception as e:
            raise Exception(str(e))

    def get_medical_history(self, patient_id: str):
        try:
            return self.get_medical_history_of_patient(patient_id=patient_id)

        except Exception as e:
            raise HTTPException(status_code=409,
                                detail=f'Error occurred as {str(e)} while fetching medical history of the patient')

    def add_vitals_by_doctor_and_admin(self, patient_id: str, data: VitalsRecord, logged_in_user: str, added_by_type:str):
        try:

            from ayoo_backend.api.vitals.vitals_models import CreateVitalRequest
            from ayoo_backend.api.vitals.vitals_controllers import VitalsController

            vital_controller = VitalsController()

            # Add general vitals
            for key, value in data.__dict__.items():
                if key in vital_units and value is not None and key not in ['bp_systolic', 'bp_diastolic',
                                                                            'case_id', 'date']:
                    if value not in [None, '']:
                        vital_controller.add_vital(vital_request=CreateVitalRequest(
                            patient_id=patient_id,
                            case_id=data.case_id,
                            type=key,
                            value=value,
                            unit=vital_units[key],
                            date=data.date
                        ),
                            added_by=logged_in_user,
                            added_by_type=added_by_type,
                            patient_id=patient_id)

            # Add blood pressure
            if data.bp_systolic and data.bp_diastolic:
                bp_value = f"{data.bp_systolic}/{data.bp_diastolic}"
                vital_controller.add_vital(vital_request=CreateVitalRequest(
                    patient_id=patient_id,
                    case_id=data.case_id,
                    type='blood_pressure',
                    value=bp_value,
                    unit=vital_units['blood_pressure'],
                    date=data.date
                ),
                    added_by=logged_in_user,
                    added_by_type='admin',
                    patient_id=patient_id
                )


            return {
                'msg': 'Vital data added',
                'status': 'Success'
            }

        except Exception as e:
            raise Exception(str(e))

    def add_medical_history(self, patient_id: str, key_to_add: str, data: PatientMedicalHistory, logged_in_user: str, added_by_type:str):
        try:
            if key_to_add == 'vitals':
                # data_to_add = {
                #     "id": str(uuid.uuid4()),
                #     "case_id": data.vitals.case_id,
                #     "temperature": data.vitals.temperature,
                #     "pulse": data.vitals.pulse,
                #     "bp_systolic": data.vitals.bp_systolic,
                #     "bp_diastolic": data.vitals.bp_diastolic,
                #     "weight": data.vitals.weight,
                #     "height": data.vitals.height,
                #     "respiratory_rate": data.vitals.respiratory_rate,
                #     "pulse_ox": data.vitals.pulse_ox,
                #     "blood_glucose": data.vitals.blood_glucose,
                #     "waist_circum": data.vitals.waist_circum,
                #     "bmi": data.vitals.bmi,
                #     "date": data.vitals.date,
                #     "created_at": datetime.now(),
                #     "updated_at": None
                # }
                return self.add_vitals_by_doctor_and_admin(patient_id=patient_id, data=data.vitals,
                                                        logged_in_user=logged_in_user, added_by_type=added_by_type)

            elif key_to_add == 'known_allergies':
                data_to_add = {
                    "id": str(uuid.uuid4()),
                    "drug": data.known_allergies.drug,
                    "food": data.known_allergies.food,
                    "others": data.known_allergies.others,
                    "created_at": datetime.now(),
                    "updated_at": None
                }

            elif key_to_add == 'prior_medical_history':
                data_to_add = {
                    "id": str(uuid.uuid4()),
                    "condition": data.prior_medical_history.condition,
                    "date": data.prior_medical_history.date,
                    "physician_hospital": data.prior_medical_history.physician_hospital,
                    "notes": data.prior_medical_history.notes,
                    "created_at": datetime.now(),
                    "updated_at": None
                }

            elif key_to_add == 'current_or_past_medication':
                data_to_add = {
                    "id": str(uuid.uuid4()),
                    "medicine_name": data.current_or_past_medication.medicine_name,
                    "dosage": data.current_or_past_medication.dosage,
                    "frequency": data.current_or_past_medication.frequency,
                    "start_date": data.current_or_past_medication.start_date,
                    "end_date": data.current_or_past_medication.end_date,
                    "purpose": data.current_or_past_medication.purpose,
                    "created_at": datetime.now(),
                    "updated_at": None
                }

            elif key_to_add == 'family_history':
                data_to_add = {
                    "id": str(uuid.uuid4()),
                    "relationship": data.family_history.relationship,
                    "condition": data.family_history.condition,
                    "notes": data.family_history.notes,
                    "created_at": datetime.now(),
                    "updated_at": None
                }

            elif key_to_add == 'vaccination_record':
                data_to_add = {
                    "id": str(uuid.uuid4()),
                    "case_id": data.vaccination_record.case_id,
                    "vaccine_name": data.vaccination_record.vaccine_name,
                    "date": data.vaccination_record.date,
                    "due_date": data.vaccination_record.due_date,
                    "created_at": datetime.now(),
                    "updated_at": None
                }

            elif key_to_add == 'obstetric_history':
                data_to_add = {
                    "id": str(uuid.uuid4()),
                    "date": data.obstetric_history.date,
                    "obstetric_score": data.obstetric_history.obstetric_score,
                    "lmp": data.obstetric_history.lmp,
                    "disturbances": data.obstetric_history.disturbances,
                    "created_at": datetime.now(),
                    "updated_at": None
                }
            else:
                raise ValueError("Invalid key provided")

            self.add_key_value_to_mongo(key_to_add=key_to_add, patient_id=patient_id, data=data_to_add)

            return {'msg': 'Medical history added'}
        except Exception as e:
            raise HTTPException(status_code=409,
                                detail=f'Error occurred as {str(e)} while adding medical history of the patient')

    def update_medical_history(self, patient_id: str, key_to_update: str, key_id: str, data: PatientMedicalHistory):
        try:
            get_previous_record = self.get_medical_history_of_patient(patient_id=patient_id)
            if get_previous_record is None:
                raise Exception('No record found to update, add a record first')
            previous_record = get_previous_record.get('medical_history', {}).get(key_to_update, [])
            created_at = None
            if len(previous_record) > 0:
                key_record_data = next((record for record in previous_record if record.get('id') == key_id), {})
                created_at = key_record_data.get('created_at', None)

            if created_at is None:
                raise Exception('No record found to update with given key id')
            if key_to_update == 'vitals':
                data_to_update = {
                    "id": str(key_id),
                    "case_id": data.vitals.case_id,
                    "temperature": data.vitals.temperature,
                    "pulse": data.vitals.pulse,
                    "bp_systolic": data.vitals.bp_systolic,
                    "bp_diastolic": data.vitals.bp_diastolic,
                    "weight": data.vitals.weight,
                    "height": data.vitals.height,
                    "respiratory_rate": data.vitals.respiratory_rate,
                    "pulse_ox": data.vitals.pulse_ox,
                    "blood_glucose": data.vitals.blood_glucose,
                    "waist_circum": data.vitals.waist_circum,
                    "bmi": data.vitals.bmi,
                    "date": data.vitals.date,
                    "created_at": created_at,
                    "updated_at": datetime.now()
                }

            elif key_to_update == 'known_allergies':
                data_to_update = {
                    "id": str(key_id),
                    "drug": data.known_allergies.drug,
                    "food": data.known_allergies.food,
                    "others": data.known_allergies.others,
                    "created_at": created_at,
                    "updated_at": datetime.now()
                }

            elif key_to_update == 'prior_medical_history':
                data_to_update = {
                    "id": str(key_id),
                    "condition": data.prior_medical_history.condition,
                    "date": data.prior_medical_history.date,
                    "physician_hospital": data.prior_medical_history.physician_hospital,
                    "notes": data.prior_medical_history.notes,
                    "created_at": created_at,
                    "updated_at": datetime.now()
                }

            elif key_to_update == 'current_or_past_medication':
                data_to_update = {
                    "id": str(key_id),
                    "medicine_name": data.current_or_past_medication.medicine_name,
                    "dosage": data.current_or_past_medication.dosage,
                    "frequency": data.current_or_past_medication.frequency,
                    "start_date": data.current_or_past_medication.start_date,
                    "end_date": data.current_or_past_medication.end_date,
                    "purpose": data.current_or_past_medication.purpose,
                    "created_at": created_at,
                    "updated_at": datetime.now()
                }

            elif key_to_update == 'family_history':
                data_to_update = {
                    "id": str(key_id),
                    "relationship": data.family_history.relationship,
                    "condition": data.family_history.condition,
                    "notes": data.family_history.notes,
                    "created_at": created_at,
                    "updated_at": datetime.now()
                }

            elif key_to_update == 'vaccination_record':
                data_to_update = {
                    "id": str(key_id),
                    "case_id": data.vaccination_record.case_id,
                    "vaccine_name": data.vaccination_record.vaccine_name,
                    "date": data.vaccination_record.date,
                    "due_date": data.vaccination_record.due_date,
                    "created_at": created_at,
                    "updated_at": datetime.now()
                }

            elif key_to_update == 'obstetric_history':
                data_to_update = {
                    "id": str(key_id),
                    "date": data.obstetric_history.date,
                    "obstetric_score": data.obstetric_history.obstetric_score,
                    "lmp": data.obstetric_history.lmp,
                    "disturbances": data.obstetric_history.disturbances,
                    "created_at": created_at,
                    "updated_at": datetime.now()
                }
            else:
                raise ValueError("Invalid key provided")

            self.update_key_value_to_mongo(key_to_update=key_to_update, key_id=str(key_id), patient_id=patient_id,
                                           data=data_to_update)

            return {'msg': 'Medical history updated'}
        except Exception as e:
            raise HTTPException(status_code=409,
                                detail=f'Error occurred as {str(e)} while updating medical history of the patient')

    def get_clinical_history_from_admin(self, patient_id: str):
        try:
            return self.get_all_cases_of_user_including_referrals(user_id=patient_id)

        except Exception as e:
            loggers['logger5'].error("status_code=409, error occurred as : " + str(e))
            raise HTTPException(status_code=409,
                                detail=f'Error occurred as {str(e)} while getting clinical history of the patient')
