import random
import uuid
import datetime
from datetime import timedelta
import calendar
import copy
import enum
import time
from typing import Optional
from fastapi import Response, HTTPException
import pandas as pd
import sqlalchemy.exc
from sqlalchemy import func

from ayoo_backend.api.views import logger

from fastapi import UploadFile
from sqlalchemy.orm import scoped_session

from ayoo_backend.api import dbmodels

from ayoo_backend.api.dbmodels import DBSpecializationMeta, DBPracticeAreaMeta, DBFormTable, DBQuestionTable, \
    DBFormResponseTable
from ayoo_backend.api.metadata_models import Specialization, SpecializationUpdate, SpecializationGet, \
    PracticeArea, PracticeAreaUpdate, PracticeAreaGet, FormAdd, FormUpdate, FormDelete, QuestionAdd, GetFormQuestions, \
    QuestionUpdate, QuestionDelete, SpecializationDelete, PracticeAreaDelete, QuestionResponseRangeAdd, \
    QuestionResponseRangeUpdate, QuestionResponseDelete, DropdownMetadata, MetadataListName, MetadataListNameUpdate, \
    DropdownMetadataUpdate, DropdownMetadataDelete, DropdownMetadataSearch, AddMedicineToCatalogue, \
    UpdateMedicineInCatalogue, DeleteMedicineFromCatalogue, GetMedicineBrandFromSalt, \
    GetMedicineDoseAndDrugFormFromBrandAndSalt, DropdownMetadataAdd, AddICDCodeToCatalogue, SearchICDCodeByBlock, \
    GetMedicineSaltFromBrand


class MetadataController:

    def __init__(self, db: scoped_session, mongo=None):
        self.db = db
        self.mongo = mongo
        self.mongo_db = self.mongo['ayoo']

    def __get_specialization_by_id(self, specialization_id):
        resp: DBSpecializationMeta  # = None
        resp = self.db.query(dbmodels.DBSpecializationMeta).filter(
            DBSpecializationMeta.specialization_id == specialization_id).one_or_none()
        return resp

    def get_specialization_by_name(self, specialization):
        resp: DBSpecializationMeta  # = None
        resp = self.db.query(dbmodels.DBSpecializationMeta).filter(
            DBSpecializationMeta.specialization.ilike(specialization)).one_or_none()
        return resp

    def __get_practice_area_by_id(self, practice_area_id):
        resp: DBPracticeAreaMeta  # = None
        resp = self.db.query(dbmodels.DBPracticeAreaMeta).filter(
            DBPracticeAreaMeta.practice_area_id == practice_area_id).one_or_none()
        return resp

    def __get_practice_area_by_name(self, practice_area):
        resp: DBPracticeAreaMeta  # = None
        resp = self.db.query(dbmodels.DBPracticeAreaMeta).filter(
            DBPracticeAreaMeta.practice_area == practice_area).one_or_none()
        return resp

    def __get_form_by_name(self, form_name):
        resp: DBFormTable  # = None
        resp = self.db.query(dbmodels.DBFormTable).filter(DBFormTable.form_name == form_name).one_or_none()
        return resp

    def __get_form_by_id(self, form_id):
        resp: DBFormTable  # = None
        resp = self.db.query(dbmodels.DBFormTable).filter(DBFormTable.form_id == form_id).one_or_none()
        return resp

    def __get_question_by_id(self, question_id):
        resp: DBQuestionTable  # = None
        resp = self.db.query(dbmodels.DBQuestionTable).filter(DBQuestionTable.question_id == question_id).one_or_none()
        return resp

    def __get_question_response_by_id(self, response_id):
        resp: DBQuestionTable  # = None
        resp = self.db.query(dbmodels.DBFormResponseTable).filter(
            DBFormResponseTable.response_id == response_id).one_or_none()
        return resp

    def __check_if_practice_area_in_the_specialization_exists(self, practice_area, specialization_id):
        resp: DBPracticeAreaMeta  # = None
        resp = self.db.query(dbmodels.DBPracticeAreaMeta).filter(
            DBPracticeAreaMeta.practice_area == practice_area).filter(
            DBPracticeAreaMeta.specialization_id == specialization_id).one_or_none()
        return resp

    def specialization_add(self, metadata: Specialization):
        specialization_exist = self.get_specialization_by_name(metadata.specialization_name)
        if specialization_exist:
            return None, f'{metadata.specialization_name} already exists.'

        uid = str(uuid.uuid4())

        db_specialization = DBSpecializationMeta(specialization_id=uid,
                                                 specialization=metadata.specialization_name,
                                                 specialist=metadata.specialist if metadata.specialist else "",
                                                 department=metadata.department if metadata.department else "",
                                                 specialization_field=metadata.specialization_field,
                                                 specialization_acronym=metadata.specialization_acronym.upper(),
                                                 prescription_type=metadata.prescription_type
                                                 )

        try:
            self.db.add(db_specialization)
            self.db.commit()

        except sqlalchemy.exc.SQLAlchemyError as d:
            self.db.rollback()
            err = str(d)
            return None, f'DB Error code {err} for adding specialization, with name {str(db_specialization.specialization)}'

        except BaseException as e:
            self.db.rollback()
            err = str(e)
            return None, f'Internal Error code {err} for adding specialization, with name {str(db_specialization.specialization)}'

        return {'specialization_id': db_specialization.specialization_id,
                'specialization': db_specialization.specialization,
                'specialist': db_specialization.specialist,
                'department': db_specialization.department,
                'specialization_field': db_specialization.specialization_field,
                'specialization_acronym': db_specialization.specialization_acronym,
                'prescription_type': db_specialization.prescription_type
                }, 'Specialization added'

    def update_specialization_by_id(self, metadata: SpecializationUpdate):
        specialization_exist = self.__get_specialization_by_id(metadata.specialization_id)
        if specialization_exist is None:
            return None, f'{metadata.specialization_id} does not exist.'

        specialization: DBSpecializationMeta = self.db.query(dbmodels.DBSpecializationMeta).filter(
            DBSpecializationMeta.specialization_id == metadata.specialization_id).one_or_none()

        specialization_new = DBSpecializationMeta(
            specialization=metadata.specialization_name,
            specialist=metadata.specialist if metadata.specialist else "",
            department=metadata.department if metadata.department else "",
            specialization_field=metadata.specialization_field,
            specialization_acronym=metadata.specialization_acronym.upper(),
            prescription_type=metadata.prescription_type
        )

        try:
            specialization.update(specialization_new)
            self.db.commit()

        except sqlalchemy.exc.SQLAlchemyError as d:
            self.db.rollback()
            err = str(d)
            return None, f'DB Error code {err} for updating specialization, with id {str(metadata.specialization_id)}'

        except BaseException as e:
            self.db.rollback()
            err = str(e)
            return None, f'Internal Error code {err} for updating specialization, with id {str(metadata.specialization_id)}'

        return {'specialization_id': specialization.specialization_id,
                'specialization': specialization.specialization,
                'specialist': specialization.specialist,
                'department': specialization.department,
                'specialization_field': specialization.specialization_field,
                'specialization_acronym': specialization.specialization_acronym,
                'prescription_type': specialization.prescription_type
                }, 'Specialization updated'

    def delete_specialization_by_id(self, metadata: SpecializationDelete):
        specialization_exist = self.__get_specialization_by_id(metadata.specialization_id)
        if specialization_exist is None:
            return None, f'{metadata.specialization_id} does not exist.'

        specialization: DBSpecializationMeta = self.db.query(dbmodels.DBSpecializationMeta).filter(
            DBSpecializationMeta.specialization_id == metadata.specialization_id).one_or_none()

        try:
            self.db.delete(specialization)
            self.db.commit()

        except sqlalchemy.exc.SQLAlchemyError as d:
            self.db.rollback()
            err = str(d)
            return None, f'DB Error code {err} for deleting specialization, with id {str(metadata.specialization_id)}'

        except BaseException as e:
            self.db.rollback()
            err = str(e)
            return None, f'Internal Error code {err} for deleting specialization, with id {str(metadata.specialization_id)}'

        return {
                   'msg': 'Specialization deleted',
                   'specialization': specialization.specialization,
                   'specialist': specialization.specialist,
                   'department': specialization.department,
                   'specialization_field': specialization.specialization_field,
                   'specialization_acronym': specialization.specialization_acronym
               }, 'Specialization deleted'

    def get_all_specializations_by_field(self, metadata: SpecializationGet):
        try:
            resp = self.db.query(dbmodels.DBSpecializationMeta).filter(
                DBSpecializationMeta.specialization_field == metadata.specialization_field.title()).all()
            # #logger.info(resp)
            specializations = []
            for result in resp:
                specializations.append({'specialization_id': result.specialization_id,
                                        'specialization': result.specialization,
                                        'specialist': result.specialist,
                                        'department':result.department,
                                        'specialization_field': result.specialization_field,
                                        'specialization_acronym': result.specialization_acronym,
                                        'prescription_type': result.prescription_type
                                        })

            if len(specializations) == 0:
                return None, 'No data found'

            return specializations, 'Data Found'
        except BaseException as e:
            err = str(e)
            return None, f'Internal Error code {err} for getting specializations'

    def get_all_specializations(self):
        try:
            resp = self.db.query(dbmodels.DBSpecializationMeta).all()
            # #logger.info(resp)
            specializations = []
            for result in resp:
                specializations.append({'specialization_id': result.specialization_id,
                                        'specialization': result.specialization,
                                        'specialist': result.specialist,
                                        'department':result.department,
                                        'specialization_field': result.specialization_field,
                                        'specialization_acronym': result.specialization_acronym,
                                        'prescription_type': result.prescription_type
                                        })

            if len(specializations) == 0:
                return None, 'No data found'

            return specializations, 'Data Found'
        except BaseException as e:
            err = str(e)
            return None, f'Internal Error code {err} for getting specializations'

    def practice_area_add(self, metadata: PracticeArea):
        practice_area_exist = self.__get_practice_area_by_name(metadata.practice_area)
        if practice_area_exist:
            return None, f'{metadata.practice_area} already exists.'

        uid = str(uuid.uuid4())

        db_practice_area = DBPracticeAreaMeta(practice_area_id=uid, practice_area=metadata.practice_area,
                                              practice_area_field=metadata.practice_area_field)

        try:
            self.db.add(db_practice_area)
            self.db.commit()

        except sqlalchemy.exc.SQLAlchemyError as d:
            self.db.rollback()
            err = str(d)
            return None, f'DB Error code {err} for adding practice area, with name {str(db_practice_area.practice_area)}'

        except BaseException as e:
            self.db.rollback()
            err = str(e)
            return None, f'Internal Error code {err} for adding practice area, with name {str(db_practice_area.practice_area)}'

        return {'practice_area_id': db_practice_area.practice_area_id,
                'practice_area': db_practice_area.practice_area,
                'practice_area_field': db_practice_area.practice_area_field
                }, 'Practice area added'

    def delete_practice_area_by_id(self, metadata: PracticeAreaDelete):
        practice_area_exist = self.__get_practice_area_by_id(metadata.practice_area_id)
        if practice_area_exist is None:
            return None, f'{metadata.practice_area_id} does not exist.'

        practice_area: DBPracticeAreaMeta = self.db.query(dbmodels.DBPracticeAreaMeta).filter(
            DBPracticeAreaMeta.practice_area_id == metadata.practice_area_id).one_or_none()

        try:
            self.db.delete(practice_area)
            self.db.commit()

        except sqlalchemy.exc.SQLAlchemyError as d:
            self.db.rollback()
            err = str(d)
            return None, f'DB Error code {err} for deleting practice_area, with id {str(metadata.practice_area_id)}'

        except BaseException as e:
            self.db.rollback()
            err = str(e)
            return None, f'Internal Error code {err} for deleting practice_area, with id {str(metadata.practice_area_id)}'

        return {'msg': 'Practice area deleted',
                'practice_area': practice_area.practice_area,
                'practice_area_field': practice_area.practice_area_field
                }, 'Practice area deleted'

    def update_practice_area_by_id(self, metadata: PracticeAreaUpdate):
        practice_area_exist = self.__get_practice_area_by_id(metadata.practice_area_id)
        if practice_area_exist is None:
            return None, f'{metadata.practice_area_id} does not exist.'

        practice_area: DBPracticeAreaMeta = self.db.query(dbmodels.DBPracticeAreaMeta).filter(
            DBPracticeAreaMeta.practice_area_id == metadata.practice_area_id).one_or_none()

        practice_area_new = DBPracticeAreaMeta(
            practice_area=metadata.practice_area,
            practice_area_field=metadata.practice_area_field)

        try:
            practice_area.update(practice_area_new)
            self.db.commit()

        except sqlalchemy.exc.SQLAlchemyError as d:
            self.db.rollback()
            err = str(d)
            return None, f'DB Error code {err} for updating practice_area, with id {str(metadata.practice_area_id)}'

        except BaseException as e:
            self.db.rollback()
            err = str(e)
            return None, f'Internal Error code {err} for updating practice_area, with id {str(metadata.practice_area_id)}'

        return {'practice_area_id': practice_area.practice_area_id,
                'practice_area': practice_area.practice_area,
                'practice_area_field': practice_area.practice_area_field
                }, 'Practice area updated'

    def get_all_practice_areas(self, metadata: PracticeAreaGet):
        try:
            resp = self.db.query(dbmodels.DBPracticeAreaMeta).filter(
                DBPracticeAreaMeta.practice_area_field == metadata.practice_area_field.title()).all()

            order_list = ["Psychiatry", "Psychology", "Depression", "Anxiety Disorders", "Substance Use Disorder",
                          "Child Therapy", "Marriage/Couples Therapy", "Bipolar/Schizophrenia", "Other Concerns"]

            practice_areas = []
            for result in resp:
                practice_areas.append({'practice_area_id': result.practice_area_id,
                                       'practice_area': result.practice_area,
                                       'practice_area_field': result.practice_area_field})

            if len(practice_areas) == 0:
                return None, 'No data found for given practice area field'

            sorted_practice_areas = sorted(practice_areas, key=lambda x: (order_list.index(x["practice_area"])
                                                                          if x["practice_area"] in order_list else len(
                order_list)))
            return sorted_practice_areas, 'Data Found for given specialization field'

        except BaseException as e:
            err = str(e)
            return None, f'Internal Error code {err} for getting practice area'

    def add_new_form(self, metadata: FormAdd):
        form_exists = self.__get_form_by_name(metadata.form_name)
        if form_exists:
            return None, f'{metadata.form_name} already exists.'

        uid = str(uuid.uuid4())

        db_new_form = DBFormTable(form_id=uid, form_name=metadata.form_name.title())

        try:
            self.db.add(db_new_form)
            self.db.commit()

        except sqlalchemy.exc.SQLAlchemyError as d:
            self.db.rollback()
            err = str(d)
            return None, f'DB Error code {err} for adding new form, with name {str(db_new_form.form_name)}'

        except BaseException as e:
            self.db.rollback()
            err = str(e)
            return None, f'Internal Error code {err} for adding new form, with name {str(db_new_form.form_name)}'

        return {'form_id': db_new_form.form_id,
                'form_name': db_new_form.form_name
                }, 'Form added'

    def download_sample_form_format(self):
        try:
            # data = {'serial': [1, 2, 3, 4],
            #         'form_name': ['form name 1', 'form name 2', 'form name 3', 'form name 4']
            #         }
            #
            # df = pd.DataFrame(data)
            # df.to_csv('form_upload_sample.csv', index=False, header=True)
            #
            # return {'msg': 'Form Downloaded. Please do not modify the contents of Row 1'}, 'Form download success'
            return {
                       'form_download_s3_link': 'https://ayoo-web-bucket.s3.ap-south-1.amazonaws.com/template-forms/form_upload_sample.csv'}, 'Form download success'
        except Exception as e:
            return None, f'error occurred as {str(e)} while downloading sample form csv file'

    def add_new_form_by_excel(self, metadata: UploadFile):
        try:
            # data = pd.read_excel(metadata.file)
            data = pd.read_csv(metadata.file)
            csv_data = data.to_dict(orient="records")
            for ele in csv_data:
                save_form, msg = self.add_new_form(metadata=FormAdd(form_name=ele['form_name']))

                if save_form is None:
                    return None, msg

            return {'msg': 'Forms Added'}, 'Form added'
        except Exception as e:
            return None, f'error occurred as {str(e)} while adding form with csv file'

    def download_sample_question_format(self):
        try:

            # data = {'serial': [1, 2, 3, 4, 5, 6],
            #         'question_type': ['MCQ', 'TEXT', 'MULTISELECT', 'MCQ', 'TEXT', 'MULTISELECT'],
            #         'question_text': ['how many hours of sleep do you get daily?',
            #                           'what are your thoughts generally when you wake up?',
            #                           'do you have allergies from any of these substances?',
            #                           'have you been treated with any other disease in past?',
            #                           'with whom you are more close?', 'if other relative, mention relation.'],
            #         'option1': ['8 hours', None, 'walnuts', 'yes', 'mother', None],
            #         'option1_point': [],
            #         'option2': ['6 hours', None, 'almonds', 'no', 'father', None],
            #         'option2_point': [],
            #         'option3': ['8-10 hours', None, 'cheese', None, 'brother', None],
            #         'option3_point': [],
            #         'option4': ['less than 6 hours', None, 'milk', None, 'sister', None],
            #         'option4_point': [],
            #         'option5': ['more than 10 hours', None, 'soy', None, 'friend', None],
            #         'option5_point': [],
            #         'option6': [None, None, 'eggs', None, 'other relative', None],
            #         'option6_point': [],
            #         'option7': [None, None, 'fish', None, 'none', None],
            #         'option7_point': [],
            #         'option8': [None, None, 'peanuts', None, None, None],
            #         'option8_point': [],
            #         'option9': [None, None, 'wheat', None, None, None],
            #         'option9_point': [],
            #         'option10': [None, None, 'others', None, None, None],
            #         'option10_point': [],
            #
            #         }
            # data = {'serial': [1, 2, 3, 4, 5, 6, 7],
            #         'question_type': ['MCQ', 'MCQ', 'MCQ', 'MCQ', 'MCQ', 'MCQ', 'MCQ'],
            #         'question_text': ['Feeling nervous, anxious, or on edge',
            #                           'Not being able to stop or control worrying',
            #                           'Worrying too much about different things',
            #                           'Trouble relaxing',
            #                           'Being so restless that it is hard to sit still',
            #                           'Becoming easily annoyed or irritable',
            #                           'Feeling afraid, as if something awful might happen'],
            #         'option1': ['Not at all', 'Not at all', 'Not at all', 'Not at all', 'Not at all', 'Not at all',
            #                     'Not at all'],
            #         'option1_point': [0, 0, 0, 0, 0, 0, 0],
            #         'option2': ['Several days', 'Several days', 'Several days', 'Several days', 'Several days',
            #                     'Several days', 'Several days'],
            #         'option2_point': [1, 1, 1, 1, 1, 1, 1],
            #         'option3': ['More than half the days', 'More than half the days', 'More than half the days',
            #                     'More than half the days', 'More than half the days', 'More than half the days',
            #                     'More than half the days'],
            #         'option3_point': [2, 2, 2, 2, 2, 2, 2],
            #         'option4': ['Nearly every day', 'Nearly every day', 'Nearly every day', 'Nearly every day',
            #                     'Nearly every day', 'Nearly every day', 'Nearly every day'],
            #         'option4_point': [3, 3, 3, 3, 3, 3, 3],
            #         'option5': [None, None, None, None, None, None, None],
            #         'option5_point': [None, None, None, None, None, None, None],
            #         'option6': [None, None, None, None, None, None, None],
            #         'option6_point': [None, None, None, None, None, None, None],
            #         'option7': [None, None, None, None, None, None, None],
            #         'option7_point': [None, None, None, None, None, None, None],
            #         'option8': [None, None, None, None, None, None, None],
            #         'option8_point': [None, None, None, None, None, None, None],
            #         'option9': [None, None, None, None, None, None, None],
            #         'option9_point': [None, None, None, None, None, None, None],
            #         'option10': [None, None, None, None, None, None, None],
            #         'option10_point': [None, None, None, None, None, None, None],
            #
            #         }
            # df = pd.DataFrame(data)
            # df.to_csv('question_upload_sample.csv', index=False, header=True)
            #
            # return {
            #            'msg': 'Question Sample Downloaded. Please do not modify the contents of Row 1'
            #        }, 'Question Sample download success'
            return {
                       'form_download_s3_link': 'https://ayoo-web-bucket.s3.ap-south-1.amazonaws.com/template-forms/question_upload_sample.csv'
                   }, 'Question form download success'
        except Exception as e:
            return None, f'error occurred as {str(e)} while downloading sample question csv file'

    def add_questions_by_excel(self, form_id: str, metadata: UploadFile):
        try:
            # data = pd.read_excel(metadata.file)
            data = pd.read_csv(metadata.file)
            csv_data = data.to_dict(orient="records")
            for ele in csv_data:
                save_question, msg = self.add_new_question_to_form(
                    metadata=QuestionAdd(
                        form_id=form_id,
                        question_type=ele['question_type'], question_text=ele['question_text'],
                        option1=ele['option1'] if not str(ele['option1']) == 'nan' else None,
                        option1_point=ele['option1_point'] if not str(ele['option1_point']) == 'nan' else None,
                        option2=ele['option2'] if not str(ele['option2']) == 'nan' else None,
                        option2_point=ele['option2_point'] if not str(ele['option2_point']) == 'nan' else None,
                        option3=ele['option3'] if not str(ele['option3']) == 'nan' else None,
                        option3_point=ele['option3_point'] if not str(ele['option3_point']) == 'nan' else None,
                        option4=ele['option4'] if not str(ele['option4']) == 'nan' else None,
                        option4_point=ele['option4_point'] if not str(ele['option4_point']) == 'nan' else None,
                        option5=ele['option5'] if not str(ele['option5']) == 'nan' else None,
                        option5_point=ele['option5_point'] if not str(ele['option5_point']) == 'nan' else None,
                        option6=ele['option6'] if not str(ele['option6']) == 'nan' else None,
                        option6_point=ele['option6_point'] if not str(ele['option6_point']) == 'nan' else None,
                        option7=ele['option7'] if not str(ele['option7']) == 'nan' else None,
                        option7_point=ele['option7_point'] if not str(ele['option7_point']) == 'nan' else None,
                        option8=ele['option8'] if not str(ele['option8']) == 'nan' else None,
                        option8_point=ele['option8_point'] if not str(ele['option8_point']) == 'nan' else None,
                        option9=ele['option9'] if not str(ele['option9']) == 'nan' else None,
                        option9_point=ele['option9_point'] if not str(ele['option9_point']) == 'nan' else None,
                        option10=ele['option10'] if not str(ele['option10']) == 'nan' else None,
                        option10_point=ele['option10_point'] if not str(ele['option10_point']) == 'nan' else None
                    )
                )

                if save_question is None:
                    return None, msg

            return {'msg': 'Questions Added'}, 'Questions Added'
        except Exception as e:
            return None, f'error occurred as {str(e)} while adding questions with csv file'

    def get_all_forms(self):

        try:
            resp: DBFormTable  # = None
            resp = self.db.query(dbmodels.DBFormTable).all()

        except BaseException as e:
            self.db.rollback()
            err = str(e)
            return None, f'Internal Error code {err} while fetching forms'

        if (len(resp) == 0):
            return None, 'No Forms Found'

        return resp, 'Forms found'

    def update_form_by_id(self, metadata: FormUpdate):
        form_exists = self.__get_form_by_id(metadata.form_id)
        if form_exists is None:
            return None, f'{metadata.form_id} does not exist.'

        form: DBFormTable = self.db.query(dbmodels.DBFormTable).filter(
            DBFormTable.form_id == metadata.form_id).one_or_none()

        form_new = DBFormTable(form_name=metadata.form_name)

        try:
            form.update(form_new)
            self.db.commit()

        except sqlalchemy.exc.SQLAlchemyError as d:
            self.db.rollback()
            err = str(d)
            return None, f'DB Error code {err} for updating form, with id {str(metadata.form_id)}'

        except BaseException as e:
            self.db.rollback()
            err = str(e)
            return None, f'Internal Error code {err} for updating form, with id {str(metadata.form_id)}'

        return {'form_id': form.form_id,
                'form_name': form.form_name
                }, 'Form updated'

    def delete_form_by_id(self, metadata: FormDelete):
        form_exists = self.__get_form_by_id(metadata.form_id)
        if form_exists is None:
            return None, f'{metadata.form_id} does not exist.'

        form_to_delete: DBFormTable = self.db.query(dbmodels.DBFormTable).filter(
            DBFormTable.form_id == metadata.form_id).one_or_none()

        try:
            self.db.delete(form_to_delete)
            self.db.commit()

        except sqlalchemy.exc.SQLAlchemyError as d:
            self.db.rollback()
            err = str(d)
            return None, f'DB Error code {err} for deleting form, with id {str(metadata.form_id)}'

        except BaseException as e:
            self.db.rollback()
            err = str(e)
            return None, f'Internal Error code {err} for deleting form, with id {str(metadata.form_id)}'

        return 'Form deleted', 'Success'

    def add_new_question_to_form(self, metadata: QuestionAdd):
        uid = str(uuid.uuid4())
        db_new_question = DBQuestionTable(question_id=uid, form_id=metadata.form_id,
                                          question_type=metadata.question_type,
                                          question_text=metadata.question_text.title(),
                                          option1=metadata.option1.title() if metadata.option1 else None,
                                          option1_point=metadata.option1_point,
                                          option2=metadata.option2.title() if metadata.option2 else None,
                                          option2_point=metadata.option2_point,
                                          option3=metadata.option3.title() if metadata.option3 else None,
                                          option3_point=metadata.option3_point,
                                          option4=metadata.option4.title() if metadata.option4 else None,
                                          option4_point=metadata.option4_point,
                                          option5=metadata.option5.title() if metadata.option5 else None,
                                          option5_point=metadata.option5_point,
                                          option6=metadata.option6.title() if metadata.option6 else None,
                                          option6_point=metadata.option6_point,
                                          option7=metadata.option7.title() if metadata.option7 else None,
                                          option7_point=metadata.option7_point,
                                          option8=metadata.option8.title() if metadata.option8 else None,
                                          option8_point=metadata.option8_point,
                                          option9=metadata.option9.title() if metadata.option9 else None,
                                          option9_point=metadata.option9_point,
                                          option10=metadata.option10.title() if metadata.option10 else None,
                                          option10_point=metadata.option10_point
                                          )

        try:
            self.db.add(db_new_question)
            self.db.commit()

        except sqlalchemy.exc.SQLAlchemyError as d:
            self.db.rollback()
            err = str(d)
            return None, f'DB Error code {err} for adding new question'

        except BaseException as e:
            self.db.rollback()
            err = str(e)
            return None, f'Internal Error code {err} for adding new question'

        return db_new_question, 'Question added'

    def get_all_form_questions(self, metadata: GetFormQuestions):
        form_exists = self.__get_form_by_id(metadata.form_id)
        if form_exists is None:
            return None, f'{metadata.form_id} does not exist.'

        data_to_return = {}
        try:

            form_details = self.__get_form_by_id(form_id=metadata.form_id)
            data_to_return['form_id'] = form_details.form_id
            data_to_return['form_name'] = form_details.form_name

            question_data = []
            resp: DBQuestionTable  # = None
            resp = self.db.query(dbmodels.DBQuestionTable).filter(DBQuestionTable.form_id == metadata.form_id).all()

            for data in resp:
                question_data.append(dict(
                    question_id=data.question_id,
                    question_type=data.question_type,
                    question_text=data.question_text,
                    option1=data.option1,
                    option1_point=data.option1_point,
                    option2=data.option2,
                    option2_point=data.option2_point,
                    option3=data.option3,
                    option3_point=data.option3_point,
                    option4=data.option4,
                    option4_point=data.option4_point,
                    option5=data.option5,
                    option5_point=data.option5_point,
                    option6=data.option6,
                    option6_point=data.option6_point,
                    option7=data.option7,
                    option7_point=data.option7_point,
                    option8=data.option8,
                    option8_point=data.option8_point,
                    option9=data.option9,
                    option9_point=data.option9_point,
                    option10=data.option10,
                    option10_point=data.option10_point,
                ))

            data_to_return['form_questions'] = question_data

            question_response = self.db.query(dbmodels.DBFormResponseTable).filter(
                DBFormResponseTable.form_id == metadata.form_id).all()

            question_response_data = []

            for q_resp in question_response:
                question_response_data.append(dict(
                    range1_lower_limit=q_resp.range1_lower_limit,
                    range1_upper_limit=q_resp.range1_upper_limit,
                    range1_response_text=q_resp.range1_response_text,
                    range2_lower_limit=q_resp.range2_lower_limit,
                    range2_upper_limit=q_resp.range2_upper_limit,
                    range2_response_text=q_resp.range2_response_text,
                    range3_lower_limit=q_resp.range3_lower_limit,
                    range3_upper_limit=q_resp.range3_upper_limit,
                    range3_response_text=q_resp.range3_response_text,
                    range4_lower_limit=q_resp.range4_lower_limit,
                    range4_upper_limit=q_resp.range4_upper_limit,
                    range4_response_text=q_resp.range4_response_text,
                    range5_lower_limit=q_resp.range5_lower_limit,
                    range5_upper_limit=q_resp.range5_upper_limit,
                    range5_response_text=q_resp.range5_response_text,
                    range6_lower_limit=q_resp.range6_lower_limit,
                    range6_upper_limit=q_resp.range6_upper_limit,
                    range6_response_text=q_resp.range6_response_text
                ))

            data_to_return['form_question_responses'] = question_response

        except BaseException as e:
            self.db.rollback()
            err = str(e)
            return None, f'Internal Error code {err} while fetching forms'

        if (len(resp) == 0):
            return None, 'No Questions Found'
        return data_to_return, 'Questions found'

    def update_form_question(self, metadata: QuestionUpdate):
        question_exist = self.__get_question_by_id(metadata.question_id)
        if question_exist is None:
            return None, f'{metadata.question_id} does not exist.'

        question: DBQuestionTable = self.db.query(dbmodels.DBQuestionTable).filter(
            DBQuestionTable.question_id == metadata.question_id).one_or_none()

        question_new = DBQuestionTable(form_id=metadata.form_id,
                                       question_type=metadata.question_type, question_text=metadata.question_text,
                                       option1=metadata.option1, option1_point=metadata.option1_point,
                                       option2=metadata.option2, option2_point=metadata.option2_point,
                                       option3=metadata.option3, option3_point=metadata.option3_point,
                                       option4=metadata.option4, option4_point=metadata.option4_point,
                                       option5=metadata.option5, option5_point=metadata.option5_point,
                                       option6=metadata.option6, option6_point=metadata.option6_point,
                                       option7=metadata.option7, option7_point=metadata.option7_point,
                                       option8=metadata.option8, option8_point=metadata.option8_point,
                                       option9=metadata.option9, option9_point=metadata.option9_point,
                                       option10=metadata.option10, option10_point=metadata.option10_point)

        try:
            question.update(question_new)
            self.db.commit()

        except sqlalchemy.exc.SQLAlchemyError as d:
            self.db.rollback()
            err = str(d)
            return None, f'DB Error code {err} for updating question, with id {str(metadata.question_id)}'

        except BaseException as e:
            self.db.rollback()
            err = str(e)
            return None, f'Internal Error code {err} for updating question, with id {str(metadata.question_id)}'

        # logger.info(metadata)
        # logger.info('\n\n')
        # logger.info(question_new)

        return question_new, 'Question updated'

    def delete_form_question(self, metadata: QuestionDelete):
        question_exist = self.__get_question_by_id(metadata.question_id)
        if question_exist is None:
            return None, f'{metadata.question_id} does not exist.'

        question_to_delete: DBQuestionTable = self.db.query(dbmodels.DBQuestionTable).filter(
            DBQuestionTable.question_id == metadata.question_id).one_or_none()

        try:
            self.db.delete(question_to_delete)
            self.db.commit()

        except sqlalchemy.exc.SQLAlchemyError as d:
            self.db.rollback()
            err = str(d)
            return None, f'DB Error code {err} for deleting question, with id {str(metadata.question_id)}'

        except BaseException as e:
            self.db.rollback()
            err = str(e)
            return None, f'Internal Error code {err} for deleting question, with id {str(metadata.question_id)}'

        return 'Question deleted', 'Success'

    def add_new_question_response_to_form(self, metadata: QuestionResponseRangeAdd):
        uid = str(uuid.uuid4())

        db_new_question_response = DBFormResponseTable(
            response_id=uid,
            form_id=metadata.form_id,
            range1_lower_limit=metadata.range1_lower_limit,
            range1_upper_limit=metadata.range1_upper_limit,
            range1_response_text=metadata.range1_response_text,
            range2_lower_limit=metadata.range2_lower_limit,
            range2_upper_limit=metadata.range2_upper_limit,
            range2_response_text=metadata.range2_response_text,
            range3_lower_limit=metadata.range3_lower_limit,
            range3_upper_limit=metadata.range3_upper_limit,
            range3_response_text=metadata.range3_response_text,
            range4_lower_limit=metadata.range4_lower_limit,
            range4_upper_limit=metadata.range4_upper_limit,
            range4_response_text=metadata.range4_response_text,
            range5_lower_limit=metadata.range5_lower_limit,
            range5_upper_limit=metadata.range5_upper_limit,
            range5_response_text=metadata.range5_response_text,
            range6_lower_limit=metadata.range6_lower_limit,
            range6_upper_limit=metadata.range6_upper_limit,
            range6_response_text=metadata.range6_response_text
        )

        try:
            self.db.add(db_new_question_response)
            self.db.commit()

        except sqlalchemy.exc.SQLAlchemyError as d:
            self.db.rollback()
            err = str(d)
            return None, f'DB Error code {err} for adding question response'

        except BaseException as e:
            self.db.rollback()
            err = str(e)
            return None, f'Internal Error code {err} for adding question response'

        return db_new_question_response, 'Question response added'

    def update_question_response(self, metadata: QuestionResponseRangeUpdate):
        question_exist = self.__get_question_response_by_id(metadata.response_id)
        if question_exist is None:
            return None, f'{metadata.response_id} does not exist.'

        response: DBFormResponseTable = self.db.query(dbmodels.DBFormResponseTable).filter(
            DBFormResponseTable.response_id == metadata.response_id).one_or_none()

        question_response_new = DBFormResponseTable(
            form_id=metadata.form_id,
            range1_lower_limit=metadata.range1_lower_limit,
            range1_upper_limit=metadata.range1_upper_limit,
            range1_response_text=metadata.range1_response_text,
            range2_lower_limit=metadata.range2_lower_limit,
            range2_upper_limit=metadata.range2_upper_limit,
            range2_response_text=metadata.range2_response_text,
            range3_lower_limit=metadata.range3_lower_limit,
            range3_upper_limit=metadata.range3_upper_limit,
            range3_response_text=metadata.range3_response_text,
            range4_lower_limit=metadata.range4_lower_limit,
            range4_upper_limit=metadata.range4_upper_limit,
            range4_response_text=metadata.range4_response_text,
            range5_lower_limit=metadata.range5_lower_limit,
            range5_upper_limit=metadata.range5_upper_limit,
            range5_response_text=metadata.range5_response_text,
            range6_lower_limit=metadata.range6_lower_limit,
            range6_upper_limit=metadata.range6_upper_limit,
            range6_response_text=metadata.range6_response_text
        )

        try:
            response.update(question_response_new)
            self.db.commit()

        except sqlalchemy.exc.SQLAlchemyError as d:
            self.db.rollback()
            err = str(d)
            return None, f'DB Error code {err} for updating question response, with id {str(metadata.response_id)}'

        except BaseException as e:
            self.db.rollback()
            err = str(e)
            return None, f'Internal Error code {err} for updating question response, with id {str(metadata.response_id)}'

        question_response_new.response_id = metadata.response_id
        return question_response_new, 'Question response updated'

    def delete_question_response(self, metadata: QuestionResponseDelete):
        question_exist = self.__get_question_response_by_id(metadata.response_id)
        if question_exist is None:
            return None, f'{metadata.response_id} does not exist.'

        question_response_to_delete: DBFormResponseTable = self.db.query(dbmodels.DBFormResponseTable).filter(
            DBFormResponseTable.response_id == metadata.response_id).one_or_none()

        try:
            self.db.delete(question_response_to_delete)
            self.db.commit()

        except sqlalchemy.exc.SQLAlchemyError as d:
            self.db.rollback()
            err = str(d)
            return None, f'DB Error code {err} for deleting response, with id {str(metadata.response_id)}'

        except BaseException as e:
            self.db.rollback()
            err = str(e)
            return None, f'Internal Error code {err} for deleting response, with id {str(metadata.response_id)}'

        return {'msg': 'Question response deleted'}, 'Success'

    def add_list_names_of_metadata(self, data: MetadataListName):
        try:
            list_name_exists = self.mongo_db['MetaData'].find_one(dict(metadata_name=data.list_name))
            if list_name_exists:
                return dict(msg='List already exists', list_id=list_name_exists['metadata_id'],
                            list_name=list_name_exists['metadata_name'])
            else:
                list_id = str(uuid.uuid4())
                self.mongo_db['MetaData'].insert_one(dict(metadata_id=list_id, metadata_name=data.list_name))
                return dict(msg='List name added', list_id=list_id,
                            list_name=data.list_name)
        except Exception as e:
            raise HTTPException(status_code=409, detail=f'Error occurred as {str(e)} while adding list name')

    def update_list_names_of_metadata(self, data: MetadataListNameUpdate):
        try:
            list_exists = self.mongo_db['MetaData'].find_one(dict(metadata_id=data.list_id))
            if list_exists is None:
                raise Exception('Invalid list id')

            self.mongo_db['MetaData'].find_one_and_update(dict(metadata_id=data.list_id),
                                                          {'$set': {'metadata_name': data.list_name}})
            return dict(msg='List name updated', list_id=data.list_id,
                        list_name=data.list_name)
        except Exception as e:
            raise HTTPException(status_code=409, detail=f'Error occurred as {str(e)} while updating list name')

    def get_list_names_of_metadata(self):
        try:
            list_exists = self.mongo_db['MetaData'].find()
            list_data = []
            for data in list(list_exists.clone()):
                list_data.append(dict(list_id=data['metadata_id'], list_name=data['metadata_name']))
            # logger.info(list_data)
            return dict(msg='List found', list_data=list_data)
        except Exception as e:
            raise HTTPException(status_code=409, detail=f'Error occurred as {str(e)} while getting list name')

    def append_dropdown_elements_to_existing_record(self, data: DropdownMetadataAdd):
        try:
            list_data = self.mongo_db['MetaData'].find_one(dict(metadata_id=data.list_id))
            if list_data is None:
                raise Exception('Invalid list id')

            list_items = list_data['list_items']
            for option in data.options:
                if option not in list_items:
                    list_items.append(option)

            self.mongo_db['MetaData'].find_one_and_update(dict(metadata_id=data.list_id),
                                                          {'$set': {'list_items': list_items}})

        except Exception as e:
            raise Exception(f'{str(e)}')

    def add_dropdown_elements(self, data: DropdownMetadata):
        try:
            list_exists = self.mongo_db['MetaData'].find_one(
                {"$or": [{"metadata_id": data.list_id_or_name}, {"metadata_name": data.list_id_or_name}]})
            if list_exists is None:
                raise Exception('Invalid list id or name')
            # list_exists = self.mongo_db['MetaData'].find_one(dict(metadata_id=data.list_id))
            # if list_exists is None:
            #     raise Exception('Invalid list id')

            if 'list_items' in list_exists:
                if len(list_exists['list_items']) > 0:
                    self.append_dropdown_elements_to_existing_record(
                        data=DropdownMetadataAdd(list_id=list_exists['metadata_id'], options=data.options))
                else:
                    self.mongo_db['MetaData'].find_one_and_update(dict(metadata_id=list_exists['metadata_id']),
                                                                  {'$set': {'list_items': data.options}})

            else:
                self.mongo_db['MetaData'].find_one_and_update(dict(metadata_id=list_exists['metadata_id']),
                                                              {'$set': {'list_items': data.options}})

            get_updated_list = self.mongo_db['MetaData'].find_one(dict(metadata_id=list_exists['metadata_id']))

            return dict(msg='List items added', list_id=list_exists['metadata_id'],
                        list_name=get_updated_list['metadata_name'],
                        list_items=get_updated_list['list_items'])
        except Exception as e:
            raise HTTPException(status_code=409, detail=f'Error occurred as {str(e)} while adding dropdown elements')

    def update_dropdown_elements(self, data: DropdownMetadataUpdate):
        try:
            list_exists = self.mongo_db['MetaData'].find_one(dict(metadata_id=data.list_id))
            if list_exists is None:
                raise Exception('Invalid list id')

            list_items = list_exists['list_items']
            if data.option_name not in list_items:
                raise Exception('List item not found')
            list_items.remove(data.option_name)
            list_items.append(data.new_option)

            self.mongo_db['MetaData'].find_one_and_update(dict(metadata_id=data.list_id),
                                                          {'$set': {'list_items': list_items}})

            get_updated_list = self.mongo_db['MetaData'].find_one(dict(metadata_id=data.list_id))

            return dict(msg='List items updated', list_id=data.list_id, list_name=get_updated_list['metadata_name'],
                        list_items=get_updated_list['list_items'])
        except Exception as e:
            raise HTTPException(status_code=409, detail=f'Error occurred as {str(e)} while updating dropdown elements')

    def delete_dropdown_elements(self, data: DropdownMetadataDelete):
        try:
            list_exists = self.mongo_db['MetaData'].find_one(dict(metadata_id=data.list_id))
            if list_exists is None:
                raise Exception('Invalid list id')

            list_items = list_exists['list_items']
            if data.option_name not in list_items:
                raise Exception('List item not found')
            list_items.remove(data.option_name)

            self.mongo_db['MetaData'].find_one_and_update(dict(metadata_id=data.list_id),
                                                          {'$set': {'list_items': list_items}})

            get_updated_list = self.mongo_db['MetaData'].find_one(dict(metadata_id=data.list_id))

            return dict(msg='List items deleted', list_id=data.list_id, list_name=get_updated_list['metadata_name'],
                        list_items=get_updated_list['list_items'])
        except Exception as e:
            raise HTTPException(status_code=409, detail=f'Error occurred as {str(e)} while deleting dropdown elements')

    def get_dropdown_elements(self, data: DropdownMetadataSearch):
        try:
            list_exists = self.mongo_db['MetaData'].find_one(
                {"$or": [{"metadata_id": data.list_id_or_name}, {"metadata_name": data.list_id_or_name}]})
            if list_exists is None:
                raise Exception('Invalid list id or name')

            return dict(msg='List items found', list_id=list_exists['metadata_id'],
                        list_name=list_exists['metadata_name'],
                        list_items=list_exists['list_items'])
        except Exception as e:
            raise HTTPException(status_code=409, detail=f'Error occurred as {str(e)} while getting dropdown elements')

    def get_sample_csv_file_for_bulk_meds_upload(self):
        try:
            return {
                'medicine_sample_download_s3_link': 'https://ayoo-web-bucket.s3.ap-south-1.amazonaws.com/template-forms/medicine+salts+sample.csv'}
        except Exception as e:
            return None, f'error occurred as {str(e)} while downloading sample form csv file'

    def add_medicine_in_mongo(self, meds: AddMedicineToCatalogue):
        try:
            drug_exists = self.mongo_db['DrugCatalogue'].find_one(dict(meds))
            if drug_exists is None:
                medicine_id = str(uuid.uuid4())
                med_to_insert = dict(medicine_id=medicine_id)
                med_to_insert.update(dict(meds))
                self.mongo_db['DrugCatalogue'].insert_one(med_to_insert)
                return med_to_insert
            else:
                return False
        except Exception as e:
            raise Exception(str(e))

    def add_medicine_in_ayoo_drug_catalogue_csv_file(self, data: UploadFile):
        try:
            data = pd.read_csv(data.file)
            csv_data = data.to_dict(orient="records")
            for ele in csv_data:
                self.add_medicine_in_mongo(AddMedicineToCatalogue(medicine_salt=ele['medicine_salt'],
                                                                  medicine_brand=ele['medicine_brand'],
                                                                  drug_form=ele['drug_form'],
                                                                  route=ele['route'],
                                                                  dose=ele['dose']))

            return {'msg': 'Medicine data saved'}
        except Exception as e:
            raise HTTPException(status_code=409, detail=f'Error occurred as {str(e)} while adding medicines')

    def add_single_medicine_in_ayoo_drug_catalogue(self, data: AddMedicineToCatalogue):
        try:
            add_meds = self.add_medicine_in_mongo(data)
            if add_meds is False:
                return {'msg': 'Medicine data already exists', 'drug_details': data}
            return {'msg': 'Medicine data added', 'drug_details': data}
        except Exception as e:
            raise HTTPException(status_code=409, detail=f'Error occurred as {str(e)} while adding medicines')

    def update_single_medicine_in_ayoo_drug_catalogue(self, data: UpdateMedicineInCatalogue):
        try:
            drug_exists = self.mongo_db['DrugCatalogue'].find_one(
                dict(medicine_salt=data.medicine_salt, medicine_brand=data.medicine_brand, drug_form=data.drug_form,
                     route=data.route, dose=data.dose))
            if drug_exists:
                raise Exception('Medicine already exists')

            self.mongo_db['DrugCatalogue'].find_one_and_update(dict(medicine_id=data.medicine_id), {
                "$set": dict(medicine_salt=data.medicine_salt,
                             medicine_brand=data.medicine_brand,
                             drug_form=data.drug_form,
                             route=data.route,
                             dose=data.dose)})
            add_meds = self.add_medicine_in_mongo(data)
            if add_meds is None:
                return {'msg': 'Medicine data already exists', 'drug_details': data}
            return {'msg': 'Medicine data updated', 'drug_details': data}
        except Exception as e:
            raise HTTPException(status_code=409, detail=f'Error occurred as {str(e)} while updating medicines')

    def delete_single_medicine_in_ayoo_drug_catalogue(self, data: DeleteMedicineFromCatalogue):
        try:
            drug_exists = self.mongo_db['DrugCatalogue'].find_one(dict(data))
            if drug_exists is None:
                raise Exception('Medicine does not exist')

            self.mongo_db['DrugCatalogue'].find_one_and_delete(dict(data))
            return {'msg': 'Medicine data deleted',
                    'drug_details': dict(medicine_id=data.medicine_id,
                                         medicine_salt=drug_exists['medicine_salt'],
                                         medicine_brand=drug_exists['medicine_brand'],
                                         drug_form=drug_exists['drug_form'],
                                         route=drug_exists['route'],
                                         dose=drug_exists['dose'])}
        except Exception as e:
            raise HTTPException(status_code=409, detail=f'Error occurred as {str(e)} while deleting medicines')

    def get_complete_drug_catalogue(self):
        try:
            drug_exists = self.mongo_db['DrugCatalogue'].find()

            if drug_exists is None:
                raise Exception('No medicine exist')

            return_med_data = []
            for med in list(drug_exists.clone()):
                return_med_data.append(dict(medicine_id=med['medicine_id'],
                                            medicine_salt=med['medicine_salt'],
                                            medicine_brand=med['medicine_brand'],
                                            drug_form=med['drug_form'],
                                            route=med['route'],
                                            dose=med['dose']))

            return {'msg': 'Medicine data found',
                    'drugs': return_med_data}
        except Exception as e:
            raise HTTPException(status_code=409, detail=f'Error occurred as {str(e)} while getting medicines')

    def get_medicine_salts(self, data: GetMedicineSaltFromBrand):
        try:
            if data.medicine_brand == "":
                all_salts = list(self.mongo_db['DrugCatalogue'].find({}, {"_id": 0, "medicine_salt": 1}).clone())
                medicine_salts = sorted({meds["medicine_salt"] for meds in all_salts})
            else:
                medicine_salts = self.mongo_db['DrugCatalogue'].distinct("medicine_salt",
                                                                         {"medicine_brand": data.medicine_brand})

            return {'msg': 'Medicine brand found',
                    'medicine_brand': data.medicine_brand,
                    'medicine_salts': medicine_salts
                    }
        except Exception as e:
            raise HTTPException(status_code=409, detail=f'Error occurred as {str(e)} while getting medicine salts')

    def get_medicine_brands_from_salts(self, data: GetMedicineBrandFromSalt):
        try:
            if data.medicine_salt == "":
                all_brands = list(self.mongo_db['DrugCatalogue'].find({}, {"_id": 0, "medicine_brand": 1}).clone())
                medicine_brands = sorted({meds["medicine_brand"] for meds in all_brands})
            else:
                medicine_brands = self.mongo_db['DrugCatalogue'].distinct("medicine_brand",
                                                                          {"medicine_salt": data.medicine_salt})

            return {'msg': 'Medicine brand found',
                    'medicine_salt': data.medicine_salt,
                    'medicine_brands': medicine_brands
                    }
        except Exception as e:
            raise HTTPException(status_code=409, detail=f'Error occurred as {str(e)} while getting medicine brands')

    def get_medicine_dose_and_drug_form(self, data: GetMedicineDoseAndDrugFormFromBrandAndSalt):
        try:
            medicine_brands = self.mongo_db['DrugCatalogue'].find(dict(data))

            data_to_return = []

            for brand in list(medicine_brands.clone()):
                data_to_return.append(dict(medicine_id=brand['medicine_id'],
                                           # medicine_salt=brand['medicine_salt'],
                                           # medicine_brand=brand['medicine_brand'],
                                           drug_form=brand['drug_form'],
                                           route=brand['route'],
                                           dose=brand['dose']
                                           ))

            return {'msg': 'Medicine drug form found',
                    'medicine_salt': data.medicine_salt,
                    'medicine_brand': data.medicine_brand,
                    'medicine_details': data_to_return}
        except Exception as e:
            raise HTTPException(status_code=409, detail=f'Error occurred as {str(e)} while getting medicine details')

    def add_icd_in_mongo(self, icd_codes: AddICDCodeToCatalogue):
        try:
            icdCode_exists = self.mongo_db['ICDCodes'].find_one(dict(icd_codes))
            if icdCode_exists is None:
                self.mongo_db['ICDCodes'].insert_one(dict(icd_codes))
                return icd_codes
            else:
                return False
        except Exception as e:
            raise Exception(str(e))

    def add_icd_codes_by_csv(self, data: UploadFile):
        try:
            data = pd.read_csv(data.file)
            df = data.to_dict(orient="records")
            for ele in df:
                self.add_icd_in_mongo(AddICDCodeToCatalogue(icd_code=ele['Code'] if str(ele['Code']) != "nan" else '',
                                                            icd_block_id=ele['BlockId'] if str(
                                                                ele['BlockId']) != "nan" else '',
                                                            icd_block_title=ele['Block_Title'].lstrip(
                                                                '- ').strip() if str(
                                                                ele['Block_Title']) != "nan" else '',
                                                            icd_title=ele['Title'].lstrip('- ').strip(),
                                                            icd_chapter=ele['ChapterNo'],
                                                            icd_class_kind=ele['ClassKind']
                                                            ))

            return {'msg': 'ICD Codes data saved'}
        except Exception as e:
            raise HTTPException(status_code=409, detail=f'Error occurred as {str(e)} while adding ICD Codes')

    def get_icd_codes(self):
        try:
            icd_codes = self.mongo_db['ICDCodes'].find()
            icd_codes_resp = []

            for data in list(icd_codes.clone()):
                icd_codes_resp.append(dict(
                    icd_code=data['icd_code'],
                    icd_title=data['icd_title']
                ))
            return icd_codes_resp
        except Exception as e:
            raise Exception(str(e))

    def search_by_block(self, data: SearchICDCodeByBlock):
        try:
            icd_category_list = self.mongo_db['ICDCodes'].find(
                {'icd_block_title': {"$regex": data.search_string, '$options': 'i'}})
            icd_codes_resp = []

            for data in list(icd_category_list.clone()):
                icd_codes_resp.append(dict(
                    icd_code=data['icd_code'],
                    icd_title=data['icd_title']
                ))
            return icd_codes_resp
        except Exception as e:
            raise Exception(str(e))
