import json
import hashlib
from typing import List
from Crypto.Cipher import <PERSON><PERSON>
from datetime import datetime
from fastapi import HTTPException
import requests
from ..ayoo_utils import encrypt_password
from ..api_configs import p_cancel_url, p_redirect_url, T_url, CCAVENUE_WORKING_KEY, CCAVENUE_MERCHANT_ID, CCAVENUE_ACCESS_CODE
from ..revenue_models import Payment, Status, Paymentview, AdhocPaymentStatusRequest, AdhocPaymentStatusResponse
from ..dbmodels import DBUser
from ..DataModels.payment import CustomFees
from ..DAOs.userDAO import UserDAO
from ..DAOs.paymentDAO import PaymentDAO, CustomFeesDAO, GetCustomFeeForDeleteParams
from ..mongodb import mongodb_conn


access_code = CCAVENUE_ACCESS_CODE
merchant_id = CCAVENUE_MERCHANT_ID
working_key = CCAVENUE_WORKING_KEY


def pad(data):
    length = 16 - (len(data) % 16)
    data += chr(length) * length
    return data


def encrypt(plainText, workingKey):
    iv = bytes(
        '\x00\x01\x02\x03\x04\x05\x06\x07\x08\x09\x0a\x0b\x0c\x0d\x0e\x0f', 'utf-8')
    plainText = pad(plainText)
    res = bytes(plainText, 'utf-8')
    encDigest = hashlib.md5(workingKey.encode('utf-8'))
    enc_cipher = AES.new(encDigest.digest(), AES.MODE_CBC, iv)
    encryptedText = enc_cipher.encrypt(res).hex()
    return encryptedText


async def get_normal_payment_url(amount: str, secret_key: str, order_id: str, user_id: str):
    passwd = encrypt_password(secret_key)
    redirect_url = p_redirect_url[1:]+"?secret_key="+passwd
    redirect_url = redirect_url.replace('"', '')
    cancel_url = p_cancel_url[1:] + "?secret_key=" + passwd
    cancel_url = cancel_url.replace('"', '')
    # "tid": order_id.replace("-", ""),
    data_to_encrypt = {
        "merchant_id": merchant_id,
        "order_id": order_id,
        "currency": "INR",
        "amount": amount,
        "redirect_url": redirect_url,
        "cancel_url": cancel_url,
        "passwd": passwd,
        "language": "EN",
        "integration_type": "iframe_normal",
    }

    merchant_data = ""
    for key, value in data_to_encrypt.items():
        merchant_data += f"{key}={value}&"

    merchant_data = merchant_data[:-1]

    encryption = encrypt(merchant_data, working_key)
    final_url = f"{T_url}{encryption}&access_code={access_code}",
    return final_url


def initiate_payment(paymentview: Paymentview, order_id: str, secret_key):
    resp_user: DBUser = UserDAO().get(paymentview.user_id)

    if not resp_user:
        detail = f"user not found for {paymentview.user_id}"
        raise HTTPException(409, detail)

    promo_code = paymentview.promo_code if paymentview.promo_code else ""

    payment_info = Payment(
        user_id=paymentview.user_id,
        order_id=order_id,
        amount=paymentview.amount,
        promo_code=promo_code,
        appointment_id=paymentview.appointment_id,
        appointment_or_membership=paymentview.appointment_or_membership,
        payment_status="initiate",
        payment_date=datetime.now(),
        secret_key=secret_key,
        payment_card='',
        payment_mode=''
    )

    PaymentDAO().create(payment_info)
    return order_id, 'data saved successfully'


async def add_zero_payment_entry(paymentview: Paymentview, order_id: str, secret_key):
    resp_user: DBUser = UserDAO().get(paymentview.user_id)

    if resp_user is None:
        return '', 'user not found'

    payment_info = Payment(
        user_id=paymentview.user_id,
        order_id=order_id,
        amount=paymentview.amount,
        promo_code=paymentview.promo_code,
        appointment_id=paymentview.appointment_id,
        appointment_or_membership=paymentview.appointment_or_membership,
        payment_status=Status.success,
        payment_date=datetime.now(),
        secret_key=secret_key,
        payment_card='',
        payment_mode=''
    )

    PaymentDAO().create(payment_info.dict())
    return order_id, 'data saved successfully'


def get_adhoc_mechant_json(name: str, mobile: str, email: str, amount: str):
    merchant_json_data = {
        'merchant_id': merchant_id,
        'customer_name': name,
        'bill_delivery_type': 'BOTH',
        'customer_mobile_no': mobile,
        'customer_email_id': email,
        'customer_email_subject': 'Ayoo Care Invoice Payment active for 15 Minutes',
        'invoice_description': 'This link is active for 15 minutes',
        'currency': 'INR',
        'valid_for': 15,
        'valid_type': 'minutes',
        'amount': amount,
        "sms_content": "Pls call 022-2121212121topayyourLegalEntity_Namebill# Invoice_IDfor Invoice_CurrencyInvoice_AmountorpayonlineatPay_Link."
    }

    merchant_data_json = json.dumps(merchant_json_data)
    encrypted_data = encrypt(merchant_data_json, working_key)

    payload = {
        'enc_request': encrypted_data,
        'access_code': access_code,
        'command': 'generateQuickInvoice',
        'request_type': 'JSON',
        'response_type': 'JSON',
        "version": '1.2'
    }

    return payload


def get_payment_DAO(order_id: str, user_id: str, amount: str, appointment_id: str, promo_code: str, payment_status: str, is_activity_confirmed: bool, url: str):
    payment_gateway_data_dict = Payment(
        order_id=order_id,
        transaction_id='',
        user_id=user_id,
        amount=amount,
        appointment_id=appointment_id,
        appointment_or_membership="appointment",
        payment_status=payment_status,
        promo_code=promo_code,
        doctor_id="",
        case_id="",
        payment_date=datetime.now(),
        secret_key="",
        is_activity_confirmed=is_activity_confirmed,
        refund_details={},
        url=url,
        payment_mode='',
        payment_card=''
    )
    return payment_gateway_data_dict


def remove_custom_fees(patient_id: str, case_id: str):
    query = GetCustomFeeForDeleteParams(patient_id=patient_id, case_id=case_id)
    cases: List[CustomFees] = CustomFeesDAO().list(filters=query)
    for case in cases:
        CustomFeesDAO().delete(case.id)
    return "ok"


def decrypt_order_status(cipher_text, working_key):
    """Decrypt CCAvenue response"""
    try:
        iv = bytes('\x00\x01\x02\x03\x04\x05\x06\x07\x08\x09\x0a\x0b\x0c\x0d\x0e\x0f', 'utf-8')
        enc_digest = hashlib.md5(working_key.encode('utf-8')).digest()
        dec_cipher = AES.new(enc_digest, AES.MODE_CBC, iv)
        decrypted_text = dec_cipher.decrypt(bytes.fromhex(cipher_text))
        text = decrypted_text.rstrip(b'\0').decode('utf-8')
        return text
    except Exception as e:
        raise Exception(f'Error decrypting CCAvenue response: {str(e)}')


async def check_adhoc_payment_status(request: AdhocPaymentStatusRequest) -> AdhocPaymentStatusResponse:
    """Check adhoc payment status using CCAvenue orderStatusTracker API"""
    try:
        # First check if the payment exists in our database and is adhoc
        mongo_db = mongodb_conn.get_database()
        payment_record = mongo_db['Paymentgateway3'].find_one({
            "order_id": request.order_id,
            "appointment_or_membership": "adhoc"
        })

        if not payment_record:
            return AdhocPaymentStatusResponse(
                order_id=request.order_id,
                payment_status="not_found",
                message="Adhoc payment record not found"
            )

        # Prepare request data for CCAvenue orderStatusTracker
        merchant_json_data = {
            "order_no": request.order_id,
            "reference_no": payment_record.get('transaction_id', '')
        }

        merchant_data_json = json.dumps(merchant_json_data)
        encrypted_data = encrypt(merchant_data_json, working_key)

        # Prepare API payload
        payload = {
            'enc_request': encrypted_data,
            'access_code': access_code,
            'command': 'orderStatusTracker',
            'request_type': 'JSON',
            'response_type': 'JSON'
        }

        # Make API call to CCAvenue
        response = requests.post("https://apitest.ccavenue.com/apis/servlet/DoWebTrans", data=payload)

        if response.status_code == 200:
            # Parse the encrypted response
            response_parts = response.text.split('&')
            encrypted_response = None

            for part in response_parts:
                if part.startswith('enc_response='):
                    encrypted_response = part.split('=', 1)[1]
                    break

            if encrypted_response:
                # Decrypt the response
                decrypted_response = decrypt_order_status(encrypted_response, working_key)

                # Parse the decrypted response
                response_data = {}
                for pair in decrypted_response.split('&'):
                    if '=' in pair:
                        key, value = pair.split('=', 1)
                        response_data[key] = value

                # Extract payment details
                order_status = response_data.get('order_status', payment_record.get('payment_status', 'Unknown'))
                transaction_id = response_data.get('tracking_id', payment_record.get('transaction_id', ''))
                payment_mode = response_data.get('payment_mode', payment_record.get('payment_mode', ''))
                payment_card = response_data.get('card_name', payment_record.get('payment_card', ''))

                # Update database with latest status
                update_data = {
                    'payment_status': order_status,
                    'transaction_id': transaction_id,
                    'payment_mode': payment_mode,
                    'payment_card': payment_card,
                    'payment_date': datetime.now()
                }

                mongo_db['Paymentgateway3'].find_one_and_update(
                    {"order_id": request.order_id},
                    {'$set': update_data}
                )

                return AdhocPaymentStatusResponse(
                    order_id=request.order_id,
                    payment_status=order_status,
                    transaction_id=transaction_id,
                    payment_mode=payment_mode,
                    payment_card=payment_card,
                    updated_from_gateway=True,
                    message="Payment status updated from CCAvenue gateway"
                )
            else:
                # Return current database status if gateway response parsing fails
                return AdhocPaymentStatusResponse(
                    order_id=request.order_id,
                    payment_status=payment_record.get('payment_status', 'Unknown'),
                    transaction_id=payment_record.get('transaction_id', ''),
                    payment_mode=payment_record.get('payment_mode', ''),
                    payment_card=payment_record.get('payment_card', ''),
                    updated_from_gateway=False,
                    message="Could not parse gateway response, returning database status"
                )
        else:
            # Return current database status if API call fails
            return AdhocPaymentStatusResponse(
                order_id=request.order_id,
                payment_status=payment_record.get('payment_status', 'Unknown'),
                transaction_id=payment_record.get('transaction_id', ''),
                payment_mode=payment_record.get('payment_mode', ''),
                payment_card=payment_record.get('payment_card', ''),
                updated_from_gateway=False,
                message=f"CCAvenue API call failed with status {response.status_code}"
            )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error checking payment status: {str(e)}")
