from datetime import datetime, timed<PERSON>ta
from enum import Enum

from pydantic import constr, root_validator, validator, Field

from .basemodel import BaseMongoModel, BaseModel, BaseMongoSubEntityModel
from typing import List, Optional
import re


class PrescriptionType(str, Enum):
    Psychiatry = 'Psychiatry'
    Therapy = 'Therapy'
    MedicalHealth = 'MedicalHealth'


class AppointmentType(str, Enum):
    InClinic = 'InClinic'
    Virtual = 'Virtual'
    # ConvenienceBased='ConvenienceBased'


class ConsultationType(str, Enum):
    Appointment = 'Appointment'
    ReIssue = 'ReIssue'
    Modify = 'Modify'
    ConvenienceBased = 'ConvenienceBased'
    # FollowUp = 'FollowUp' # Appointment


pdf_keys = [
    'assessment',
    'medication',
    'work_up_plan_or_recommendation',
    'therapy_recommendation',
    'follow_up',
    'lab_tests'
]


class ChiefComplain(BaseMongoSubEntityModel):
    complain_text: constr(min_length=3, max_length=300)
    onset: constr(min_length=3, max_length=300)


class SubstanceCurrentState(str, Enum):
    NoUsage = 'No Usage'
    CurrentUse = 'Current Use'
    CurrentAbuse = 'Current Abuse'
    CurrentDependence = 'Current Dependence'
    InRecovery = 'In Recovery'
    EarlyRemission = 'Early Remission'
    SustainedRemission = 'Sustained Remission'


class SubstanceUse(BaseMongoSubEntityModel):
    substance: constr(min_length=1)
    first_use: constr(min_length=1)
    frequency: constr(min_length=1)
    current_state: SubstanceCurrentState
    perceived_problem: constr(min_length=1)

    class Config:
        use_enum_values = True


class Assessment(BaseMongoSubEntityModel):
    icd_code: Optional[str] = None
    title: constr(min_length=1)
    comments: Optional[str] = None
    rule_out_or_remarks: Optional[str] = None
    allowed_to_patient: bool = True
    # issued_to_patient: bool = None


class MedsFrequency(BaseModel):
    mor: Optional[str] = None
    aft: Optional[str] = None
    eve: Optional[str] = None
    custom: Optional[str] = None


class Medication(BaseMongoSubEntityModel):
    medicine_id: str = None
    medicine_salt: constr(min_length=3)
    medicine_brand: Optional[constr(min_length=3)]
    drug_form: Optional[str] | None = None
    dose: str = None
    frequency: MedsFrequency | None = None
    start_date: Optional[str] | None = None  # optional when sos true
    duration_in_days: Optional[int] = 0  # optional when sos true
    end_date: Optional[str] = None  # optional when sos true
    instructions: Optional[str] | None = None
    sos: bool = False
    instructions_for_pharmacy: Optional[str] | None = None  # should be filled when sos is true
    instructions_for_patient: Optional[str] | None = None  # should be filled when sos is true

    # is_changed_medicine: Optional[bool] = False
    # issued_to_patient: bool = None

    @validator('end_date', always=True, pre=False)
    def set_end_date(cls, v, values):
        start_date = values.get('start_date')
        duration_in_days = values.get('duration_in_days', 0)
        sos = values.get('sos', False)

        if start_date and duration_in_days > 0 and not sos:
            start_date_obj = datetime.strptime(start_date, "%Y-%m-%d")
            end_date_obj = start_date_obj + timedelta(days=duration_in_days)
            return end_date_obj.strftime("%Y-%m-%d")

        return v


class WorkUpPlanOrRecommendationFrequency(str, Enum):
    Daily = 'Daily'
    Weekly = 'Weekly'
    Monthly = 'Monthly'
    AsNeeded = 'As Needed'


class WorkUpPlanOrRecommendation(BaseMongoSubEntityModel):
    description: Optional[str] | None = None  # former key name - problem
    goal: Optional[str] | None = None
    activities: Optional[str] | None = None
    frequency: WorkUpPlanOrRecommendationFrequency | None = None
    task_reminder: bool = None
    comments: Optional[str] | None = None
    allowed_to_patient: bool = None
    # issued_to_patient: bool = None


class Recommendation(BaseMongoSubEntityModel):
    comments: constr(min_length=2) = None
    allowed_to_patient: bool = None
    # issued_to_patient: bool = None


class FollowUp(BaseMongoSubEntityModel):
    objectives: Optional[str] = ''
    next_appointment: datetime | None = None
    # next_appointment: Optional[str] = ''
    appointment_type: AppointmentType | None = None
    ayoo_check_in_days: str | None = None  # YYYY-MM-DD

    # issued_to_patient: bool = None

    @validator('next_appointment', always=True)
    def set_next_appointment(cls, v):
        if v and v.date() == datetime.now().date():
            return None
        return v

    class Config:
        use_enum_values = True


class MediaFileStore(BaseModel):
    file_id: str = None
    file_name: str = None
    file_tag: str = None
    file_path: str = None


class DoctorNotes(BaseMongoSubEntityModel):
    notes: str = None
    audio_files: Optional[List[MediaFileStore]] = Field(default_factory=list)
    images: Optional[List[MediaFileStore]] = Field(default_factory=list)
    docs: Optional[List[MediaFileStore]] = Field(default_factory=list)
    is_reissue_notes: bool = False  # if this is true, notes shall not be editable
    is_modify_notes: bool = False  # if this is true, notes shall not be editable


class LabTests(BaseModel):
    lab_test_name: constr(min_length=2)


class ReferralFor(str, Enum):
    Therapy = 'Therapy'
    Psychiatry = 'Psychiatry'
    MedicalHealth = 'Medical Health'


class ReferringPersonTypes(str, Enum):
    Referrer = 'Referrer'
    Referee = 'Referee'


class ReferralTypes(str, Enum):
    External = 'External'
    Internal = 'Internal'


class UpdateToReferrer(BaseModel):
    referrer_case_id: str
    current_case_id: str
    appointment_id: str
    notes: constr(min_length=2)


class UpdateToReferee(BaseModel):
    referee_case_id: str
    current_case_id: str
    appointment_id: str
    notes: constr(min_length=2)


class ReferralUpdateWithDoctorInfo(BaseModel):
    note_id: str
    appointment_no: str
    notes: str
    date: str
    updates_from_doctor_id: str
    updates_from_doctor_name: str
    updates_from_type: ReferringPersonTypes
    date_recorded: datetime


class ReferCaseView(BaseModel):
    current_case_id: str
    referral_type: ReferralTypes = 'Internal'
    referred_to_doctor: str | None  # doctor id
    external_referral_doctor_name: str | None = None
    appointment_id: str
    notes: str = ''


class CaseSummary(BaseMongoSubEntityModel):
    case_closure_diagnosis: constr(min_length=2) = None
    case_closure_doctor_notes: constr(min_length=2) = None
    case_closure_recommendation: constr(min_length=2) = None
    transfer_case: bool = False
    transfer_details: ReferCaseView | None = None


class ReferralInformation(BaseMongoModel):
    patient_id: str
    existing_case_id: str
    referral_type: ReferralTypes = 'Internal'
    referred_case_id: str | None = None
    is_case_transferred: bool = False  # if True - close existing case
    referred_by: str  # doctor id - referrer
    referred_to: str | None = None  # doctor id - referee
    referred_by_name: str  # doctor name - referrer's name
    referred_to_name: str  # doctor name - referee's name
    # referral_for: ReferralFor
    updates: List[ReferralUpdateWithDoctorInfo] | None = []
    date_referred: datetime


class Prescriptions(BaseMongoSubEntityModel):
    allow_to_patient: bool = False
    issue_date: datetime
    s3_object_key: str = None
    s3_object_url: str = None
    is_reissued_prescription: bool = False


class PrescriptionUpdateType(str, Enum):
    ReIssue = 'ReIssue'
    Modify = 'Modify'


class UpdatePrescription(BaseModel):
    update_type: PrescriptionUpdateType = 'ReIssue'
    update_reason: constr(min_length=2)


class CaseSheet(BaseMongoModel):
    case_sheet_id: str
    appointment_id: str
    appointment_slot: datetime
    patient_id: str
    case_id: str
    is_open: bool = True
    date_open: datetime
    date_closed: datetime = None
    case_doctor: str
    doctor_name: str
    prescription_type: PrescriptionType
    appointment_type: AppointmentType = 'Virtual'
    consultation_type: ConsultationType = 'Appointment'
    session_no: int = 1
    chief_complain: Optional[List[ChiefComplain]] = None
    doctor_notes: List[DoctorNotes] = list()
    substance_use: Optional[List[SubstanceUse]] = None
    assessment: Optional[List[Assessment]] = None
    # lab_test: Optional[List] = None # not for therapy
    medication: Optional[List[Medication]] = None  # not for therapy
    work_up_plan_or_recommendation: Optional[List[WorkUpPlanOrRecommendation]] = None  # not for therapy
    therapy_recommendation: Optional[List[Recommendation]] = None  # for therapy only
    follow_up: Optional[FollowUp] = None
    follow_up_objective: Optional[str] = None
    case_summary: Optional[CaseSummary] = None  # this will be stored in the last record of appointment

    prescription_count: int = 1
    prescription_preview: bool = False  # kept to keep track if the prescription is in preview mode
    is_prescription_issued: bool = False  # This will be true if prescription is issued even if one time against one apnt id
    is_prescription_issued_for_current_record: bool = False  # This key is for each prescription record

    case_sheet_in_edit_mode: bool = True  # Other fields except prescription pdf fields --- False if consultation_type is Re-Issue
    prescription_fields_in_edit_mode: bool = True  # False if consultation_type is Re-Issue, False if prescription is issued
    is_case_sheet_submitted: bool = False
    case_sheet_submit_date: datetime | None = None

    prescription_allowed_to_patient: bool = False  # generally true
    prescription_issue_date: datetime | None = None  # this is generally same as case sheet submit date but might be situations where it would differ

    prescription_s3_object_key: str = None
    prescription_s3_object_url: str = None

    is_reissue_prescription: bool = False
    reissue_reason: str = None  # kept in case of directly accessing the reason for re-issue

    is_modified_prescription: bool = False
    modify_reason: str = None  # kept in case of directly accessing the reason for modify


class ClinicalHistory(BaseModel):
    case_id: str
    case_doctor: str
    doctor_name: str = None
    is_open: bool
    date_open: datetime
    date_closed: Optional[datetime] = None
    prescription_type: str
    case_summary: dict | None = None
    case_transferred_to: dict | None = None


class ShareCaseSheet(BaseModel):
    email: Optional[str] = ''
    email_admin: bool = False
    pdf_url: str
    patient_name: Optional[str] = ''
    patient_phone: Optional[str] = ''
    case_id: str
