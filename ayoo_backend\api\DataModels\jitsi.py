from .basemodel import BaseMongoModel
from datetime import datetime
from typing import Optional

class PatientDetails(BaseMongoModel):
    firstname: str
    lastname: str
    patient_id: str

class DoctorDetails(BaseMongoModel):
    firstname: str
    lastname: str
    doctor_id: str

class Jitsi(BaseMongoModel):
    appointment_id: str
    appointment_slot: datetime
    case_id: str | None
    doctor_joining_link: str
    doctor_meeting_code: str
    doctorid: str
    meeting_code: str
    meeting_id: str
    meeting_link: str
    patient_consent: bool | None
    patient_joining_link: str
    patientid: str
    patient_details: Optional[PatientDetails]
    doctor_details: Optional[DoctorDetails]
    zoom_metadata: Optional[dict]
    patient_joined: Optional[datetime] = None
    doctor_joined: Optional[datetime] = None

