import uuid
from ..DTOs.userDTO import Admin<PERSON>serListingDTO
import pandas as pd

from ..DAOs.caseSheetDAO import CaseSheetDAO
from ..DTOs.userCollectionDTO import RelativesDTO
from ..services.auth import get_admin_from_token
from fastapi import Depends, HTTPException, APIRouter, Request, UploadFile, File
from fastapi.responses import StreamingResponse
from io import StringIO
from .payloads.reset_password import ResetPasswordPayload
from .payloads.relatives import CreateRelativePayload, UpdateRelativePayload, UpdateRelative
from ..DTOs.reset_passwordDTO import ResetPasswordResponseDTO
from ..DTOs.baseDTO import NotFoundError
from ..DTOs.patientsDTO import AdminPatientListingDTO
from ..DTOs.paymentDTO import GetPaymentDTO
from ..text_local_service.text_local_controller import TextLocalController
from ..view_controller import UserController
from ..services.aws_email_and_message_sender import <PERSON><PERSON><PERSON><PERSON>AndMsgSender, NotSentException
from ..services.aws_email_model import ResetPasswordEmailMessage
from ..database import init_db
from ..mongodb import mongodb_conn
from .utils import get_host_from_url_object
from ..DAOs.relativeDAO import RelativeDAO, RelativeListQueryFields, RelativeSort
from ..DAOs.lockerDAO import LockerDAO, LockerFilterQueryFields, LockerSort, LockerGroup, locker_set_aggregation
from datetime import datetime
from ..services.paginators import LimitOffsetPaginator, HighLimitOffsetPaginator
from ..validators import AdminRelativeNotUpdateFieldsValidator
from ..DAOs.paymentDAO import PaymentDAO, PaymentListQueryFields, PaymentSort
from ..DAOs.userDAO import UserDAO, UserCollectionDAO, BaseSQLQueryFields, UserListQueryFields, UserSort
from ..DAOs.allPatientsDAO import AllPatientsDAO, AdminPatientListingQueryFields, AdminPatientListingSort
from typing import List
from ..file_upload_service.file_upload_models import *
from ..file_upload_service.file_upload_controller import fileUploadContoller
from ..file_upload_service.file_tags_controller import FileTagContoller
from ..file_upload_service.file_tags_models import *
from ..DTOs.lockerDTO import AdminDocumentListingDTO, DoctorView
from ..DAOs.doctorDAO import DoctorProfileDAO
from .blocks_views import blocks_router
from .slots_views import slots_router
from .appointment_views import appointments_router_dash
from ..DataModels.locker import Locker
from ..patient_controller import PatientController
from ..viewmodels import AddFamilyMember, EmergencyContactDetails
from ..mongodb import mongodb_conn
from ..database import get_db
from .payment_views import payments_router
from ..api_configs import WEB_URL_PATH
from .prompt_views import prompt_router
from .clinic_views import clinic_router
from .case_sheet_views import case_sheet_router
from .services.services_views import services_router
from .services.tags_views import tags_router
from .patient_views import patients_router

admin_router = APIRouter(prefix = "/admin", dependencies=[Depends(get_admin_from_token)], tags = ['admin', 'admin_v2'])
admin_router.include_router(slots_router)
admin_router.include_router(blocks_router)
admin_router.include_router(appointments_router_dash)
admin_router.include_router(payments_router)
admin_router.include_router(prompt_router)
admin_router.include_router(clinic_router)
admin_router.include_router(case_sheet_router)
admin_router.include_router(services_router)
admin_router.include_router(tags_router)
admin_router.include_router(patients_router)

@admin_router.post("/reset_password", response_model=ResetPasswordResponseDTO)
async def reset_password(reset_password_payload: ResetPasswordPayload):
    #get host
    #get email from user_id
    user_id = reset_password_payload.user_id
    user_data = UserController(init_db(), mongodb_conn).get_user_by_id(user_id)
    if user_data is None:
        raise HTTPException(404, NotFoundError(error = "User not Found!!!",details = f"The user_id {user_id} was not found").json())
    user_email = user_data.email
    #create link
    reset_password_url = WEB_URL_PATH + f"forgotPassword?email={user_email}"
    #send email notification
    email = ResetPasswordEmailMessage.create_from_link(user_data.firstname, reset_password_url, user_email)
    if reset_password_payload.send_email:
        try:
            email_message_id = AWSEmailAndMsgSender().send_email(**email.convert_to_aws_format())
        except NotSentException as e:
            raise HTTPException(500, e)
    #send sms notification
    if reset_password_payload.send_sms:
        try:
            sms_message_id = AWSEmailAndMsgSender().send_message(user_data.mobile, f" Your password reset link is {reset_password_url}")
        except NotSentException as e:
            raise HTTPException(500, e)
    #send link in Response
    return ResetPasswordResponseDTO(url = reset_password_url)


@admin_router.put("/relatives/{relation_id}")
async def update_relative(relation_id, payload: UpdateRelative):
    if payload.is_emergency_contact:
        PatientController(get_db(), mongodb_conn).add_emergency_contact(user_id=str(payload.caretaker_id),
                                                                        emergency_contact_details=EmergencyContactDetails(
                                                                            emergency_contact_id=str(uuid.uuid4()),
                                                                            user_id=str(payload.relativeid),
                                                                            full_name=payload.firstname + ' ' + payload.lastname,
                                                                            mobile=payload.mobile,
                                                                            email=payload.email,
                                                                            relation=payload.relationship))
    else:
        PatientController(get_db(), mongodb_conn).remove_emergency_contact(user_id=str(payload.caretaker_id), id_to_remove=str(payload.relativeid))
    return RelativeDAO().update(relation_id, UpdateRelativePayload(**payload.dict()).dict(), validators = [AdminRelativeNotUpdateFieldsValidator])

@admin_router.post("/relatives")
async def create_relative(payload:CreateRelativePayload):
    family_member_payload = AddFamilyMember(**payload.dict(), dob = payload.birthdate, relation = payload.relationship)
    return PatientController(get_db(), mongodb_conn).add_family_member(family_member_data = family_member_payload, care_taker_id = payload.caretaker_id)
    


@admin_router.get("/relatives/{relation_id}")
async def get_relative(relation_id):
    return RelativeDAO().get(_id = relation_id)

@admin_router.delete("/relatives/{relation_id}")
async def delete_relative(relation_id):
    relation = RelativeDAO().get(relation_id)
    if relation is None:
        return HTTPException(404, f"Relation with id {relation_id} not found!!!")
    first_relation_delete_response = RelativeDAO().delete(relation_id)
    complementary_relation = RelativeDAO().list(skip = 0, limit = 1, filters = RelativeListQueryFields(relativeid = relation.caretaker_id, caretaker_id=relation.relativeid))
    if not complementary_relation:
        return {"relation": first_relation_delete_response, "complementary_relation": "Complementary relation not found!!!!"} 
    
    return {"relation": first_relation_delete_response, "complementary_relation": RelativeDAO().delete(complementary_relation[0].relation_id)}

@admin_router.get("/relatives")
async def get_relatives(paginator: LimitOffsetPaginator = Depends(), query_fields: RelativeListQueryFields = Depends(), sort: RelativeSort = Depends()):
    relative_list = RelativeDAO().list(paginator.offset, paginator.limit, query_fields, sort)
    return RelativesDTO.get_relatives_with_emergency_contacts(caretaker_id=query_fields.caretaker_id, relative_list=relative_list)

@admin_router.post("/uploadfile/{userid}", tags = ["AdminFileUpload"])
async def file_upload(
        userid,
        body: FileUploadRequest = Depends(),
        files: List[UploadFile] = File(None)
):
    uploadedBy = userid
    if body.patient_id and len(body.patient_id) > 5:
        resp = RelativeDAO().check_if_relatives(user_1 = userid, user_2 = body.patient_id)
        if not resp:
            raise HTTPException(status_code=409, detail="Selected Relative not found")
        else:
            userid = body.patient_id
    recordId = str(body.recordId) if body.recordId else None
    if recordId is not None:
        msg, error = fileUploadContoller().file_upload_with_record_id(userid, uploadedBy, body)
    elif files is not None:
        for file in files:
            file_extension = file.filename.split(".")[-1].lower()
            if file_extension not in ALLOWED_FILE_TYPES:
                raise HTTPException(
                    status_code= 422,
                    detail="Unsupported file type. Allowed types: {}".format(ALLOWED_FILE_TYPES)
                )
        msg, error = fileUploadContoller().file_upload(userid, uploadedBy, body, files)
    else:
        raise HTTPException(status_code=409, detail="please provide files or requestId/recordId")
    if msg is not None:
        return {"message": msg}
    else:
        raise HTTPException(status_code=409, detail=error)

@admin_router.post('/uploadRequest/{userid}', tags=["fileUpload","AdminFileUpload"])
async def request_file_upload(userid: str, body: List[CreateUploadRequest]):
    # print("post uploadRequest Initiated")
    requestIds, msg = fileUploadContoller().file_upload_request(userid, body)
    if not requestIds:
        raise HTTPException(status_code=409, detail=msg)
    else:
        return {"requestIds": requestIds, "message": msg}
    
@admin_router.delete('/deleteFileRequest/{doctorId}', tags=["fileUpload", "AdminFileUpload"])
async def delete_upload_request(
       request_body: deleteUploadRequest,
       doctorId
):
    
    error = fileUploadContoller().delete_upload_request(request_body.recordId)
    if not error:
        return {"msg": "Upload Request deleted"}
    else:
        raise HTTPException(status_code=409, detail=error)

@admin_router.get('/uploadRequest/{userid}', tags=["fileUpload","AdminFileUpload"])
async def get_file_upload_request(
        userid,
        query: GetUploadRequest = Depends()
):
    if query.patient_id and len(query.patient_id) > 5:
        resp = RelativeDAO().check_if_relatives(user_1 = userid, user_2 = query.patient_id)
        if not resp:
            raise HTTPException(status_code=409, detail="Selected Relative not found")
        else:
            userid = query.patient_id
    requestUploads, msg = fileUploadContoller().get_upload_request(userid, query)
    if msg is None:
        return requestUploads
    else:
        raise HTTPException(status_code=409, detail="request Failed")
    
@admin_router.get('/uploadfile/{userid}', tags=["fileUpload","AdminFileUpload"])
async def get_user_file_upload(
        userid,
        query: GetUserFiles = Depends()
):
    if query.patient_id and len(query.patient_id) > 5:
        resp = RelativeDAO().check_if_relatives(user_1 = userid, user_2 = query.patient_id)
        if not resp:
            raise HTTPException(status_code=409, detail="Selected Relative not found")
        else:
            userid = query.patient_id
    response, msg = fileUploadContoller().get_upload_file(userid, query)
    if msg is None:
        return response
    else:
        raise HTTPException(status_code=409, detail=msg)

@admin_router.post('/uploadTag', tags=["fileUploadTags","AdminFileUpload"])
async def create_file_upload_tag(body: CreateFileTag):
    response, error = FileTagContoller().file_tag_create(body)
    if not error:
        return {"msg": response}
    else:
        raise HTTPException(status_code=409, detail=error)
    
@admin_router.get('/uploadTag', tags=["fileUploadTags"])
async def get_file_upload_tag(query: GetFileTag = Depends()):
    response, error = FileTagContoller().file_tags_get(query)
    if not error:
        return response
    else:
        raise HTTPException(status_code=409, detail=error)
    
@admin_router.patch('/uploadTag/{tagId}', tags=["fileUploadTags","AdminFileUpload"])
async def update_file_upload_tag(tagId, query: UpdateFileTag):
    response, error = FileTagContoller().file_tags_update(tagId, query)
    if not error:
        return {"msg", response}
    else:
        raise HTTPException(status_code=409, detail=error)
    
@admin_router.get('/uploadfile/{userId}/{caseId}', tags=["fileUpload","AdminFileUpload"])
async def get_all_files_for_case(userId, caseId):
    response, error = fileUploadContoller().get_all_files_for_case(userId, caseId)
    if response is not None:
        return response
    else:
        raise HTTPException(status_code=409, detail=error)

@admin_router.get('/uploadfile/{userId}/{caseId}/list', tags=["fileUpload","AdminFileUpload"])
async def get_all_files_for_case_list(userId, caseId):
    response, error = fileUploadContoller().get_all_files_for_case_list(userId, caseId)
    if response is not None:
        return response
    else:
        raise HTTPException(status_code=409, detail=error)

@admin_router.post('/shareFile/{userid}', tags=["fileUpload","AdminFileUpload"])
async def share_file_from_locker(
        userid,
        request: ShareFileReq = Depends()
        ):
    if request.patient_id and len(request.patient_id) > 5:
        resp = RelativeDAO().check_if_relatives(user_1 = userid, user_2 = request.patient_id)
        if not resp:
            raise HTTPException(status_code=409, detail="Selected Relative not found")
        else:
            userid = request.patient_id
        resp, error = fileUploadContoller().share_file_from_locker(userid, request)
        if not error:
            return resp
        else:
            raise HTTPException(status_code=409, detail=error)
        
@admin_router.delete('/deleteRequest/{userid}', tags=["fileUpload","AdminFileUpload"])
async def delete_file_request_doctor(userid, request_body: deleteUserFile):
    error = fileUploadContoller().delete_file_record(request_body.recordId)
    if not error:
        return {"msg": "File Deleted"}
    else:
        raise HTTPException(status_code=409, detail=error)

@admin_router.get("/documents/{userid}",  tags=["fileUpload","AdminFileUpload"])
async def get_documents(userid, locker_query: LockerFilterQueryFields = Depends(), locker_sort: LockerSort = Depends(), paginator: HighLimitOffsetPaginator = Depends(), locker_group: LockerGroup = Depends()):
    locker_query.set_userid(userid)
    all_docs = LockerDAO().list(skip = paginator.offset, limit = paginator.limit, filter = locker_query, sort = locker_sort)
    doctors = list(set([x.doctorId for x in all_docs]))
    all_doctors = DoctorProfileDAO().retrieve_from_list(doctors, field_name = "doctorid")
    doctor_dict = {x.doctorid:DoctorView(**x.__dict__) for x in all_doctors}
    if locker_group.group_by is None:
        return [AdminDocumentListingDTO(**x.dict(exclude_none = True), doctor_details = doctor_dict.get(x.doctorId, None)) for x in all_docs]
    response_body = {}
    for doc in all_docs:
        if getattr(doc, locker_group.group_by.value, None) not in response_body:
            response_body[str(getattr(doc, locker_group.group_by.value, None))] = []
        response_body[str(getattr(doc, locker_group.group_by.value, None))].append(AdminDocumentListingDTO(**doc.dict(exclude_none = True), doctor_details = doctor_dict.get(doc.doctorId, None)))
    return response_body
        

@admin_router.get("/documents/{user_id}/request_count", tags = ["fileUpload", "AdminFileUpload"])
async def get_request_count(user_id):
    query = LockerFilterQueryFields(isRequested=True, userid = user_id)
    return {"count": LockerDAO().get_count(query)}

@admin_router.get("/documents/{user_id}/set/{tag_id}", tags = ["fileUpload", "AdminFileUpload"])
async def get_document_set(user_id, tag_id):
    pipeline = locker_set_aggregation
    pipeline[0]["$match"] =  {
            'userid': user_id,
            'fileTag': tag_id
        }
    return [Locker(**x) for x in LockerDAO().execute_pipeline(locker_set_aggregation)]

@admin_router.get("/patients/download_patient_data", tags = ["export", "patients"])
async def get_all_patient_data():
    all_patients = AllPatientsDAO().list(0, 0)
    data = [x.dict() for x in AdminPatientListingDTO.create_from_patient_list(all_patients)]
    buffer = StringIO()
    pd.DataFrame(data).to_csv(buffer)
    buffer.seek(0)
    return StreamingResponse(iter(buffer.readlines()), media_type= 'text/csv', headers = {"Content-Disposition":
              "attachment;filename=appointments.csv"})

@admin_router.get("/patients/list", tags = ["patients"])
async def admin_patient_list(query_filter: AdminPatientListingQueryFields = Depends(), sort: AdminPatientListingSort = Depends(), paginator: LimitOffsetPaginator = Depends()):
    try:
        print("Getting count")
        patient_count = AllPatientsDAO().get_count(query_filter)
    except Exception as e:
        print("Failed in getting count")
        print(str(e))
        patient_count = 0
    try:
        all_patients = AllPatientsDAO().list(paginator.offset, paginator.limit, query_filter, sort)
    except Exception as e:
        print(str(e))
        all_patients = []
    data = [x.dict() for x in AdminPatientListingDTO.create_from_patient_list(all_patients)]
    return {
            "count": patient_count,
        "data": data
    }

@admin_router.get("/users/list", tags = ["users"])
async def admin_users_list(query_filter: UserListQueryFields = Depends(), sort: UserSort = Depends(), paginator: LimitOffsetPaginator = Depends()):
    user_list = UserDAO().list(paginator.offset, paginator.limit, query_filter, sort)
    return [AdminUserListingDTO(**x.__dict__) for x in user_list]


@admin_router.get("/clinical_history", tags=["case sheet"])
async def get_clinical_history_from_admin(patient_id: str):
    return CaseSheetDAO().get_clinical_history_of_patient(patient_id=str(patient_id))


@admin_router.get("/due_follow_up", tags=["case sheet"])
async def due_follow_up():
    return CaseSheetDAO().fetch_follow_up_appointments()
