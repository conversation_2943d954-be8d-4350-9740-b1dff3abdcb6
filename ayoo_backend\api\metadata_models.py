from sqlalchemy.sql.sqltypes import DateTime
from enum import Enum
from typing import Optional, List
from datetime import datetime

from pydantic import BaseModel


class Specialization(BaseModel):
    specialization_name: str
    specialist: Optional[str] = None
    department: Optional[str] = None
    specialization_field: str
    specialization_acronym: str
    prescription_type: str


class SpecializationUpdate(BaseModel):
    specialization_id: str
    specialization_name: str
    specialist: Optional[str] = None
    department: Optional[str] = None
    specialization_field: str
    specialization_acronym: str
    prescription_type: str


class SpecializationDelete(BaseModel):
    specialization_id: str


class SpecializationGet(BaseModel):
    specialization_field: str


class PracticeArea(BaseModel):
    practice_area: str
    practice_area_field: str


class PracticeAreaUpdate(BaseModel):
    practice_area_id: str
    practice_area: str
    practice_area_field: str


class PracticeAreaDelete(BaseModel):
    practice_area_id: str


class PracticeAreaGet(BaseModel):
    practice_area_field: str


class FormAdd(BaseModel):
    form_name: str


class FormUpdate(BaseModel):
    form_id: str
    form_name: str


class FormDelete(BaseModel):
    form_id: str


class QuestionType(str, Enum):
    MCQ = 'MCQ'
    TEXT = 'TEXT'
    MULTISELECT = 'MULTISELECT'


class QuestionAdd(BaseModel):
    form_id: str
    question_type: QuestionType
    question_text: str
    option1: Optional[str]
    option1_point: Optional[int]
    option2: Optional[str]
    option2_point: Optional[int]
    option3: Optional[str]
    option3_point: Optional[int]
    option4: Optional[str]
    option4_point: Optional[int]
    option5: Optional[str]
    option5_point: Optional[int]
    option6: Optional[str]
    option6_point: Optional[int]
    option7: Optional[str]
    option7_point: Optional[int]
    option8: Optional[str]
    option8_point: Optional[int]
    option9: Optional[str]
    option9_point: Optional[int]
    option10: Optional[str]
    option10_point: Optional[int]

    class Config:
        use_enum_values: True


class QuestionUpdate(BaseModel):
    question_id: str
    form_id: str
    question_type: QuestionType
    question_text: str
    option1: Optional[str]
    option1_point: Optional[int]
    option2: Optional[str]
    option2_point: Optional[int]
    option3: Optional[str]
    option3_point: Optional[int]
    option4: Optional[str]
    option4_point: Optional[int]
    option5: Optional[str]
    option5_point: Optional[int]
    option6: Optional[str]
    option6_point: Optional[int]
    option7: Optional[str]
    option7_point: Optional[int]
    option8: Optional[str]
    option8_point: Optional[int]
    option9: Optional[str]
    option9_point: Optional[int]
    option10: Optional[str]
    option10_point: Optional[int]

    class Config:
        use_enum_values: True


class GetFormQuestions(BaseModel):
    form_id: str


class QuestionDelete(BaseModel):
    question_id: str


class QuestionResponse(BaseModel):
    question_id: str
    form_id: str
    question_type: QuestionType
    question_text: str
    option1: Optional[str]
    option1_point: Optional[int]
    option2: Optional[str]
    option2_point: Optional[int]
    option3: Optional[str]
    option3_point: Optional[int]
    option4: Optional[str]
    option4_point: Optional[int]
    option5: Optional[str]
    option5_point: Optional[int]
    option6: Optional[str]
    option6_point: Optional[int]
    option7: Optional[str]
    option7_point: Optional[int]
    option8: Optional[str]
    option8_point: Optional[int]
    option9: Optional[str]
    option9_point: Optional[int]
    option10: Optional[str]
    option10_point: Optional[int]

    class Config:
        use_enum_values: True


class QuestionResponseRangeAdd(BaseModel):
    form_id: str
    range1_lower_limit: int
    range1_upper_limit: int
    range1_response_text: str
    range2_lower_limit: int
    range2_upper_limit: int
    range2_response_text: str
    range3_lower_limit: Optional[int]
    range3_upper_limit: Optional[int]
    range3_response_text: Optional[str]
    range4_lower_limit: Optional[int]
    range4_upper_limit: Optional[int]
    range4_response_text: Optional[str]
    range5_lower_limit: Optional[int]
    range5_upper_limit: Optional[int]
    range5_response_text: Optional[str]
    range6_lower_limit: Optional[int]
    range6_upper_limit: Optional[int]
    range6_response_text: Optional[str]


class QuestionResponseRangeUpdate(BaseModel):
    response_id: str
    form_id: str
    range1_lower_limit: int
    range1_upper_limit: int
    range1_response_text: str
    range2_lower_limit: int
    range2_upper_limit: int
    range2_response_text: str
    range3_lower_limit: Optional[int]
    range3_upper_limit: Optional[int]
    range3_response_text: Optional[str]
    range4_lower_limit: Optional[int]
    range4_upper_limit: Optional[int]
    range4_response_text: Optional[str]
    range5_lower_limit: Optional[int]
    range5_upper_limit: Optional[int]
    range5_response_text: Optional[str]
    range6_lower_limit: Optional[int]
    range6_upper_limit: Optional[int]
    range6_response_text: Optional[str]


class QuestionResponseDelete(BaseModel):
    response_id: str


class MetadataListName(BaseModel):
    list_name: str


class MetadataListNameUpdate(BaseModel):
    list_id: str
    list_name: str


# class OptionAndValues(BaseModel):
#     option: str
#     # value: str


class DropdownMetadata(BaseModel):
    list_id_or_name: str
    options: List[str]


class DropdownMetadataAdd(BaseModel):
    list_id: str
    options: List[str]


class DropdownMetadataUpdate(BaseModel):
    list_id: str
    option_name: str
    new_option: str


class DropdownMetadataDelete(BaseModel):
    list_id: str
    option_name: str


class DropdownMetadataSearch(BaseModel):
    list_id_or_name: str


#
# class DrugForm(str, Enum):
#     Tablet = 'Tablet'
#     Liquid = 'Liquid'
#     Capsules = 'Capsules'
#     Topical = 'Topical'
#     Drops = 'Drops'
#     Inhalers = 'Inhalers'
#     Injections = 'Injections'
#     Implants_or_Patches = 'Implants or Patches'
#     Suppositories = 'Suppositories'
#     Spansules = 'Spansules'
#     Softgels = 'Softgels'
#     Suspension = 'Suspension'
#     Syrup = 'Syrup'
#
#
# class DrugRoute(str, Enum):
#     Oral = 'Oral'
#     Sublingual_and_Buccal = 'Sublingual and Buccal'
#     Rectal = 'Rectal'
#     Intravenous = 'Intravenous'
#     Intramuscular = 'Intramuscular'
#     Subcutaneous = 'Subcutaneous'
#     Intranasal = 'Intranasal'
#     Inhalational = 'Inhalational'
#     Transdermal = 'Transdermal'
#     Vaginal = 'Vaginal'
#     Deep_Intramuscular = 'Deep Intramuscular'
#
#
# class DrugDosage(BaseModel):
#     drug_form: DrugForm
#     route: DrugRoute
#     dose: str
#
#
# class BrandAndDosage(BaseModel):
#     medicine_brand: str
#     dosage: List[DrugDosage]
#
#
# class MedicineCatalogue(BaseModel):
#     medicine_salt: str
#     medicine_brand_and_dosage: List[BrandAndDosage]


class AddMedicineToCatalogue(BaseModel):
    medicine_salt: str
    medicine_brand: str
    drug_form: str
    route: str
    dose: Optional[str]


class UpdateMedicineInCatalogue(BaseModel):
    medicine_id: str
    medicine_salt: str
    medicine_brand: str
    drug_form: str
    route: str
    dose: str


class DeleteMedicineFromCatalogue(BaseModel):
    medicine_id: str


class GetMedicineBrandFromSalt(BaseModel):
    medicine_salt: str


class GetMedicineSaltFromBrand(BaseModel):
    medicine_brand: str


class GetMedicineDoseAndDrugFormFromBrandAndSalt(BaseModel):
    medicine_salt: str
    medicine_brand: str


class AddICDCodeToCatalogue(BaseModel):
    icd_code: Optional[str] = ''
    icd_block_id: Optional[str] = ''
    icd_block_title: Optional[str] = ''
    icd_title: str
    icd_chapter: str
    icd_class_kind: str


class SearchICDCodeByBlock(BaseModel):
    search_string: str
