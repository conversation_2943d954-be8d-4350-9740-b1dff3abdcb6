from datetime import datetime, timedelta
from .DTOs.jitsiDTO import MeetingStartJoinBothResponse
from .ayoo_utils import calcualte_appointment_duration
from .DAOs.jitsiDAO import JitsiDAO, JitsiQueryFields
from .DAOs.appointmentDAO import Appointment<PERSON><PERSON>, GetAppointmentById
from fastapi import APIRouter, Depends, HTTPException
from .services.auth import get_user_from_token 
from .jwt import create_video_service_token, create_video_sdk_token

meet_router = APIRouter(prefix = "/meet", tags = ["meet"], dependencies = [])


@meet_router.get("/video_sdk_token/{appointment_id}")
async def get_video_sdk_token(appointment_id: str):
    appointments = AppointmentDAO().list(filter = GetAppointmentById(appointment_id = appointment_id))
    if len(appointments) == 0:
        raise HTTPException(404, "Appointment not found !!!!")
    appointment = appointments[0]
    start = appointment.appointment_slot
    end = appointment.end_date
    slot_duration = (end - start).total_seconds()/60
    start = start - timedelta(minutes = 5)
    end = end + timedelta(minutes = calcualte_appointment_duration(slot_duration))
    return {"patient_user_token":create_video_sdk_token(appointment_id, start, end, 0),
     "doctor_user_token":create_video_sdk_token(appointment_id, start, end, 1),
            "service_account_token": create_video_service_token()
            }

@meet_router.get("/meet_details/{appointment_id}")
async def get_meet_details(appointment_id: str):
    appointments = JitsiDAO().list(0, 1, filter = JitsiQueryFields(appointment_id = appointment_id))
    if len(appointments):
        return appointments[0]
    raise HTTPException(404, "No meeting details found for appointment id {}".format(appointment_id))

@meet_router.get("/set_joining_time/{appointment_id}")
async def set_joining_time(appointment_id: str, role: int):
    appointments = JitsiDAO().list(0, 1, filter = JitsiQueryFields(appointment_id = appointment_id))
    if len(appointments) == 0:
        raise HTTPException(404, "No meeting details found for appointment_id {}".format(appointment_id))
    meeting = appointments[0]
    print(role)
    if role == 0:
        if meeting.patient_joined is not None:
            raise HTTPException(400, "Patient has already joined on {}".format(meeting.patient_joined))
        meeting.patient_joined = datetime.now()
    elif role == 1:
        if meeting.doctor_joined is not None:
            raise HTTPException(400, "Doctor has already joined on {}".format(meeting.doctor_joined))
        meeting.doctor_joined = datetime.now()
    else:
        raise HTTPException(400, "Wrong role entered !!!!!")
    _id = meeting.id
    print(_id)
    del meeting.id
    update = JitsiDAO().update(_id = _id, _obj = meeting)
    return update

@meet_router.get("/get_start_time/{appointment_id}")
async def get_start_time(appointment_id: str):
    appointments = JitsiDAO().list(0, 1, filter = JitsiQueryFields(appointment_id = appointment_id))
    if len(appointments) == 0:
        raise HTTPException(404, "No meeting details found for appointment_id {}".format(appointment_id))
    meeting = appointments[0]
    return meeting.doctor_joined

@meet_router.get("/get_start_time/both/{appointment_id}", response_model=MeetingStartJoinBothResponse)
async def get_start_time(appointment_id: str):
    appointments = JitsiDAO().list(
        0, 1, filter=JitsiQueryFields(appointment_id=appointment_id))
    if len(appointments) == 0:
        detail = f"No meeting details found for appointment_id {appointment_id}"
        raise HTTPException(404, detail)
    meeting = appointments[0]
    return MeetingStartJoinBothResponse(
        doctor= meeting.doctor_joined if meeting.doctor_joined else "",
        patient= meeting.patient_joined if meeting.patient_joined else ""
    )
