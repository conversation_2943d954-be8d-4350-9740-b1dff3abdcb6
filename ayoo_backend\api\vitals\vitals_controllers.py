from typing import List, Optional
from fastapi import HTT<PERSON>Exception
from .vitals_models import CreateVitalRequest, VitalsResponse, Vital, AddVitalsRequest, AddVitalsDTO, VitalTypeEnum, \
    PutVitalsRequest, UpdateVitalsDTO, vital_units
from bson import ObjectId
from datetime import datetime

from ..database import init_db
from ..mongodb import mongodb_conn
import uuid


class VitalsController:
    def __init__(self):
        self.db = init_db()
        self.mongo = mongodb_conn
        self.mongo_db = self.mongo['ayoo']
        self.vitals_collection = self.mongo_db["user-vitals"]

    def _generate_vital_dto(self, vital_request: CreateVitalRequest, added_by: str, added_by_type: str,
                            patient_id: str) -> AddVitalsDTO:

        vital_value = vital_request.value if str(vital_request.type.value) != 'bmi' else round(vital_request.value, 2)
        return AddVitalsDTO(
            vital_id=str(uuid.uuid4()),
            case_id=vital_request.case_id,
            type=str(vital_request.type.value),
            value=vital_value,
            unit=vital_request.unit if vital_request.unit not in [None, ''] else vital_units[
                str(vital_request.type.value)],
            date=vital_request.date,
            user_id=patient_id,
            # status=vital_request.status,
            created_at=datetime.now(),
            updated_at=datetime.now(),
            added_by=added_by,
            added_by_type=added_by_type,
        )

    def add_vital(self, vital_request: CreateVitalRequest, added_by: str, added_by_type: str, patient_id: str):
        vital_dto = self._generate_vital_dto(vital_request=vital_request, added_by=added_by,
                                             added_by_type=added_by_type, patient_id=patient_id)
        result = self.vitals_collection.insert_one(vital_dto.dict())
        if result.inserted_id:
            return vital_dto
        else:
            # raise HTTPException(status_code=500, detail="Failed to add vital")
            raise Exception("Failed to add vital")

    async def get_vitals(self, user_id: str, patient_id:Optional[str]=None) -> VitalsResponse:
        try:
            from ayoo_backend.api import dbmodels
            from ayoo_backend.api.dbmodels import DBRelatives
            search_user_id = user_id
            if patient_id is not None:
                relatives = self.db.query(dbmodels.DBRelatives).filter(DBRelatives.caretaker_id == user_id).filter(
                        DBRelatives.relativeid == patient_id).one_or_none()
                if relatives is None:
                    raise Exception(f'Relative ID: {patient_id} is not mapped with {user_id}')
                if not relatives.consent:
                    raise Exception(f'Consent not allowed')

                search_user_id = patient_id

            vitals = list(self.vitals_collection.find({"user_id": search_user_id}).sort([("created_at", -1)]))
            if not vitals:
                raise Exception("User not found")
            return VitalsResponse(user_id=user_id, vitals=[Vital(**v) for v in vitals])
        except Exception as e:
            raise HTTPException(status_code=409, detail=str(e))

    async def update_vital(self, vital_id: str, updated_data: PutVitalsRequest) -> Vital:
        # Create the update object
        update_data = {
            "case_id": updated_data.case_id,
            "value": updated_data.value,
            "unit": updated_data.unit,
            "updated_at": datetime.now()
        }

        # Perform the update operation
        result = self.vitals_collection.update_one(
            {"vital_id": vital_id},
            {"$set": update_data}
        )

        # Check if the update was successful
        if result.modified_count == 0:
            raise HTTPException(status_code=409, detail="Vital not found or no changes made")

        # Retrieve the updated document
        updated_vital = self.vitals_collection.find_one({"vital_id": vital_id})
        if updated_vital is None:
            raise HTTPException(status_code=409, detail="Vital not found after update")

        return Vital(**updated_vital)

    async def delete_vital(self, vital_id: str) -> str:
        result = self.vitals_collection.delete_one({"vital_id": vital_id})
        if result.deleted_count == 0:
            raise HTTPException(status_code=409, detail="Vital not found")
        return "Vital deleted successfully"

    async def get_latest_vitals(self, user_id: str, vital_key: Optional[str] = None, patient_id: Optional[str]=None):
        try:
            if patient_id not in ['', None]:
                query = {"user_id": patient_id}
            else:
                query = {"user_id": user_id}

            if vital_key == 'blood_glucose':
                query["type"] = {"$regex": f"^{vital_key}"}
            else:
                query["type"] = {"$in": [vital_key]}

            pipeline = [
                {"$match": query},
                {"$sort": {"created_at": -1, "date": -1}},
                {
                    "$group": {
                        "_id": {
                            "user_id": "$user_id",
                            "vital_key": "$type",
                            "date": "$date",
                            "created_at": {"$dateToString": {"format": "%Y-%m-%d", "date": "$created_at"}}
                        },
                        "latest_vital": {"$first": "$$ROOT"}
                    }
                },
                {
                    "$replaceRoot": {"newRoot": "$latest_vital"}
                },
                {"$sort": {"date": -1, "created_at": -1}}
            ]

            vitals = list(self.vitals_collection.aggregate(pipeline))

            for index, data in enumerate(vitals):
                if data.get('type') == 'blood_pressure':
                    bp_value = data.get('value').split('/')
                    vitals[index]['bp_systolic'] = int(bp_value[0])
                    vitals[index]['bp_diastolic'] = int(bp_value[1])

            # if not vitals:
            #     raise Exception("No vitals found")

            return VitalsResponse(user_id=user_id, vital_type=vital_key, vitals=[Vital(**v) for v in vitals])
        except Exception as e:
            raise Exception(f'latest vital fetch error: {str(e)}')

    async def get_vitals_by_date_range(
            self,
            user_id: str,
            vital_type: str,
            start_date: datetime,
            end_date: datetime
    ) -> List[Vital]:
        # Debugging information
        print(
            f"Fetching vitals for user_id: {user_id}, type: {vital_type}, start_date: {start_date}, end_date: {end_date}")

        pipeline = [
            {"$match": {
                "user_id": user_id,
                "type": str(vital_type),
                "date": {"$gte": start_date, "$lte": end_date}
            }},
            {"$sort": {"date": -1}},  # Sort by date in descending order
            {"$group": {
                "_id": {
                    "date": {"$dateToString": {"format": "%Y-%m-%d", "date": "$date"}},  # Group by date (without time)
                    "type": "$type"
                },
                "latest_vital": {"$first": "$$ROOT"}  # Get the latest vital for each date
            }},
            {"$replaceRoot": {"newRoot": "$latest_vital"}}  # Replace root with the latest vital document
        ]

        vitals = list(self.vitals_collection.aggregate(pipeline))

        # Debugging information
        print(f"Vitals found: {vitals}")

        if not vitals:
            raise HTTPException(status_code=409, detail="No vitals found in the specified date range")

        # Convert to Vital objects
        vitals_objects = [Vital(**v) for v in vitals]

        # Sort by date in ascending order
        sorted_vitals = sorted(vitals_objects, key=lambda v: v.date)

        return sorted_vitals
