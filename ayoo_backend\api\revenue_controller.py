from typing import Optional
import uuid
from dateutil.parser import parse
from fastapi import HTT<PERSON>Exception
from sqlalchemy import or_
import textwrap
from .mental_health_controller import MentalHealthController
from .revenue_models import Payment, Paymentview, Paymentsuccess, Status, Payments_appointment, Payment_info, \
    Paymentstatus, \
    appointment_failed, Paymentrefund, promo_code, promotion, Adhocview, Get_payment
from sqlalchemy.orm import scoped_session
from reportlab.lib.styles import getSampleStyleSheet
from reportlab.lib.pagesizes import letter, A4
import random

from . import dbmodels
from .dbmodels import DBUser
from .dbmodels import DBDoctor
import datetime
from .ayoo_utils import check_encrypted_password, get_age_in_years
from .views import logger, loggers
import random
import string
from reportlab.lib.utils import ImageReader
from reportlab.lib import colors

from PyPDF2 import PdfWriter
from .api_configs import AWS_ACCESS_KEY_ID, AWS_SECRET_ACCESS_KEY, AWS_REGION_NAME, AWS_BUCKET_NAME, PDF_FONT_FILE_PATH
from .aws_s3 import AWSS3Client
import boto3
import io
from reportlab.pdfgen import canvas
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
import base64
import os
from .patient_controller import PatientController
from .DAOs.revenueDAO import InvoiceDetails
from .mental_health_controller import BaseHealthController
from Crypto.Cipher import AES
import hashlib


class Revenuecontroller:
    def __init__(self, db: scoped_session, mongo=None):
        self.db = db
        self.mongo = mongo
        self.mongo_db = self.mongo['ayoo']
        # self.otp_generator = OTP_GENERATOR

    def payment_initiate(self, paymentview: Paymentview, order_id: str, secret_key):
        resp_user: DBUser = self.db.query(dbmodels.DBUser).filter_by(
            userid=paymentview.user_id).one_or_none()
        ###logger.info("user_info")
        ###logger.info(resp_user)
        if not resp_user:
            return '', 'user not found'
        if paymentview.appointment_or_membership == "appointment":
            if paymentview.promo_code:
                payment_info = Payment(user_id=paymentview.user_id, order_id=order_id, amount=paymentview.amount,
                                       promo_code=paymentview.promo_code, appointment_id=paymentview.appointment_id,
                                       appointment_or_membership=paymentview.appointment_or_membership,
                                       payment_status="initiate", payment_date=datetime.datetime.now(),
                                       secret_key=secret_key,payment_card='',payment_mode='')
            else:
                payment_info = Payment(user_id=paymentview.user_id, order_id=order_id, amount=paymentview.amount,
                                       appointment_id=paymentview.appointment_id,
                                       appointment_or_membership=paymentview.appointment_or_membership,
                                       payment_status="initiate", payment_date=datetime.datetime.now(),
                                       secret_key=secret_key,payment_card='',payment_mode='')

        if paymentview.appointment_or_membership == "membership":
            payment_info = Payment(user_id=paymentview.user_id, order_id=order_id, amount=paymentview.amount,
                                   appointment_or_membership=paymentview.appointment_or_membership,
                                   payment_status="initiate", payment_date=datetime.datetime.now(),payment_card='',payment_mode='')

        try:
            mongo_collection = self.mongo_db['Paymentgateway3']
            mongo_collection.insert_one(dict(payment_info))
            return order_id, 'data saved successfully'
        except Exception as e:
            err = str(e)
            loggers['logger3'].info("/ccavRequestHandler/ : {status_code=409, detail= " + str(e) + "}")
            raise Exception(f'Internal Error code {err} for adding payment data')

    def adhoc_initiate(self, paymentview: Adhocview):
        try:
            resp_user: DBUser = self.db.query(dbmodels.DBUser).filter_by(
                userid=paymentview.user_id).one_or_none()
            ###logger.info("user_info")
            ###logger.info(resp_user)
            if not resp_user:
                resp_user = self.db.query(dbmodels.DBRelatives).filter_by(relativeid=paymentview.user_id).one_or_none()

            if not resp_user:
                raise HTTPException(status_code=409, detail='User not found!!!!')


            payment_info = dict(user_id=paymentview.user_id, order_id=paymentview.order_id, amount=paymentview.amount,
                                appointment_id='',
                                appointment_or_membership="adhoc", payment_status="Pending",
                                payment_date=datetime.datetime.now(),payment_card='',payment_mode='')


            mongo_collection = self.mongo_db['Paymentgateway3']
            mongo_collection.insert_one(dict(payment_info))
            return paymentview.order_id, 'data saved successfully'
        except Exception as e:
            err = str(e)
            loggers['logger3'].info("/ccavRequestHandler/ : {status_code=409, detail= " + str(e) + "}")
            raise Exception(f'Internal Error code {err} for adding payment data')

    def payment_sucess_promo(self, paymentview: Paymentview, order_id: str, secret_key):
        resp_user: DBUser = self.db.query(dbmodels.DBUser).filter_by(
            userid=paymentview.user_id).one_or_none()
        ###logger.info("user_info")
        ###logger.info(resp_user)
        if resp_user is None:
            return '', 'user not found'
        if paymentview.appointment_or_membership == "appointment":
            if paymentview.promo_code:
                payment_info = Payment(user_id=paymentview.user_id, order_id=order_id, amount=paymentview.amount,
                                       promo_code=paymentview.promo_code, appointment_id=paymentview.appointment_id,
                                       appointment_or_membership=paymentview.appointment_or_membership,
                                       payment_status=Status.success, payment_date=datetime.datetime.now(),
                                       secret_key=secret_key,payment_card='',payment_mode='')
        try:
            ptn_ctrl = PatientController(db=self.db, mongo=self.mongo)
            mh_ctrl = MentalHealthController(db=self.db, mongo=self.mongo)
            res_app = self.mongo_db['Appointments'].find_one({"appointment_id": paymentview.appointment_id})
            open_caseid_exist = mh_ctrl.get_open_case_id(patient_id=str(res_app.get("patient_id")),
                                                     doctor_id=str(res_app.get("doctorid")))
            caseid = open_caseid_exist if open_caseid_exist else mh_ctrl.generate_case_id()
            payment_info.case_id = caseid
            mongo_collection = self.mongo_db['Paymentgateway3']
            mongo_collection.insert_one(dict(payment_info))
            if 'status' in res_app:
                is_first_appointment = ptn_ctrl.manage_first_appointment(patient_id=res_app['patient_id'])

                self.mongo_db['Appointments'].find_one_and_update({"appointment_id": paymentview.appointment_id}, {
                    "$set": {'is_active': True, 'is_confirmed': True,
                             'caseid': caseid,
                             'status': dict(status='Booked', Reason='', comment=''),
                             'payment_status': "Successful",
                             'is_first_appointment': is_first_appointment
                             }})
            notif_data_exist = self.mongo_db['Notifications'].find_one({'object_id': paymentview.appointment_id})
            self.mongo_db['JitsiMeetInfo'].update_one({"appointment_id": paymentview.appointment_id}, {"$set":{"case_id":caseid}})
            if notif_data_exist is None:
                ptn_ctrl.manage_notifications_for_booked_appointments(appointment_id=paymentview.appointment_id)
                # self.appointment_payment(appointment_id=resp['appointment_id'])

            return order_id, 'data saved successfully'
        except Exception as e:
            err = str(e)
            raise Exception(f'Internal Error code {err} for adding payment data')

    # this is for web hook
    def payment_sucess(self, paymentview: Paymentsuccess, *args):
        payment_details = self.mongo_db['Paymentgateway3'].find_one(
            {"order_id": paymentview.order_id})

        if payment_details:
            try:
                ptn_ctrl = PatientController(db=self.db, mongo=self.mongo)
                mh_ctrl = MentalHealthController(db=self.db, mongo=self.mongo)
                appointment_details = self.mongo_db['Appointments'].find_one({
                    "appointment_id": payment_details['appointment_id']
                })

                open_caseid_exist = mh_ctrl.get_open_case_id(patient_id=str(appointment_details.get("patient_id")),
                                                     doctor_id=str(appointment_details.get("doctorid")))
                caseid = open_caseid_exist if open_caseid_exist else mh_ctrl.generate_case_id()
                appointment_id = payment_details['appointment_id']
                self.mongo_db['Paymentgateway3'].find_one_and_update(
                    {"order_id": paymentview.order_id},
                    {'$set': {
                        'payment_status': Status.success,
                        'transaction_id': paymentview.transaction_id,
                        'payment_mode': paymentview.payment_mode,
                        'payment_card': paymentview.payment_card,
                        'case_id': caseid,
                        'is_activity_confirmed': True}

                     })
                logger.info('updated PG3')
                logger.info(f'appointment id: {appointment_id}')
                if 'status' in appointment_details:
                    is_first_appointment = ptn_ctrl.manage_first_appointment(
                        patient_id=appointment_details['patient_id'])
                    status =  dict(status='Booked', Reason='', comment='')
                    if appointment_details.get("extension"):
                        status =  dict(status='Completed', Reason='', comment='')

                    
                    self.mongo_db['Appointments'].find_one_and_update(
                        {"appointment_id": payment_details['appointment_id']},
                        {"$set": {
                            'caseid': caseid,
                            'is_active': True,
                            'is_confirmed': True,
                            'status': status,
                            'payment_status': 'Successful',
                            'is_first_appointment': is_first_appointment
                        }})
                else:
                    self.mongo_db['Appointments'].find_one_and_update(
                        {"appointment_id": payment_details['appointment_id']},
                        {"$set": {'is_active': True, 'is_confirmed': True}}
                    )

                logger.info(f'status in appointment collection updated')

                if payment_details['appointment_or_membership'] == "adhoc":
                    return paymentview.order_id

                logger.info(f'notif_data_exist checking')

                notif_data_exist = self.mongo_db['Notifications'].find_one(
                    {'object_id': payment_details['appointment_id']})
                logger.info(f'notif_data_exist : {notif_data_exist}')
                if notif_data_exist is None:
                    ptn_ctrl.manage_notifications_for_booked_appointments(
                        appointment_id=payment_details['appointment_id'])

                self.appointment_payment(paymentview.order_id)
                logger.info("-------payment_sucess function for web hook end-----------")

                return paymentview.order_id

            except Exception as e:
                err = str(e)
                logger.info(f"/get/status/ : {str(e)}")
                raise Exception(f'Internal Error code {err} on Successfull payment of invoice_id : {paymentview.order_id}')

    def payment(self, paymentview: Paymentsuccess):
        # logger.info("Successfull")
        payment_success = paymentview
        faq_update = dict(order_id=payment_success['order_id'], transaction_id=paymentview['transaction_id'],
                          payment_status="success", payment_date=datetime.datetime.now())
        # logger.info(payment_success['order_id'])
        try:
            self.mongo_db['Paymentgateway3'].find_one_and_update(
                {"order_id": paymentview['order_id'], "payment_status": "initiate"},
                {'$set': {'transaction_id': paymentview['transaction_id'],
                          'payment_status': Status.cancelled,
                          'payment_date': datetime.datetime.now()}})

        except Exception as e:
            err = str(e)
            raise Exception(f'Internal Error code {err} on cancelled payment of order id{payment_success.order_id}')
        return payment_success['order_id']

    def payment_failure(self, paymentview: Paymentsuccess):
        # logger.info("Successfull")
        payment_success = paymentview
        faq_update = dict(order_id=payment_success.order_id, transaction_id=paymentview.transaction_id,
                          payment_status="success", payment_date=datetime.datetime.now())
        # logger.info(payment_success['order_id'])
        try:
            resp = self.mongo_db['Paymentgateway3'].find_one({"order_id": paymentview.order_id})
            res_app = self.mongo_db['Appointments'].find_one({"appointment_id": resp['appointment_id']})
            if 'status' in res_app:
                self.mongo_db['Appointments'].find_one_and_update({"appointment_id": resp['appointment_id']}, {
                    "$set": {'status': dict(status='Pending', Reason='payment cancelled', comment=''),
                             'payment_status': Status.cancelled,
                             'is_active': False,
                             'is_confirmed': False
                             }})
            self.mongo_db['Paymentgateway3'].find_one_and_update(
                {"order_id": paymentview.order_id, "payment_status": "initiate"},
                {'$set': {'transaction_id': paymentview.transaction_id,
                          'payment_status': Status.cancelled,
                          'payment_date': datetime.datetime.now(),
                        'payment_mode': paymentview.payment_mode,
                         'payment_card': paymentview.payment_card}})

        except Exception as e:
            err = str(e)
            loggers['logger3'].info("/ccavResponseHandler/ : {status_code=409, detail= " + str(e) + "}")
            raise Exception(f'Internal Error code {err} on cancelled payment of order id{payment_success.order_id}')
        return payment_success.order_id

    def payment_Aborted(self, paymentview: Paymentsuccess):
        # logger.info("Successfull")
        payment_success = paymentview
        faq_update = dict(order_id=payment_success.order_id, transaction_id=paymentview.transaction_id,
                          payment_status="success", payment_date=datetime.datetime.now())
        # logger.info(payment_success['order_id'])
        try:
            resp = self.mongo_db['Paymentgateway3'].find_one({"order_id": paymentview.order_id})
            res_app = self.mongo_db['Appointments'].find_one({"appointment_id": resp['appointment_id']})
            if 'status' in res_app:
                self.mongo_db['Appointments'].find_one_and_update({"appointment_id": resp['appointment_id']}, {
                    "$set": {'status': dict(status='Pending', Reason='payment aborted', comment=''),
                             'payment_status': Status.aborted,
                             'is_active': False,
                             'is_confirmed': False
                             }})
            self.mongo_db['Paymentgateway3'].find_one_and_update(
                {"order_id": paymentview.order_id, "payment_status": "initiate"},
                {'$set': {'transaction_id': paymentview.transaction_id,
                          'payment_status': Status.aborted,
                          'payment_date': datetime.datetime.now(),
                        'payment_mode': paymentview.payment_mode,
                        'payment_card': paymentview.payment_card}})

        except Exception as e:
            err = str(e)
            loggers['logger3'].info("/ccavResponseHandler/ : {status_code=409, detail= " + str(e) + "}")
            raise Exception(f'Internal Error code {err} on cancelled payment of order id{payment_success.order_id}')
        return payment_success.order_id

    def payment_invalid(self, paymentview: Paymentsuccess):
        # logger.info("Successfull")
        payment_success = paymentview
        faq_update = dict(order_id=payment_success.order_id, transaction_id=paymentview.transaction_id,
                          payment_status="success", payment_date=datetime.datetime.now())
        # logger.info(payment_success['order_id'])
        try:
            resp = self.mongo_db['Paymentgateway3'].find_one({"order_id": paymentview.order_id})
            res_app = self.mongo_db['Appointments'].find_one({"appointment_id": resp['appointment_id']})
            if 'status' in res_app:
                self.mongo_db['Appointments'].find_one_and_update({"appointment_id": resp['appointment_id']}, {
                    "$set": {'status': dict(status='Pending', Reason='payment Invalid', comment=''),
                             'payment_status': Status.Invalid,
                             'is_active': False,
                             'is_confirmed': False
                             }})
            self.mongo_db['Paymentgateway3'].find_one_and_update(
                {"order_id": paymentview.order_id, "payment_status": "initiate"},
                {'$set': {'transaction_id': paymentview.transaction_id,
                          'payment_status': Status.Invalid,
                          'payment_date': datetime.datetime.now()}})

        except Exception as e:
            err = str(e)
            raise Exception(f'Internal Error code {err} on cancelled payment of order id{payment_success.order_id}')
        return payment_success.order_id

    def payment_awaited(self, paymentview: Paymentsuccess):
        # logger.info("Successfull")
        payment_success = paymentview
        faq_update = dict(order_id=payment_success.order_id, transaction_id=paymentview.transaction_id,
                          payment_status="success", payment_date=datetime.datetime.now())
        # logger.info(payment_success['order_id'])
        try:
            resp = self.mongo_db['Paymentgateway3'].find_one({"order_id": paymentview.order_id})
            res_app = self.mongo_db['Appointments'].find_one({"appointment_id": resp['appointment_id']})
            if 'status' in res_app:
                self.mongo_db['Appointments'].find_one_and_update({"appointment_id": resp['appointment_id']}, {
                    "$set": {'status': dict(status='Pending', Reason='payment Invalid', comment=''),
                             'payment_status': Status.Awaited,
                             'is_active': False,
                             'is_confirmed': False
                             }})
            self.mongo_db['Paymentgateway3'].find_one_and_update(
                {"order_id": paymentview.order_id, "payment_status": "initiate"},
                {'$set': {'transaction_id': paymentview.transaction_id,
                          'payment_status': Status.Awaited,
                          'payment_date': datetime.datetime.now(),
                                                                         'payment_mode': paymentview.payment_mode,
                                                                         'payment_card': paymentview.payment_card}})

        except Exception as e:
            err = str(e)
            raise Exception(f'Internal Error code {err} on cancelled payment of order id{payment_success.order_id}')
        return payment_success.order_id

    def payment_status(self, paymentview: Paymentstatus):
        try:
            resp = self.mongo_db['Paymentgateway3'].find({"order_id": paymentview.order_id})
            for result in resp:
                if result['payment_status'] in ["cancelled","Failed"] or result['payment_status'] in ["aborted","Aborted"]:
                    return {"status": "cancelled", "data": {k: v for k, v in result.items() if k != "_id"} }
                elif result['payment_status'] == "Successful":
                    return {"status": "success", "data": {k: v for k, v in result.items() if k != "_id"}}
        except Exception as e:
            err = str(e)
            loggers['logger3'].info("/get/payment/status1 : {status_code=409, detail= " + str(e) + "}")

    def get_receipt(self, info: Payment_info):
        s3_instance = AWSS3Client()
        resp = self.mongo_db['Receipt'].find_one({"appointment_id": info.appointment_id})
        return {"receipt_link": s3_instance.get_presigned_url(resp['receipt_link'])} if 'receipt_link' in resp else {"receipt_link": ""}

    def payment_appointment(self, appointment: Payments_appointment):
        try:
            logger.info("/payment/appointment")
            app = self.mongo_db['Appointments'].find_one({"appointment_id": appointment.appointment_id})
            resp = self.mongo_db['Paymentgateway3'].find_one({"order_id": appointment.order_id})
            
            if (resp['payment_status'] == "Successful"):
                self.mongo_db['Paymentgateway3'].find_one_and_update(
                    {"order_id": appointment.order_id},
                    {'$set': {
                        'appointment_id': app['appointment_id'],
                        'case_id': app['caseid'],
                        'is_activity_confirmed': True
                    }})
                return self.appointment_payment(appointment.order_id)
            else:
                return {"message": "payment was not successfull"}
            # calling function over here
        except Exception as e:
            err = str(e)
            loggers['logger3'].info("/payment/appointment : {status_code=409, detail= " + str(e) + "}")
            raise Exception(f'Internal Error code {err} on Successfull payment of order id{appointment.order_id}')
        

    def appointment_failure(self, user: appointment_failed):
        li = []
        if user:
            resp = self.mongo_db['Paymentgateway3'].find({"user_id": user.user_id})
            for res in resp:
                if res['payment_status'] == "success" and res['is_activity_confirmed'] == False:
                    if not res['refund_details']:
                        li.append(res)

        else:
            for res in self.mongo_db['Paymentgateway3'].find():
                if res['payment_status'] == "success" and res['is_activity_confirmed'] == False:
                    if not res['refund_details']:
                        li.append(res)
        return li

    def refund_payment(self, order_id, status):
        self.mongo_db['Paymentgateway3'].find_one_and_update({"order_id": order_id},
                                                             {'$set': {
                                                                 'refund_details.$.refund_date': datetime.datetime.now(),
                                                                 'refund_details.$.refund_status': status}})

    # def appointment_cancel_refund_payment_check(self,order_id,appointment_id):

    # result = list(self.mongo_db['Paymentgateway3'].find({"user_id": info.user_id,"payment_status": "success"}))
    def display_payment_info(self, info: Payment_info):
        # logger.info("revenue Controller display user info")
        try:
            result = list(self.mongo_db['Paymentgateway3'].find({"user_id": info.user_id}))
            result_list = []
            for resp in result:
                dic = {}
                """resp_user: DBUser = self.db.query(dbmodels.DBUser).filter_by(
                userid=resp['user_id']).one_or_none()
                if resp_user:
                    dic['user_firstname'] = resp_user.firstname
                    dic['user_lastname'] = resp_user.lastname
                    dic['user_email'] = resp_user.email
                    dic['user_mobile'] = resp_user.mobile
                    #logger.info("till user info")
                    #logger.info(dic)"""

                dic['order_id'] = resp['order_id']
                # Allocating null values then resigning them in further steps if payment was scuess full and appointment was booked.

                dic['payment_for'] = ''
                dic['appointment_id'] = ''
                dic['appointment_date'] = ''
                dic['appointment_time'] = ''
                dic['doctor_name'] = ''
                dic['doctor_id'] = ''
                dic['refund_status'] = ''
                dic['refund_date'] = ''
                # logger.info("just till basic payment info")
                # logger.info(dic)

                # Reassigning values based on payment status
                if resp['appointment_id']:
                    app = self.mongo_db['Appointments'].find_one({"appointment_id": resp['appointment_id']})
                    # dt =datetime.strptime(app['appointment_slot'], "%Y-%m-%dT%H:%M:%S.%f%z")
                    if app:  # this if condition is incase an appointment id from another host is added to not give any errors
                        dt = parse(str(app['appointment_slot']))
                        dic['appointment_id'] = resp['appointment_id']
                        dic['appointment_date'] = dt.date()
                        dic['appointment_time'] = dt.time()
                        dic['payment_for'] = app['appointment_for']
                        doc_id = app['doctorid']
                        doc = self.mongo_db['DoctorsInfo'].find_one({"doctorid": doc_id})
                        dic['doctor_id'] = doc_id
                        dic['doctor_name'] = doc['profilename']
                # logger.info("till appointment info")
                # logger.info(dic)
                dic['amount'] = resp['amount']
                # pt =datetime.strptime(resp['payment_date'], "%Y-%m-%dT%H:%M:%S.%f%z")
                pt = parse(str(resp['payment_date']))
                dic['payment_date'] = pt.date()
                dic['payment_time'] = pt.time()
                dic['payment_status'] = resp['payment_status']
                dic['is_activity_confirmed'] = resp['is_activity_confirmed']
                # logger.info("till payment info")
                # logger.info(dic)

                # step 2 of reassigning values
                if resp['refund_details']:
                    dic['refund_status'] = resp['refund_details']['refund_status']
                    if resp['refund_details']['refund_date']:
                        dic['refund_date'] = resp['refund_details']['refund_date']
                    # logger.info("till refund info")
                    # logger.info(dic)
                # logger.info("each payment info")
                # logger.info(dic)
                result_list.append(dic)
            return result_list



        except Exception as e:
            err = str(e)
            raise Exception(f'Internal Error code {err} for retriving payment details of user_id{info.user_id}')

    def date_key(self, item):
        return datetime.datetime.strptime(item['payment_date'], '%d-%m-%Y')

    def payment_display(self, request_data: Get_payment, exclude_id: str = None):
        try:
            li = []
            dr = {}
            patient_list = []
            if request_data.name is not None:
                if len(request_data.name) < 4:
                    raise Exception('Name search string should be of 4 letters atleast')
                users = self.db.query(dbmodels.DBUser).filter(or_(
                    dbmodels.DBUser.firstname.like(f"%{request_data.name}%"),
                    dbmodels.DBUser.lastname.like(f"%{request_data.name}%")
                )).all()
                if users is not None:
                    for user in users:
                        patient_list.append(user.userid)
                relatives = self.db.query(dbmodels.DBRelatives).filter(or_(
                    dbmodels.DBRelatives.firstname.like(f"%{request_data.name}%"),
                    dbmodels.DBRelatives.lastname.like(f"%{request_data.name}%")
                )).all()
                if relatives is not None:
                    for relative in relatives:
                        patient_list.append(relative.relativeid)

            if exclude_id is not None:
                if request_data.order_id is not None:
                    payment_gateway_resp = self.mongo_db['Paymentgateway3'].find(
                        {"$and": [{'user_id': {'$ne': exclude_id}}, {'order_id': request_data.order_id}]}).sort(
                        'payment_date', -1)

                    # one_payment_resp = self.mongo_db['One_Payment'].find({"$and":[{'user_id': {'$ne': exclude_id}}, {'order_id':request_data.order_id}]}).sort('date', -1)

                elif request_data.case_id is not None:
                    payment_gateway_resp = self.mongo_db['Paymentgateway3'].find(
                        {"$and": [{'user_id': {'$ne': exclude_id}}, {'case_id': request_data.case_id}]}).sort(
                        'payment_date', -1)

                    # one_payment_resp = self.mongo_db['One_Payment'].find({"$and":[{'user_id': {'$ne': exclude_id}}, {'case_id':request_data.case_id}]}).sort('date', -1)

                elif request_data.name is not None:
                    if patient_list is None:
                        payment_gateway_resp = []
                        one_payment_resp = []
                    else:
                        payment_gateway_resp = self.mongo_db['Paymentgateway3'].find({"$and": [
                            {'user_id': {'$ne': exclude_id}},
                            {'user_id': {'$in': patient_list}}
                        ]}).sort('payment_date', -1)

                        """one_payment_resp = self.mongo_db['One_Payment'].find({"$and":[
                            {'user_id': {'$ne': exclude_id}},
                            {'user_id': {'$in': patient_list}}
                        ]}).sort('date', -1)"""
                elif request_data.payment_status is not None:
                    payment_gateway_resp = self.mongo_db['Paymentgateway3'].find(
                        {"$and": [{'user_id': {'$ne': exclude_id}}, {'payment_status': request_data.payment_status}]}).sort(
                        'payment_date', -1)

                    

                else:
                    payment_gateway_resp = self.mongo_db['Paymentgateway3'].find({'user_id': {'$ne': exclude_id}}).sort(
                        'payment_date', -1)
                    # one_payment_resp = self.mongo_db['One_Payment'].find({'user_id': {'$ne': exclude_id}}).sort('date', -1)

            else:
                if request_data.order_id is not None:
                    payment_gateway_resp = self.mongo_db['Paymentgateway3'].find(
                        {'order_id': request_data.order_id}).sort('payment_date', -1)
                    # one_payment_resp = self.mongo_db['One_Payment'].find({'order_id':request_data.order_id}).sort('date', -1)
                elif request_data.case_id is not None:
                    payment_gateway_resp = self.mongo_db['Paymentgateway3'].find(
                        {'case_id': request_data.case_id}).sort('payment_date', -1)
                    # one_payment_resp = self.mongo_db['One_Payment'].find({'case_id':request_data.case_id}).sort('date', -1)
                elif request_data.name is not None:
                    if patient_list is None:
                        payment_gateway_resp = []
                        one_payment_resp = []
                    else:
                        payment_gateway_resp = self.mongo_db['Paymentgateway3'].find(
                            {'user_id': {'$in': patient_list}
                             }).sort('payment_date', -1)

                        """one_payment_resp = self.mongo_db['One_Payment'].find(
                            {'user_id': {'$in': patient_list}
                        }).sort('date', -1)"""
                elif request_data.payment_status is not None:
                    payment_gateway_resp = self.mongo_db['Paymentgateway3'].find(
                        {'payment_status': request_data.payment_status}
                    ).sort('payment_date', -1)

                else:
                    payment_gateway_resp = self.mongo_db['Paymentgateway3'].find().sort('payment_date', -1)
                    # one_payment_resp = self.mongo_db['One_Payment'].find().sort('date', -1)

            for resp in payment_gateway_resp:
                dr = {}
                for key, values in resp.items():
                    if key == 'user_id':
                        resp_user: DBUser = self.db.query(dbmodels.DBUser).filter_by(
                            userid=values).one_or_none()
                        if resp_user:
                            dr['user_firstname'] = resp_user.firstname
                            dr['user_lastname'] = resp_user.lastname
                            dr['user_email'] = resp_user.email
                            dr['user_mobile'] = resp_user.mobile
                    if key == 'payment_date':
                        timestamp = datetime.datetime.fromisoformat(str(values))
                        formatted_date = timestamp.strftime("%d-%m-%Y")
                        formtted_time = timestamp.strftime("%H:%M:%S")
                        dr['payment_date'] = formatted_date
                        dr['payment_time'] = formtted_time
                    if key != '_id' and key != 'payment_date':
                        dr[key] = values
                # logger.info(dr)
                li.append(dr)

            """for resp in one_payment_resp:
                dr={}
                for key,values in resp.items():

                    if key == 'user_id':
                        resp_user: DBUser = self.db.query(dbmodels.DBUser).filter_by(
                        userid=values).one_or_none()
                        if resp_user:
                            dr['user_firstname'] = resp_user.firstname
                            dr['user_lastname'] = resp_user.lastname
                            dr['user_email'] = resp_user.email
                            dr['user_mobile'] = resp_user.mobile
                    if key == 'date':
                        date = datetime.datetime.strptime(values, '%d-%m-%Y')
                        formatted_date = date.strftime("%d-%m-%y")
                        dr['payment_date'] = values
                    if key == 'status':
                        dr['payment_status'] = values
                    if key=='invoice_id':
                        dr['order_id']=values

                    if key != '_id' and key != 'date' and key != 'status' and key!='invoice_id' :
                        dr[key] = values

                li.append(dr)"""

            sorted_li = sorted(li, key=self.date_key, reverse=True)
            return sorted_li
        except Exception as e:
            raise HTTPException(status_code=409, detail=str(e))

    def payment_delete(self, id: str):
        self.mongo_db['Paymentgateway1'].find_one_and_delete({"order_id": id})
        return {"details": f"deleted payment for order_id {id}"}

    def generate_Bill_id(self):
        mongo_collection = self.mongo_db['Paymentgateway3']
        appointment_count = str(mongo_collection.count_documents({}) + 1)
        bill_id = 'A' + appointment_count.rjust(6,"0")
        return bill_id

    def insert_into_receipt(self, appointment_id, Bill_date):
        try:
            res = self.mongo_db['Receipt'].find_one({"appointment_id": appointment_id})
            # if exists then check for status in recipt and payment and if mismatch with payment as sucess
            # then overwrite same data with sucess in reciept and recipt id = bill_id+str(len(all_matching_bill_ids))
            if res is None:
                bill_id = self.generate_Bill_id()
                receipt_id = bill_id
                result = self.mongo_db['Paymentgateway3'].find_one(
                    {"appointment_id": appointment_id, "payment_status": "Successful"})
                rec_obj = dict(bill_id=bill_id, receipt_id=receipt_id, transaction_id=result['transaction_id'],
                               case_id=result['case_id'], appointment_id=result['appointment_id'], bill_date=Bill_date,
                               service=result['appointment_or_membership'], status=result['payment_status'],
                               payment_type="Payment Gate way")

                mongo_collection = self.mongo_db['Receipt']
                mongo_collection.insert_one(rec_obj)
                return bill_id, receipt_id
            else:
                return res['bill_id'], res['receipt_id']
        except Exception as e:
            loggers['logger3'].info("/payment/appointment : {status_code=409, detail= " + str(e) + "}")
            err = str(e)
            raise Exception(f'Internal Error code {err} for add billing data of receipt ID: {receipt_id}')

    def payment_list(self, user_id):
        s3_instance = AWSS3Client()

        app1 = list(self.mongo_db['Appointments'].find({"booked_by": user_id}, projection = {"appointment_id": 1, "appointment_slot": 1, "doctorid": 1}).sort([("appointment_slot", -1)]))

        all_doctors = [x.get("doctorid") for x in app1]

        doctor_data = list(self.mongo_db["DoctorsInfo"].find({"doctorid":{"$in":list(set(all_doctors))}}))

        doctor_info = {}
        for entry in doctor_data:
            doctor_info[entry["doctorid"]] = entry

        res = []

        for app in app1:
            dic = {}

            resp = self.mongo_db['Receipt'].find_one({"appointment_id": app['appointment_id']})

            doc = doctor_info[app["doctorid"]]
            # logger.info("doctors info receipt")
            # logger.info(doc)
            # logger.info(app['appointment_id'])
            # logger.info(app['doctorid'])
            dt = parse(str(app['appointment_slot']))
            appointment_time = str(dt.time())
            appointment_date = str(dt.date())
            time_obj = datetime.datetime.strptime(appointment_time, "%H:%M:%S")
            hour = time_obj.strftime("%I").lstrip("0")
            minute = time_obj.strftime("%M")
            am_pm = time_obj.strftime("%p")

            # Format the time as "2:45 PM"
            formatted_time = f"{hour}:{minute} {am_pm}"
            # formatted_time = time_obj.strftime("%-I:%M %p").lstrip("0").replace(" 0", " ")
            date_obj = datetime.datetime.strptime(appointment_date, "%Y-%m-%d")
            formatted_date = date_obj.strftime("%b %d, %Y")
            # dic['payment_for']=app['appointment_for']
            # dic['doctor_id']=doc_id
            doctor_name = doc['profilename']
            dic['des'] = doctor_name + ", " + str(formatted_date) + ", " + str(formatted_time)
            dic["doctor_name"] = doctor_name
            dic["date"] = str(formatted_date)
            dic["time"] = str(formatted_time)
            dic['appointment_id'] = app['appointment_id']
            if resp:
                dic['Receipt_link'] = s3_instance.get_presigned_url(resp['receipt_link']) if 'receipt_link' in resp else ""
            else:
                dic['msg'] = "need to generate receipt link"
            res.append(dic)
        return res

    def display_payment_info_pdf(self, appointment_id: str):
        # logger.info("revenue Controller display user info in PDF")
        try:
            from . import dbmodels
            result = self.mongo_db['Paymentgateway3'].find_one(
                {"appointment_id": appointment_id, "payment_status": "Successful"})
            result_list = []
            re = {}
            re['list'] = []
            # dic['order_id']=result['order_id']
            appointment = self.mongo_db['Appointments'].find_one({"appointment_id": result['appointment_id']})
            # dt =datetime.strptime(app['appointment_slot'], "%Y-%m-%dT%H:%M:%S.%f%z")
            if appointment is not None:

                amount = 0
                resp_user: DBUser = self.db.query(dbmodels.DBUser).filter_by(
                    userid=appointment.get('patient_id')).one_or_none()

                if resp_user is None:
                    resp_user: dbmodels.DBRelatives = self.db.query(dbmodels.DBRelatives).filter_by(
                    relativeid=appointment.get('patient_id')).one_or_none()

                if resp_user:
                    re['name'] = resp_user.firstname + " " + resp_user.lastname
                    re['UID'] = appointment.get('patient_id')
                    re['gender'] = resp_user.gender
                    re['email'] = resp_user.email
                    re['user_mobile'] = resp_user.mobile
                    dob_date = datetime.datetime.strptime(str(resp_user.birthdate), "%Y-%m-%d")
                    current_date = datetime.datetime.now()
                    age_timedelta = current_date - dob_date
                    age_years = age_timedelta.days // 365
                    re['age'] = age_years
                    pt = parse(str(current_date))
                    payment_date = str(pt.date())
                    # dic['payment_time']=pt.time()
                    date_obj = datetime.datetime.strptime(payment_date, "%Y-%m-%d")
                    formatted_date = date_obj.strftime("%b %d, %Y")
                    re['Bill_date'] = formatted_date
                    # logger.info("till user info")
                    # logger.info(re)

                    re['bill_id'] = self.generate_Bill_id()
                    re['receipt_id'] = re['bill_id']
                    re['bill_id'], re['receipt_id'] = self.insert_into_receipt(appointment_id=appointment_id,
                                                                               Bill_date=re['Bill_date'])

                    # Allocating null values then resigning them in further steps if payment was scuess full and appointment was booked.

                    # Reassigning values based on payment status


                    re['case_id'] = appointment['caseid']
                    dic = {}  # this if condition is incase an appointment id from another host is added to not give any errors
                    dt = parse(str(appointment['appointment_slot']))
                    dic['app_type'] = appointment['appointment_type']
                    # dic['appointment_id']=app['appointment_id']
                    # dic['caseid']=app['caseid']
                    appointment_date = str(dt.date())
                    appointment_time = str(dt.time())
                    time_obj = datetime.datetime.strptime(appointment_time, "%H:%M:%S")
                    hour = time_obj.strftime("%I").lstrip("0")
                    minute = time_obj.strftime("%M")
                    am_pm = time_obj.strftime("%p")

                    # Format the time as "2:45 PM"
                    formatted_time = f"{hour}:{minute} {am_pm}"
                    # formatted_time = time_obj.strftime("%-I:%M %p").lstrip("0").replace(" 0", " ")
                    date_obj = datetime.datetime.strptime(appointment_date, "%Y-%m-%d")
                    formatted_date = date_obj.strftime("%b %d, %Y")
                    # dic['payment_for']=app['appointment_for']
                    doc_id = appointment['doctorid']
                    dic['doc_id'] = appointment['doctorid']
                    doc = self.mongo_db['DoctorsInfo'].find_one({"doctorid": doc_id})
                    # dic['doctor_id']=doc_id
                    doctor_name = doc['profilename']
                    dic['des'] = doctor_name + " " + str(formatted_date) + " " + str(formatted_time)

                    pay = self.mongo_db['Paymentgateway3'].find_one({"appointment_id": appointment['appointment_id']})

                    dic['amount'] = pay['amount']

                    re['list'].append(dic)

                pt = parse(str(pay['payment_date']))
                payment_date = str(pt.date())
                # dic['payment_time']=pt.time()
                date_obj = datetime.datetime.strptime(payment_date, "%Y-%m-%d")
                formatted_date = date_obj.strftime("%b %d, %Y")
                re['transaaction_date'] = str(formatted_date)
                re['Transaction_id'] = pay['transaction_id']

                # logger.info("till appointment info")
                # logger.info(re)

            discount_amount = 0
            amount = result['amount']
            discount_amount = 0
            if 'promo_code' in result:
                if result['promo_code'] is not None:
                    re['promo_code'] = result['promo_code']
                    dic1 = {}
                    resp = self.mongo_db['Promotion'].find_one({'promotion_Code': result['promo_code']})
                    promotion_amt = int(round(float(resp["promotion_value"])))

                    if resp['promotion_type'] == "Flat":

                        discount_amount = promotion_amt
                        doctor_data: DBDoctor = self.db.query(dbmodels.DBDoctor).filter(
                            DBDoctor.doctorid == re['list'][0]['doc_id']).one_or_none()
                        if re['list'][0]['app_type'] == "Virtual":
                            re['list'][0]['amount'] = doctor_data.consulting_fees_virtual
                        else:
                            re['list'][0]['amount'] = doctor_data.consulting_fees_clinic
                    else:
                        if promotion_amt != 100:
                            actual_price = (int(amount) * 100) / (100 - promotion_amt)
                            discount_amount = actual_price - int(amount)
                            doctor_data: DBDoctor = self.db.query(dbmodels.DBDoctor).filter(
                                DBDoctor.doctorid == re['list'][0]['doc_id']).one_or_none()
                            if re['list'][0]['app_type'] == "Virtual":
                                re['list'][0]['amount'] = doctor_data.consulting_fees_virtual
                            else:
                                re['list'][0]['amount'] = doctor_data.consulting_fees_clinic
                        else:
                            doctor_data: DBDoctor = self.db.query(dbmodels.DBDoctor).filter(
                                DBDoctor.doctorid == re['list'][0]['doc_id']).one_or_none()
                            if re['list'][0]['app_type'] == "Virtual":
                                actual_price = doctor_data.consulting_fees_virtual
                                discount_amount = actual_price - int(amount)
                                re['list'][0]['amount'] = doctor_data.consulting_fees_virtual
                            else:
                                actual_price = doctor_data.consulting_fees_clinic
                                discount_amount = actual_price - int(amount)
                                re['list'][0]['amount'] = doctor_data.consulting_fees_clinic

            re['discount_amount'] = discount_amount

            re['amount'] = result['amount']
            # re['case_id']=result['case_id']
            # logger.info(re)
            return re

        except Exception as e:
            err = str(e)
            raise Exception(f'Internal Error code {err} for retriving payment details of case_id{appointment_id}')


    def one_time_pay_datastore(self, data: dict):
        try:
            rec_obj = dict(data)
            mongo_collection = self.mongo_db['Paymentgateway3']
            mongo_collection.insert_one(rec_obj)
        except Exception as e:
            err = str(e)
            raise Exception(f'Error occurred as {err} while adding data to payment gateway collection')

    def confirm_appointment(self, id: str):
        ptn_ctrl = PatientController(db=self.db, mongo=self.mongo)
        mh_ctrl = MentalHealthController(db=self.db, mongo=self.mongo)
        self.mongo_db['Appointments'].find_one_and_update({"appointment_id": id}, {
            "$set": {'is_active': True, 'is_confirmed': True}})
        res_app = self.mongo_db['Appointments'].find_one({"appointment_id": id})
        if 'status' in res_app:
            is_first_appointment = ptn_ctrl.manage_first_appointment(patient_id=res_app['patient_id'])
            self.mongo_db['Appointments'].find_one_and_update({"appointment_id": id}, {
                "$set": {'is_active': True, 'is_confirmed': True,
                         'status': dict(status='Booked', Reason='', comment=''), 'payment_status': 'Successful',
                         'is_first_appointment': is_first_appointment
                         }})
        if res_app.get("caseid") is None:
            open_caseid_exist = mh_ctrl.get_open_case_id(patient_id=str(res_app.get("patient_id")),
                                                     doctor_id=str(res_app.get("doctorid")))
            caseid = open_caseid_exist if open_caseid_exist else mh_ctrl.generate_case_id()
            self.mongo_db['Appointments'].find_one_and_update({"appointment_id": id}, {
                "$set": {
                    "caseid": caseid
                         }})
        notif_data_exist = self.mongo_db['Notifications'].find_one({'object_id': id})
        if notif_data_exist is None:
            ptn_ctrl.manage_notifications_for_booked_appointments(appointment_id=id)
        return

    def one_time_appointment_datastore(self, data: dict):
        try:
            rec_obj = dict(data)
            mongo_collection = self.mongo_db['appointment_one_payment']
            mongo_collection.insert_one(rec_obj)
        except Exception as e:
            err = str(e)
            raise Exception(f'Error occured as {err} while adding one time payment')

    def get_invoice_list(self):
        try:
            # Pending
            resp = list(self.mongo_db['One_Payment'].find({"$or": [{"status": "initiate"}, {"status": "Pending"}]}))
            # resp=self.mongo_db['One_Payment'].find({"status": "initiate"})
            return resp
        except Exception as e:
            err = str(e)
            raise Exception(f'Error occured as {err} while getting list in one time payment')

    def set_invoice_status(self, id, status):
        try:

            self.mongo_db['Paymentgateway3'].find_one_and_update({"order_id": id},
                                                             {'$set': {'status': status}})

            return
        except Exception as e:
            err = str(e)
            raise Exception(f'Error occured as {err} while updating status on one time payment')

    def get_invoice(self):
        try:

            li = []
            for resp in self.mongo_db['One_Payment'].find():
                resp_user: DBUser = self.db.query(dbmodels.DBUser).filter_by(
                    userid=resp['user_id']).one_or_none()
                if resp_user:
                    name = resp_user.firstname + " " + resp_user.lastname
                li.append(
                    dict(date=resp['date'], invoice_id=resp['invoice_id'], amount=resp['amount'], status=resp['status'],
                         name=name))
            return li

            return
        except Exception as e:
            err = str(e)
            raise Exception(f'Error occured as {err} while updating status on one time payment')

    def insert_into_receipt_invoice(self, appointment_id, Bill_date):
        try:
            res = self.mongo_db['Receipt'].find_one({"appointment_id": appointment_id})
            receipt_id = ''
            # if exists then check for status in recipt and payment and if mismatch with payment as sucess
            # then overwrite same data with sucess in reciept and recipt id = bill_id+str(len(all_matching_bill_ids))
            if res is None:
                bill_id = self.generate_Bill_id()
                receipt_id = bill_id
                result = self.mongo_db['Paymentgateway3'].find_one({"appointment_id": appointment_id})
                rec_obj = dict(bill_id=bill_id, receipt_id=receipt_id, transaction_id=result['transaction_id'],
                               case_id=" ", appointment_id=result['appointment_id'], bill_date=Bill_date,
                               service="appointment", status=result['payment_status'], payment_type="Quick_book")

                mongo_collection = self.mongo_db['Receipt']
                mongo_collection.insert_one(rec_obj)
                return bill_id, receipt_id
            else:
                return res['bill_id'], res['receipt_id']
        except Exception as e:
            err = str(e)
            raise Exception(f'Internal Error code {err} for add billing data of receipt ID: {receipt_id}')

    def get_invoice_receipt(self, id: str) -> InvoiceDetails:
        try:
            result = self.mongo_db['Paymentgateway3'].find_one(
                {"order_id": id})
            invoice_details = InvoiceDetails()

            appointment_details = self.mongo_db['Appointments'].find_one(
                {"appointment_id": result['appointment_id']})
            if appointment_details:
                resp_user = BaseHealthController(self.db, self.mongo).get_patient_details(appointment_details['patient_id'])
                bill_date = str(result['payment_date'].date())
                dob_date = datetime.datetime.strptime(
                    str(resp_user['dob']), "%Y-%m-%d")
                current_date = datetime.datetime.now()
                age_timedelta = current_date - dob_date
                age_years = age_timedelta.days // 365

                name_title = "Mr"
                if (resp_user['gender'] == "Female"):
                    name_title = "Mrs"
                    if (age_years < 18):
                        name_title = "Ms"

                bill_id, receipt_id = self.insert_into_receipt_invoice(
                    appointment_id=result['appointment_id'],
                    Bill_date=bill_date)

                name = resp_user['firstname'] + " " + resp_user['lastname']
                invoice_details.name = name
                invoice_details.UID = result['user_id']
                invoice_details.age = age_years
                invoice_details.bill_date = bill_date
                invoice_details.gender = resp_user['gender']
                invoice_details.email = resp_user['email']
                invoice_details.user_mobile = resp_user['mobile']
                invoice_details.bill_id = bill_id
                invoice_details.receipt_id = receipt_id


                dt = parse(str(appointment_details['appointment_slot']))
                appointment_date = str(dt.date())
                appointment_time = str(dt.time())
                time_obj = datetime.datetime.strptime(
                    appointment_time, "%H:%M:%S")
                hour = time_obj.strftime("%I").lstrip("0")
                minute = time_obj.strftime("%M")
                am_pm = time_obj.strftime("%p")

                # Format the time as "2:45 PM"
                formatted_time = f"{hour}:{minute} {am_pm}"
                date_obj = datetime.datetime.strptime(
                    appointment_date, "%Y-%m-%d")
                formatted_date = date_obj.strftime("%b %d, %Y")
                doc_id = appointment_details['doctorid']
                doc_query = {"doctorid": doc_id}
                doc = self.mongo_db['DoctorsInfo'].find_one(doc_query)
                doctor_name = doc['profilename']
                pay = self.mongo_db['Paymentgateway3'].find_one(
                    {"appointment_id": appointment_details['appointment_id']})
                t_date = str(pay['payment_date'].date())

                invoice_details.description = f"{name_title} {name}'s consultation with {doctor_name} {formatted_date} {formatted_time}"
                invoice_details.amount = pay['amount']
                invoice_details.case_id = appointment_details['caseid']
                invoice_details.transaaction_date = t_date
                invoice_details.transaction_id = pay['transaction_id']
                invoice_details.payment_mode = pay['payment_mode']

            return invoice_details

        except Exception as e:
            err = str(e)
            raise Exception(
                f'Internal Error code {err} for retriving one time payment details of invoice_id{id}')

    def get_doc_id(self, id: str):
        res = self.mongo_db['Appointments'].find_one({"appointment_id": id})
        return res

    def appointment_payment(self, invoice_id: str):
        invoice_details = self.get_invoice_receipt(id=invoice_id)
        client = boto3.client('s3', region_name=AWS_REGION_NAME)

        # pdfmetrics.registerFont(TTFont('Arial', 'C:\Windows\Fonts\Arial.ttf'))
        # /usr/share/fonts/truetype/msttcorefonts/Arial.ttf
        # pdfmetrics.registerFont(TTFont('Arial', PDF_FONT_FILE_PATH))
        ttf_path = os.path.join("ayoo_backend", "extras", "Rupee_Foradian.ttf")
        pdfmetrics.registerFont(TTFont('Rupee', ttf_path))

        # Create a new PDF file
        buffer = io.BytesIO()
        output_pdf = canvas.Canvas(buffer)

        font_length = 11 * 0.6
        font_color = "#000"
        page_x, page_y = A4

        def add_header():
            # page_x, page_y = page
            output_pdf.setFont("Helvetica-Bold", 14)
            font_length = 14 * 0.6
            ayoo_logo = os.path.join("ayoo_backend", "extras", "logo.png")
            image = ImageReader(ayoo_logo)
            output_pdf.drawImage(image, x=40, y=page_y-84, width=44, height=44)
            doc_header = "TAX INVOICE"
            header_x = (page_x//2)-((len(doc_header)//2)*font_length)
            header_y = page_y - 28 - 40
            output_pdf.drawString(header_x, header_y, doc_header)
            output_pdf.setStrokeColor("#262224")
            output_pdf.setLineWidth(0.25)  # Set the line width
            output_pdf.line(40, page_y-92, page_x-40, page_y-92)
            output_pdf.setStrokeColor(font_color)
            return (40, page_y-102)

        def add_footer():
            # page_x, page_y = page
            x, y = 40, 122
            output_pdf.setLineWidth(0.25)
            output_pdf.setFillColor(colors.HexColor("#262224"))
            output_pdf.setStrokeColor(colors.HexColor("#262224"))
            output_pdf.line(x, y, page_x-x, y)

            output_pdf.setFillColor(colors.HexColor("#999798"))
            output_pdf.setStrokeColor(colors.HexColor("#999798"))
            output_pdf.setFont("Helvetica-Bold", 11)
            output_pdf.drawString(40, 91, "AYOO Care")
            output_pdf.setFont("Helvetica", 8)
            output_pdf.drawString(40, 78, "AYOO MEDICAL PVT LTD")
            address = [
                "7/A, 14th A-Cross,",
                "HSR Layout, Sec 6, Bengaluru, Karnataka, 560102.",
                "Tel: +91-**********, E-mail: <EMAIL>"
            ]
            y = 78
            for line in address:
                y -= 10
                output_pdf.drawString(40, y, line)

            output_pdf.drawString(267, 78, "GSTIN: 29**********1ZY")
            output_pdf.drawString(267, 65, "PAN: **********")
            output_pdf.drawString(267, 52, "Place of Service: Bangalore")

            logo_path = os.path.join("ayoo_backend", "extras", "footer_logo.png")
            output_pdf.drawImage(logo_path, 413, 50, width=140, height=48)

        # Create a new PDF file
        # output_pdf = canvas.Canvas("buffer.pdf", A4)
        x, y = add_header()
        add_footer()

        output_pdf.setFont("Helvetica-Bold", 11)
        font_length = 11 * 0.6

        # Line 1:
        x, y = x+4, y-11
        output_pdf.setStrokeColor(colors.HexColor("#000000"))
        output_pdf.setFillColor(colors.HexColor("#000000"))
        name_string = 'Name: {}'.format(invoice_details.name)
        output_pdf.drawString(x, y, name_string)
        output_pdf.setFillColor(colors.Color(0, 0, 0, 0.40))
        output_pdf.setFont("Helvetica", 11)
        x = x + (len(name_string)*font_length) + 16
        output_pdf.drawString(x, y, "|")
        output_pdf.setFillColor(colors.HexColor("#000000"))
        gender_string = '{}'.format(invoice_details.gender)
        x = x + font_length + 16
        output_pdf.drawString(x, y, gender_string)
        x = x + (len(gender_string)*font_length) + 16
        output_pdf.setFillColor(colors.Color(0, 0, 0, 0.40))
        output_pdf.drawString(x, y, "|")
        output_pdf.setFillColor(colors.HexColor("#000000"))
        age_string = '{} yrs'.format(invoice_details.age)
        output_pdf.drawString(x+16, y, age_string)
        bill_date = invoice_details.bill_date
        bill_date_label = "BILL DATE:"
        x = page_x - 44 - ((len(bill_date)-2) * font_length)
        output_pdf.drawString(x, y, bill_date)
        x = x - (len(bill_date_label) * font_length)
        output_pdf.setFont("Helvetica-Bold", 11)
        output_pdf.drawString(x, y, bill_date_label)

        # Line 2:
        x, y = 44, y - 23
        invoice_label = "Invoice #"
        output_pdf.drawString(x, y, invoice_label)
        output_pdf.setFont("Helvetica", 11)
        x = x + ((len(invoice_label)-1)*font_length)
        output_pdf.drawString(x, y, "{}".format(invoice_details.bill_id))
        output_pdf.setFont("Helvetica-Bold", 11)
        hns_label = "HSN/SAC: "
        x = 238
        output_pdf.drawString(x, y, f"{hns_label}")
        output_pdf.setFont("Helvetica", 11)
        hns_value = "999312"
        x += (len(hns_label)*font_length)
        output_pdf.drawString(x, y, f"{hns_value}")
        case_id = invoice_details.case_id
        case_id_label = "Case ID:"
        x = page_x - 44 - (len(case_id)*font_length)
        output_pdf.drawString(x, y, case_id)
        output_pdf.setFont("Helvetica-Bold", 11)
        x = x - (len(case_id_label)*font_length)
        output_pdf.drawString(x, y, case_id_label)
        output_pdf.setFont("Helvetica", 11)

        # Line 3:
        x, y = 40, y - 10
        output_pdf.setLineWidth(0.25)  # Set the line width
        output_pdf.line(x, y, page_x-x, y)

        # Line 4:
        x, y = 40, y - 12 - 14
        services_label = "SERVICES"
        output_pdf.setStrokeColor("#EBEDEB")
        output_pdf.setFillColor("#EBEDEB")
        output_pdf.rect(x, y, (page_x - 80), 14, 1, 1)
        output_pdf.setFillColor("#000000")
        output_pdf.setFont("Helvetica-Bold", 9)
        output_pdf.drawString(x+4, y+2, services_label)

        # Line 5:
        x, y = 40, y - 8
        output_pdf.setLineWidth(0.25)
        output_pdf.setDash(7, 3)
        output_pdf.setFillColor(colors.Color(0, 0, 0, 0.7))
        output_pdf.setStrokeColor(colors.Color(0, 0, 0, 0.7))
        output_pdf.line(x, y, page_x-x, y)

        # Line 6:
        x, y = 44, y - 19
        output_pdf.setFillColor("#000000")
        output_pdf.setStrokeColor("#000000")
        output_pdf.drawString(x, y, services_label)
        output_pdf.drawString(116, y, "DESCRIPTION")
        output_pdf.drawString(254, y, "UNIT COST")
        output_pdf.drawString(344, y, "QTY")
        output_pdf.drawString(410, y, "TAX")
        output_pdf.drawString(498, y, "TOTAL COST")

        # Line 7:
        x, y = 40, y - 9
        output_pdf.setLineWidth(0.25)
        output_pdf.setFillColor(colors.Color(0, 0, 0, 0.7))
        output_pdf.setStrokeColor(colors.Color(0, 0, 0, 0.7))
        output_pdf.setDash(7, 3)
        output_pdf.line(x, y, page_x-x, y)
        output_pdf.setFillColor("#000000")
        output_pdf.setStrokeColor("#000000")

        def fit_description(c: canvas.Canvas, description: str, y_pos: int, x_pos: int, font_size: int):
            line_y = y_pos
            wrap_list = textwrap.wrap(description, width=27)
            for word in wrap_list:
                c.drawString(x_pos, line_y, word)
                line_y -= (font_size * 1.2)

            line_y += (font_size * 1.2)
            return line_y

        # Table:
        # TODO: CHECK THIS SECTION. Need to add new keys in invoice details and func
        output_pdf.setFont("Helvetica", 8)
        # for service in invoice_details["list"]:
        #     y = y - 17
        #     output_pdf.drawString(44, y, service["ser"])
        #     output_pdf.drawString(259, y, service["unit"])
        #     output_pdf.drawString(349, y, service["qty"])
        #     output_pdf.drawString(413, y, service["tax"])
        #     output_pdf.drawString(500, y, service["total"])
        #     y = fit_description(output_pdf, service["des"], y, 116, 8)

        amt = invoice_details.amount
        y = y - 17
        output_pdf.drawString(44, y, "Consultation")
        output_pdf.drawString(259, y, f"INR {amt}")
        output_pdf.drawString(349, y, "1")
        output_pdf.drawString(413, y, "0")
        output_pdf.drawString(500, y, f"INR {amt}")
        y = fit_description(output_pdf, invoice_details.description, y, 116, 8)

        # Line 8:
        x, y = 40, y - 9
        output_pdf.setLineWidth(0.25)
        output_pdf.setFillColor(colors.Color(0, 0, 0, 0.7))
        output_pdf.setStrokeColor(colors.Color(0, 0, 0, 0.7))
        output_pdf.setDash(7, 3)
        output_pdf.line(x, y, page_x-x, y)
        output_pdf.setFillColor("#000000")
        output_pdf.setStrokeColor("#000000")

        # Total section:
        y -= 34
        output_pdf.setDash(1, 0)
        output_pdf.setStrokeColor("#EBEDEB")
        output_pdf.setFillColor("#EBEDEB")
        output_pdf.rect(323, y, 232, 18, 1, 1)
        output_pdf.setFillColor("#333233")
        output_pdf.setFont("Helvetica-Bold", 9)
        output_pdf.drawString(327, y+4, "TOTAL AMOUNT")
        output_pdf.setFont("Helvetica", 9)
        output_pdf.drawString(500, y+4, f"INR {amt}")

        # Paid Amout:
        output_pdf.setFont("Helvetica", 8)
        y -= 19
        output_pdf.drawString(327, y, "AMOUNT PAID")
        output_pdf.drawString(500, y, f"INR {amt}")
        y -= 19
        output_pdf.drawString(327, y, "BALANCE")
        output_pdf.drawString(500, y, "0")
        y -= 19
        output_pdf.drawString(327, y, "PAYMENT METHOD")
        output_pdf.drawString(500, y, invoice_details.payment_mode)
        y -= 19
        output_pdf.drawString(327, y, "TRANSACTION ID")
        output_pdf.drawString(499, y, invoice_details.transaction_id)
        y -= 19
        output_pdf.drawString(327, y, "TRANSACTION DATE")
        output_pdf.drawString(500, y, invoice_details.transaaction_date)

        output_pdf.save()
        buffer.seek(0)

        cid = invoice_details.case_id
        uid = invoice_details.UID
        receipt_id = invoice_details.receipt_id
        img_key = "output" + str(receipt_id) + ".pdf"
        object_key = f'patient/{uid}/{cid}/invoices/{img_key}'
        try:
            client.put_object(Body=buffer, Bucket=AWS_BUCKET_NAME, Key=object_key)
            location = client.get_bucket_location(Bucket=AWS_BUCKET_NAME)['LocationConstraint']
            url = f"https://s3-{location}.amazonaws.com/{AWS_BUCKET_NAME}/{object_key}"
            self.mongo_db['Receipt'].find_one_and_update({"receipt_id": receipt_id},
                                                         {"$set": {"receipt_link": url}})

            return url
            # "s3: // ayoo - web - bucket / symptoms / image / 0df516de-6380-4080-aa47-72d9402b65ba.png"
            # "https://ayoo-web-bucket.s3.ap-south-1.amazonaws.com/symptoms/image/0df516de-6380-4080-aa47-72d9402b65ba.png"
        except Exception as e:
            err = str(e)
            return None

    def create_promo(self, request: promo_code):

        # random_number = random.randint(1000, 9999)
        # promotion_Code="PROMO"+str(random_number)

        dic = dict(promo_id=str(uuid.uuid4()), promotion_Code=request.promotion_Code,
                   promotion_type=request.Discount_Type, promotion_value=request.Discount_of,
                   promotion_availabilty_count=request.used_times, promotion_starts_on=request.valid_from,
                   promotion_expires_on=request.valid_until,
                   Promotion_Name=request.Promotion_Name,
                   Promotion_Short_Description=request.Promotion_Short_Description,
                   Promotion_Terms=request.Promotion_Terms if request.Promotion_Terms else '',
                   Payment_Option=request.Payment_Option if request.Payment_Option else '',
                   Bank_Name_Card_Name=request.Bank_Name_Card_Name if request.Bank_Name_Card_Name else '',
                   promotion_availabilty_user=request.used_user if request.used_user else '',
                   promotion_availabilty_card=request.used_card if request.used_card else '',
                   promotion_availabilty_token=request.used_token if request.used_token else '',
                   max_discount_for_percentage=request.max_discount if request.max_discount else '',
                   doctor_id=request.doctor_id if request.doctor_id else '',
                   )
        mongo_collection = self.mongo_db['Promotion']
        mongo_collection.insert_one(dic)
        return dic['promotion_Code']

    def get_all_promo(self):
        mongo_collection = self.mongo_db['Promotion']
        resp = list(self.mongo_db['Promotion'].find())
        li = []

        for res in resp:
            dic = {}
            for key, value in res.items():
                if key != '_id':
                    dic[key] = value
            li.append(dic)

        return li

    def get_promo_by_id(self, id: str):
        resp = self.mongo_db['Promotion'].find_one({'promo_id': id})
        dic = {}
        for key, value in resp.items():
            if key != '_id':
                dic[key] = value
        return dic

    def apply_promotion(self, data: promotion, id: str):
        resp = self.mongo_db['Promotion'].find_one({'promotion_Code': data.promo_code})

        if resp is None:
            raise HTTPException(status_code=409, detail='Promo code not found')

        else:
            resp1 = len(list(self.mongo_db['Paymentgateway3'].find({'promo_code': data.promo_code, 'user_id': id}).clone()))
            if resp1 >= int(resp['promotion_availabilty_count']):
                raise HTTPException(status_code=409, detail=f'Promo code {data.promo_code} already used')

            promotion_amt = int(round(float(resp['promotion_value'])))
            present_date = datetime.datetime.now().strftime("%Y-%m-%d")
            if present_date >= resp['promotion_starts_on'] and present_date <= resp['promotion_expires_on']:
                if resp['promotion_type'] == "Flat":
                    final_value = int(data.amount) - promotion_amt
                    if final_value < 0:
                        final_value = 0
                else:
                    final_value = int(int(data.amount) - ((promotion_amt / 100) * int(data.amount)))
                    if final_value < 0:
                        final_value = 0
                dic = {'amount': str(final_value), 'promo_code': data.promo_code}
                return dic
            else:
                raise HTTPException(status_code=409, detail='promo code expired')

    def fetch_promotion_for_user(self, patient_id:str, doctor_id:str):
        try:
            # at present, we are doing the happy case for dr. vikram, hence assuming only on record per doctor in db. this should be changed later as per requirement
            resp = self.mongo_db['Promotion'].find_one({'doctor_id': doctor_id})

            if resp is None:
                return {
                    'msg':'No Coupon Fetched',
                    'coupon':''
                }

            else:
                appointments = list(self.mongo_db['Appointments'].find({
                    '$and': [
                        {'$or': [{'patient_id': patient_id}, {'booked_by': patient_id}]},
                        {'doctorid': doctor_id},
                        {'status.status': {'$in': ['Booked', 'Completed']}}
                    ]
                }).clone())

                print("Appointments Found:", appointments)
                print(appointments)
                if appointments:
                    return {
                        'msg': 'Coupon not applicable',
                        'coupon': ''
                    }

            present_date = datetime.datetime.now().strftime("%Y-%m-%d")
            if present_date >= resp['promotion_starts_on'] and present_date <= resp['promotion_expires_on']:
                return {
                    'msg': 'Coupon Fetched',
                    'coupon': resp.get('promotion_Code', '')
                }
            else:
                return {
                    'msg': 'Coupon Expired',
                    'coupon': ''
                }
        except Exception as e:
            raise HTTPException(status_code=409, detail=str(e))


    def decrypt(self, cipher_text, working_key, secret_key=""):
        try:
            iv = bytes('\x00\x01\x02\x03\x04\x05\x06\x07\x08\x09\x0a\x0b\x0c\x0d\x0e\x0f', 'utf-8')
            enc_digest = hashlib.md5(working_key.encode('utf-8')).digest()
            dec_cipher = AES.new(enc_digest, AES.MODE_CBC, iv)
            decrypted_text = dec_cipher.decrypt(bytes.fromhex(cipher_text))
            text = decrypted_text.rstrip(b'\0').decode('utf-8')
            data_pairs = text.split('&')
            req_keys = {}
            for pair in data_pairs:
                key, value = pair.split('=')
                req_keys[key] = value

            order_id = req_keys["order_id"]
            tracking_id = req_keys["tracking_id"]
            order_status = req_keys["order_status"]
            payment_mode = req_keys["payment_mode"]
            payment_card = req_keys["card_name"]

            payment_view = Paymentsuccess(
                order_id=order_id,
                transaction_id=tracking_id,
                payment_mode=payment_mode,
                payment_card=payment_card
            )

            match order_status:
                case "Success":
                    self.payment_sucess(payment_view)
                    return "success"

                case "Failure":
                    self.payment_failure(payment_view)
                    return "failure"

                case "Aborted":
                    self.payment_Aborted(payment_view)
                    return "aborted"

                case "Awaited":
                    self.payment_awaited(payment_view)
                    return "awaited"

                case default:
                    return "Default"

        except Exception as e:
            err = str(e)
            raise Exception(f'Internal Error code {err} for ccavenue response handeler')
