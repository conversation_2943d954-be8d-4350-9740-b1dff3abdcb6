from fastapi import APIRouter, Depends, HTTPException, Form
from ..DataModels.appointment import Appointment, AppointmentType, BookedVia, AppointmentV2, AppointmentStatusV2, PreviousAppointment
from ..DataModels.payment import Payment, AOM, PaymentStatus
from ..DAOs.appointmentDAO import AdminAppointmentFilters, AppointmentV2DAO, UsersAppointmentFilters, UserAppointmentV2Sort
from ..DTOs.adminslots import BookingSlotListingDTO, time_format
from ..DAOs.paymentDAO import CustomFeesDAO, FindCustomFeesQuery, PaymentDAO, AppointmentPaymentObject
from ..DAOs.promotionDAO import PromotionDAO, GetByPromoCode
from ..database import base_to_json
from .payloads import AppointmentBooking
from uuid import uuid4
from .gateway import CCAvenueGateway
from .crypt import CCAvenueEncryptDecrypt
from .DTOs import PaymentAppointmentBooking

payments_router = APIRouter(prefix = "/payments_v2", tags = ["payments_v2"])

@payments_router.post("/appointment_booking")
async def initiate_appointment_booking_payment(payload: AppointmentBooking):
    appointment: AppointmentV2 = AppointmentV2DAO().get(payload.appointment_id)
    print("Appointment", appointment)
    doctor_slots = BookingSlotListingDTO.get_slots_for_doctor(doctor_id = appointment.doctor_id, search_date = appointment.start_time, availability_type = appointment.appointment_type, user_id = appointment.patient_id).slots
    slot_duration = int((appointment.end_time - appointment.start_time).total_seconds()/60)
    print()
    if str(slot_duration) not in doctor_slots:
        raise HTTPException(400, "Doctor is not available for the consultation duration !!!!")
    rate = doctor_slots.get(str(slot_duration)).consultation_fees
    if appointment.case_id is not None:
        custom_fees_query = FindCustomFeesQuery(case_id = appointment.case_id, patient_id = appointment.patient_id, doctor_id = appointment.doctor_id, duration = slot_duration)
        custom_fees_record = CustomFeesDAO().list(0, 1, filters = custom_fees_query)
        if len(custom_fees_record) >0:
            rate = custom_fees_record.amount 
    if payload.promo_code is not None:
        promo_list = PromotionDAO().list(0,  1, filter = GetByPromoCode(promotion_Code = payload.promo_code))
        if len(promo_list) == 0:
            raise HTTPException(400, "Invalid Promo Code !!!!")
        promo = promo_list[0]
        if not promo.check_availability(appointment.patient_id):
            raise HTTPException(400, "Promo Code already used !!!!")
        rate = promo.apply_promotion(rate)
    if float(payload.amount) != float(rate):
        raise HTTPException(400, "Amount is invalid !!!!")
    order_id = str(uuid4())
    secret_key = str(uuid4())
    payment_obj_list = PaymentDAO().list(filter = AppointmentPaymentObject(appointment_id = str(appointment.appointment_id)))
    payment = Payment(order_id = order_id, secret_key = secret_key, user_id = payload.user_id, appointment_or_membership = AOM.appointment, appointment_id = payload.appointment_id, amount = payload.amount, payment_status = PaymentStatus.initiated, doctor_id = appointment.doctor_id, case_id = appointment.case_id, promo_code = payload.promo_code)
    if len(payment_obj_list) > 0:
        obj_id = payment_obj_list[0].id
        update_results = PaymentDAO().update(_id = obj_id, _obj = payment)
    else:
        create_results = PaymentDAO().create(payment)
    iframe_url = CCAvenueGateway().create_transaction_url(payload.amount, order_id, secret_key)
    return PaymentAppointmentBooking(order_id = order_id, iframe_url = iframe_url)

@payments_router.post("/test_payment_redirect")
async def payment_success(secret_key: str, orderNo: str = Form(), encResp: str = Form()):
    print(secret_key)
    print(orderNo)
    print(encResp)
    response = CCAvenueEncryptDecrypt().decrypt(encResp)
    return response

@payments_router.get("/order_status")
async def order_status(order_id: str):
    return CCAvenueGateway().get_order_status(order_id = order_id)

