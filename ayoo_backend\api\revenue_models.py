from pydantic import BaseModel
from enum import Enum
from typing import Optional, List
from datetime import date, datetime, time


class Appointment_or_Membership(str, Enum):
    appointment = 'appointment'
    membership = 'membership'
    adhoc = 'adhoc'


class Status(str, Enum):
    initiate = 'initiate'
    success = 'Successful'
    cancelled = 'Failed'
    aborted = 'Aborted'
    Invalid = 'Invalid'
    Pending = 'Pending'
    Awaited = 'Awaited'
    Initiated = "Initiated"


class Mode(str, Enum):
    credit_card = 'credit_card'
    debit_card = 'debit_card'


class AppointmentType(str, Enum):
    InClinic = 'InClinic'
    Virtual = 'Virtual'


class PatientRelationType(str, Enum):
    Child = 'Child'
    Spouse = 'Spouse'
    Others = 'Others'
    Parent = 'Parent'
    Self = 'Self'


class Get_payment(BaseModel):
    skip: Optional[int] = 0
    limit: Optional[int] = 30
    name: Optional[str] = None
    case_id: Optional[str] = None
    order_id: Optional[str] = None
    payment_status: Optional[str] = None


class Adhoc_Payment(BaseModel):
    user_id: str
    amount: str
    email: Optional[bool]
    mobile: Optional[bool]
    appointment_id: Optional[str] = ''
    promo_code: Optional[str] = ''


class Adhocview(BaseModel):
    order_id: str
    user_id: str
    amount: str
    promo_code: Optional[str]
    appointment_id: Optional[str]


class One_Time_Payment(BaseModel):
    user_id: str
    appointment_id: str
    # name: str
    # email:str
    amount: str  #
    promo_code: Optional[str] = None
    # mobile_no:str #
    # appointment_type: AppointmentType
    # appointment_for: PatientRelationType
    # symptoms: Optional[List] = []
    # symptoms_audio_clip: Optional[str]
    # additional_notes: Optional[str]
    # clinicid: Optional[str]
    # doctorid: Optional[str]
    # appointment_slot: Optional[str]
    # is_active: bool = True
    # is_confirmed: bool = True
    # payment: Optional[str]


class Payment(BaseModel):
    order_id: str
    transaction_id: Optional[str] = ''
    user_id: str
    promo_code: Optional[str]
    # username: str
    amount: str
    doctor_id: Optional[str] = ''
    # doctorname:Optional[str]=''
    appointment_id: str
    case_id: Optional[str] = ''
    # appointment_date:Optional[str]=''
    appointment_or_membership: Appointment_or_Membership
    payment_mode: str
    payment_status: Status
    payment_date: datetime
    secret_key: str
    is_activity_confirmed: bool = False
    refund_details: Optional[dict]
    url: Optional[str] = ''

    class Config:
        orm_mode = True
        use_enum_values = True


class payment_payload(BaseModel):
    encResp: str
    order_id: str


class Paymentview(BaseModel):
    user_id: str
    amount: str
    appointment_or_membership: Appointment_or_Membership
    promo_code: Optional[str]
    appointment_id: str
    # payment_mode:Mode
    # payment_status:Status
    # payment_date:datetime


class Paymentsuccess(BaseModel):
    order_id: str
    transaction_id: str
    payment_mode: str
    payment_card: str
    # payment_mode:Mode
    # payment_status:Status
    # payment_date:datetime


class Payments_appointment(BaseModel):
    order_id: str
    appointment_id: str
    case_id: Optional[str]
    # doctor_id:Optional[str]=''
    # appointment_date:str


class Paymentstatus(BaseModel):
    order_id: str


class Payment_info(BaseModel):
    appointment_id: str

    class Config:
        orm_mode = True


class Paymentrefund(BaseModel):
    order_id: str


class appointment_failed(BaseModel):
    user_id: Optional[str]


class Discount(str, Enum):
    Flat = 'Flat'
    Percentage = 'Percentage'


class Promotion_Details(BaseModel):
    valid_from: date
    used_times: Optional[str]
    used_user: Optional[str]
    used_card: Optional[str]
    used_token: Optional[str]


class promo_code(BaseModel):
    promotion_Code: str
    Promotion_Name: str
    Promotion_Short_Description: str
    Promotion_Terms: Optional[str]
    Discount_Type: Discount
    Discount_of: str  # discount amount or percentage
    Payment_Option: Optional[str]
    Bank_Name_Card_Name: Optional[str]
    valid_from: str
    valid_until: str
    used_times: str
    used_user: Optional[str]
    used_card: Optional[str]
    used_token: Optional[str]
    # Details:Optional[Promotion_Details]
    max_discount: Optional[str]
    doctor_id: Optional[str] = ''

    class Config:
        use_enum_values = True


class promotion(BaseModel):
    promo_code: str
    amount: str


class promotion_admin(BaseModel):
    promo_code: str
    amount: str
    userid: str


class OrderQueryRequest(BaseModel):
    order_id: Optional[str] = None
    invoice_id: Optional[str] = None
    refrence_no: Optional[str] = None
    invoice_date: Optional[str] = None
    order_type: str


class AdhocPaymentStatusRequest(BaseModel):
    order_id: str


class AdhocPaymentStatusResponse(BaseModel):
    order_id: str
    payment_status: str
    transaction_id: Optional[str] = None
    payment_mode: Optional[str] = None
    payment_card: Optional[str] = None
    updated_from_gateway: bool = False
    message: str
