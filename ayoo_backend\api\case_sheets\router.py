from fastapi import APIRouter, Depends, HTTPException, UploadFile, File
from typing import Optional, List
from ..api_configs import AWS_BUCKET_NAME, AWS_REGION_NAME
from ..file_upload_service.file_upload_controller import fileUploadContoller
from ..file_upload_service.utils.s3 import AWSS3ClientUtil
from ..DataModels.case_sheet import <PERSON>Com<PERSON>, CaseSheet, CaseSummary, DoctorNotes, PrescriptionType, FollowUp, \
    Recommendation, WorkUpPlanOrRecommendation, Medication, Assessment, SubstanceUse, MediaFileStore, LabTests, \
    ReferCaseView, UpdateToReferee, UpdateToReferrer, UpdatePrescription, ShareCaseSheet
from ..services.auth import get_doctor_from_token
from ..view_controller import AdminController
from ..mongodb import mongodb_conn
from ..database import init_db
from ..api_configs import DATABASE_URL, OTP_GENERATOR
from datetime import datetime
from ..DAOs.caseSheetDAO import CaseSheetDAO, GetByAppointmentId, ReferCaseDAO
from ..ayoo_utils import check_if_past_next_day_eod

postgres_db = init_db(DATABASE_URL)
admin_ctrl = AdminController(db=postgres_db, mongo=mongodb_conn)

case_router = APIRouter(prefix="/case_sheet", dependencies=[Depends(get_doctor_from_token)], tags=["case sheet"])

from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials

security = HTTPBearer()


def get_token_from_header(credentials: HTTPAuthorizationCredentials = Depends(security)):
    token = credentials.credentials
    if not token:
        raise HTTPException(status_code=401, detail="Authorization token is missing")
    return token


# case_router = APIRouter(prefix="/case_sheet", tags=["case sheet"])

@case_router.get('/fetch_appointment_id')
async def fetch_appointment_id(case_id: str):
    return CaseSheetDAO().fetch_appointment_id(case_id=case_id)


@case_router.get('/all')
async def get_all_case_sheets(case_id: str):
    return_obj = CaseSheetDAO().get_all(case_id=case_id)
    all_case_sheets = return_obj.get("case_sheets", [])
    s3_client = AWSS3ClientUtil()
    for case_sheet in all_case_sheets:
        doctor_notes = case_sheet.get('doctor_notes', [])
        for note_index, note in enumerate(doctor_notes):
            for index, file_obj in enumerate(note.get('images', [])):
                case_sheet["doctor_notes"][note_index]["images"][index]["file_path"] = s3_client.get_presigned_url(
                    file_obj.get("file_path"))
            for index, file_obj in enumerate(note.get("audio_files", [])):
                case_sheet["doctor_notes"][note_index]["audio_files"][index]["file_path"] = s3_client.get_presigned_url(
                    file_obj.get("file_path"))
    return_obj["case_sheets"] = all_case_sheets
    return return_obj


@case_router.post('/chief_complain')
async def add_chief_complain(appointment_id: str, prescription_type: PrescriptionType, data: ChiefComplain):
    return CaseSheetDAO().add_to_array(appointment_id=appointment_id, prescription_type=prescription_type,
                                       key='chief_complain', value=data)


@case_router.put('/chief_complain')
async def update_chief_complain(appointment_id: str, index: int, data: ChiefComplain):
    return CaseSheetDAO().patch_array(appointment_id=appointment_id, index=index, key='chief_complain', value=data)


@case_router.delete('/chief_complain')
async def delete_chief_complain(appointment_id: str, index: int):
    return CaseSheetDAO().delete_from_array(appointment_id=appointment_id, index=index, key='chief_complain')


@case_router.post('/doctor_notes')
async def add_doctor_notes(appointment_id: str, prescription_type: PrescriptionType, data: DoctorNotes):
    return CaseSheetDAO().add_to_array(appointment_id=appointment_id, prescription_type=prescription_type,
                                       key='doctor_notes', value=data)


@case_router.post('/doctor_notes/add_files')
async def add_doctor_notes_files(appointment_id: str, prescription_type: PrescriptionType,
                                 files: List[UploadFile] = File(None)):
    try:
        _obj = CaseSheetDAO().create_case_schema(appointment_id=appointment_id, prescription_type=prescription_type)
        _obj = CaseSheetDAO().get_one(appointment_id=appointment_id)
        if not _obj.get('case_sheet_in_edit_mode'):
            raise Exception('Cannot add note for this appointment. Modify case sheet to add new notes.')

    except Exception as e:
        raise HTTPException(400, str(e))
    if not _obj.get("is_open"):
        raise HTTPException(400, "Case not open!!!")
    _obj = CaseSheet(**_obj)
    doctor_notes = _obj.doctor_notes
    if doctor_notes == [] or (
            len(doctor_notes) > 0 and (doctor_notes[0].is_reissue_notes or doctor_notes[0].is_modify_notes)):
        note = DoctorNotes(created_at=datetime.now())
        CaseSheetDAO().add_to_array(appointment_id=appointment_id, prescription_type=prescription_type,
                                    key='doctor_notes', value=note)
        doctor_notes.insert(0, note)

    latest_note = doctor_notes[0]
    file_path_prepend = f'patient/{appointment_id}/doctor_notes'
    for file in files:
        url = f"https://{AWS_BUCKET_NAME}.s3.{AWS_REGION_NAME}.amazonaws.com/{file_path_prepend}/{file.filename}"
        file_object = MediaFileStore(file_path=url, file_name=file.filename, file_tag=file.content_type,
                                     file_id=int(datetime.now().timestamp() % 1E6))
        if "image" in file.content_type:
            latest_note.images.append(file_object)
        elif "audio" in file.content_type:
            latest_note.audio_files.append(file_object)
        elif "pdf" in file.content_type:
            latest_note.docs.append(file_object)
        elif "doc" in file.content_type:
            latest_note.docs.append(file_object)
        AWSS3ClientUtil().upload_file_obj(file=file, image_key=f'{file_path_prepend}/{str(file.filename)}')
    latest_note.set_updated_at(datetime.now())
    object_id = _obj.id
    del _obj.id
    return CaseSheetDAO().update(_id=object_id, _obj=_obj)


@case_router.get('/doctor_notes/remove_file')
async def remove_doctor_notes_files(appointment_id: str, url: str):
    try:
        _obj = CaseSheetDAO().get_one(appointment_id=appointment_id)
        if not _obj.get('case_sheet_in_edit_mode'):
            raise Exception('Cannot add note for this appointment. Modify case sheet to add new notes.')
    except Exception as e:
        raise HTTPException(400, str(e))
    if not _obj.get("is_open"):
        raise HTTPException(400, "Case not open!!!")
    _obj = CaseSheet(**_obj)
    doctor_notes = _obj.doctor_notes
    if doctor_notes == [] or (
            len(doctor_notes) > 0 and (doctor_notes[0].is_reissue_notes or doctor_notes[0].is_modify_notes)):
        raise HTTPException(400, "No doctor notes added, Please add first !!!!!")
    latest_note = doctor_notes[0]
    image_list = [x for x in latest_note.images if x.file_path != url]
    audio_list = [x for x in latest_note.audio_files if x.file_path != url]
    latest_note.images = image_list
    latest_note.audio_files = audio_list
    latest_note.set_updated_at(datetime.now())
    object_id = _obj.id
    del _obj.id
    update = CaseSheetDAO().update(_id=object_id, _obj=_obj)
    return latest_note


@case_router.put('/doctor_notes')
async def update_doctor_notes(appointment_id: str, data: DoctorNotes):
    return CaseSheetDAO().patch_object(appointment_id=appointment_id, key='doctor_notes', value=data)


@case_router.patch('/doctor_notes')
async def patch_doctor_notes(appointment_id: str, data: DoctorNotes):
    try:
        _obj = CaseSheetDAO().get_one(appointment_id=appointment_id)
        if not _obj.get('case_sheet_in_edit_mode'):
            raise Exception('Cannot add note for this appointment. Modify case sheet to add new notes.')
    except Exception as e:
        raise HTTPException(400, str(e))
    if not _obj.get("is_open"):
        raise HTTPException(400, "Case not open!!!")
    _obj = CaseSheet(**_obj)
    doctor_notes = _obj.doctor_notes
    if doctor_notes == [] or (
            len(doctor_notes) > 0 and (doctor_notes[0].is_reissue_notes or doctor_notes[0].is_modify_notes)):
        raise HTTPException(400, "No doctor notes added, Please add first !!!!!")
    latest_note = doctor_notes[0]
    latest_note.notes = data.notes
    latest_note.set_updated_at(datetime.now())
    object_id = _obj.id
    del _obj.id
    return CaseSheetDAO().update(_id=object_id, _obj=_obj)


@case_router.delete('/doctor_notes')
async def delete_doctor_notes(appointment_id: str):
    return CaseSheetDAO().delete_object(appointment_id=appointment_id, key='doctor_notes')


@case_router.post('/substance_use')
async def add_substance_use(appointment_id: str, prescription_type: PrescriptionType, data: SubstanceUse):
    return CaseSheetDAO().add_to_array(appointment_id=appointment_id, prescription_type=prescription_type,
                                       key='substance_use', value=data)


@case_router.put('/substance_use')
async def update_substance_use(appointment_id: str, index: int, data: SubstanceUse):
    return CaseSheetDAO().patch_array(appointment_id=appointment_id, index=index, key='substance_use', value=data)


@case_router.delete('/substance_use')
async def delete_substance_use(appointment_id: str, index: int):
    return CaseSheetDAO().delete_from_array(appointment_id=appointment_id, index=index, key='substance_use')


@case_router.post('/assessment')
async def add_assessment(appointment_id: str, prescription_type: PrescriptionType, data: Assessment):
    return CaseSheetDAO().add_to_array(appointment_id=appointment_id, prescription_type=prescription_type,
                                       key='assessment', value=data)


@case_router.put('/assessment')
async def update_assessment(appointment_id: str, index: int, data: Assessment):
    return CaseSheetDAO().patch_array(appointment_id=appointment_id, index=index, key='assessment', value=data)


@case_router.delete('/assessment')
async def delete_assessment(appointment_id: str, index: int):
    return CaseSheetDAO().delete_from_array(appointment_id=appointment_id, index=index, key='assessment')


@case_router.post('/medication')
async def add_medication(appointment_id: str, prescription_type: PrescriptionType, data: Medication):
    return CaseSheetDAO().add_to_array(appointment_id=appointment_id, prescription_type=prescription_type,
                                       key='medication', value=data)


@case_router.put('/medication')
async def update_medication(appointment_id: str, index: int, data: Medication):
    return CaseSheetDAO().patch_array(appointment_id=appointment_id, index=index, key='medication', value=data)


@case_router.delete('/medication')
async def delete_medication(appointment_id: str, index: int):
    return CaseSheetDAO().delete_from_array(appointment_id=appointment_id, index=index, key='medication')


@case_router.post('/lab_tests')
async def add_lab_tests(appointment_id: str, prescription_type: PrescriptionType, data: LabTests):
    return CaseSheetDAO().request_lab_tests(appointment_id=appointment_id, prescription_type=prescription_type,
                                            value=data)


@case_router.put('/lab_tests')
async def update_lab_tests(appointment_id: str, record_id: str, data: LabTests):
    return CaseSheetDAO().update_lab_tests(appointment_id=appointment_id, record_id=record_id, value=data)


@case_router.delete('/lab_tests')
async def delete_lab_tests(appointment_id: str, record_id: str):
    return CaseSheetDAO().delete_lab_tests(appointment_id=appointment_id, record_id=record_id)


@case_router.post('/work_up_plan_or_recommendation')
async def add_work_up_plan_or_recommendation(appointment_id: str, prescription_type: PrescriptionType,
                                             data: WorkUpPlanOrRecommendation):
    return CaseSheetDAO().add_to_array(appointment_id=appointment_id, prescription_type=prescription_type,
                                       key='work_up_plan_or_recommendation', value=data)


@case_router.put('/work_up_plan_or_recommendation')
async def update_work_up_plan_or_recommendation(appointment_id: str, index: int, data: WorkUpPlanOrRecommendation):
    return CaseSheetDAO().patch_array(appointment_id=appointment_id, index=index, key='work_up_plan_or_recommendation',
                                      value=data)


@case_router.delete('/work_up_plan_or_recommendation')
async def delete_work_up_plan_or_recommendation(appointment_id: str, index: int):
    return CaseSheetDAO().delete_from_array(appointment_id=appointment_id, index=index,
                                            key='work_up_plan_or_recommendation')


@case_router.post('/therapy_recommendation')
async def add_therapy_recommendation(appointment_id: str, prescription_type: PrescriptionType, data: Recommendation):
    return CaseSheetDAO().add_to_array(appointment_id=appointment_id, prescription_type=prescription_type,
                                       key='therapy_recommendation', value=data)


@case_router.put('/therapy_recommendation')
async def update_therapy_recommendation(appointment_id: str, index: int, data: Recommendation):
    return CaseSheetDAO().patch_array(appointment_id=appointment_id, index=index, key='therapy_recommendation',
                                      value=data)


@case_router.delete('/therapy_recommendation')
async def delete_therapy_recommendation(appointment_id: str, index: int):
    return CaseSheetDAO().delete_from_array(appointment_id=appointment_id, index=index, key='therapy_recommendation')


@case_router.post('/follow_up')
async def add_follow_up(appointment_id: str, prescription_type: PrescriptionType, data: FollowUp):
    # objective_update = CaseSheetDAO().set_object(appointment_id = appointment_id, prescription_type = prescription_type, key = "follow_up_objective", value = data.objectives)
    return CaseSheetDAO().set_object(appointment_id=appointment_id, prescription_type=prescription_type,
                                     key='follow_up', value=data)


@case_router.put('/follow_up')
async def update_follow_up(appointment_id: str, data: FollowUp):
    return CaseSheetDAO().patch_object(appointment_id=appointment_id, key='follow_up', value=data)


@case_router.delete('/follow_up')
async def delete_follow_up(appointment_id: str):
    return CaseSheetDAO().delete_object(appointment_id=appointment_id, key='follow_up')


@case_router.post('/close_case')
async def close_case(appointment_id: str, prescription_type: PrescriptionType, data: CaseSummary):
    return CaseSheetDAO().set_object(appointment_id=appointment_id, prescription_type=prescription_type,
                                     key='case_summary', value=data, close_case=True)


@case_router.post('/prescription_modify_reissue')
async def prescription_modify_reissue(appointment_id: str, data: UpdatePrescription):
    try:
        _obj = CaseSheetDAO().get_one(appointment_id=appointment_id)

        if _obj.get('prescription_s3_object_url', None) is None and data.update_type == 'ReIssue':
            raise Exception('Prescription pdf is not generated for previous reissued/modified case sheet')

        return CaseSheetDAO().re_issue_and_modify_prescription(appointment_id=appointment_id, data=data)

    except Exception as e:
        raise HTTPException(400, str(e))


@case_router.post('/prescription_generate')  # prescription issue
async def prescription_generate(appointment_id: str, preview: bool = False):
    return CaseSheetDAO().generate_prescription(appointment_id=appointment_id, preview=preview)


# # for testing
# @case_router.post('/prescription_expire')  # prescription expire
# async def prescription_expire(appointment_id: str, preview: bool = False):
#     return CaseSheetDAO().expire_prescription(appointment_id=appointment_id, preview=preview)
#


@case_router.post('/submit')  # prescription issue
async def submit_case_sheet(appointment_id: str):
    return CaseSheetDAO().submit_case_sheet_button(appointment_id=appointment_id)


@case_router.post("/share", tags=["case sheet"])
async def share_case_sheet_to_patient(data: ShareCaseSheet):
    return CaseSheetDAO().share_case_sheet_to_patient(data=data)


@case_router.get("/clinical_history", tags=["case sheet"])
async def get_clinical_history_from_doctor(patient_id: str):
    return CaseSheetDAO().get_clinical_history_of_patient(patient_id=str(patient_id))


@case_router.get("/referral/available_doctors", tags=["case sheet"])
async def get_doctors_available_for_referral(patient_id: str):
    return ReferCaseDAO().get_doctors_available_for_referral(patient_id=patient_id)


@case_router.get("/referred_case_details", tags=["case sheet"])
async def get_referred_case_details(case_id: str, token: str = Depends(get_token_from_header)):
    doctor_id = get_doctor_from_token(token=token)
    return ReferCaseDAO().get_referral_info(case_id=case_id, doctor_id=doctor_id)


@case_router.get("/updates_made_to_referrer", tags=["case sheet"])
async def get_updates_made_to_referrer(case_id: str):
    return ReferCaseDAO().get_updates_made_to_referrer(case_id=case_id)


@case_router.post("/refer", tags=["case sheet"])
async def refer_case_to_ayoo_doctor(referral_data: ReferCaseView):
    return ReferCaseDAO().refer_existing_case(referral_data=referral_data)


@case_router.post("/update_to_referee", tags=["case sheet"])
async def update_to_referee(referral_data: UpdateToReferee):
    return ReferCaseDAO().update_to_referee(referral_data=referral_data)


@case_router.post("/update_to_referer", tags=["case sheet"])
async def update_to_referer(referral_data: UpdateToReferrer):
    return ReferCaseDAO().update_to_referrer(referral_data=referral_data)


@case_router.put("/update_referral_notes", tags=["case sheet"])
async def update_to_referee(note_id: str, referral_data: UpdateToReferee, token: str = Depends(get_token_from_header)):
    doctor_id = get_doctor_from_token(token=token)
    return ReferCaseDAO().update_referral_notes(note_id=note_id, referral_data=referral_data, doctor_id=doctor_id)
