from ayoo_backend.api.database import Base
from enum import Enum
from typing import Optional, List, Union
from bson import ObjectId
from datetime import datetime
from ayoo_backend.api.viewmodels import City

from pydantic import BaseModel
from ayoo_backend.api.viewmodels import Gender, City


class DoctorType(str, Enum):
    General = 'General'
    Specialists = 'Specialists'
    Allied_health_professionals = 'Allied health professionals'


class DoctorSpecialization(str, Enum):
    Audiologist = 'Audiologist'
    Cardiologist = 'Cardiologist'
    Dentist = 'Dentist'
    ENT_Specialist = 'ENT Specialist'
    Gynaecologist = 'Gynaecologist'
    Orthopaedic = 'Orthopaedic'
    Paediatrician = 'Paediatrician'
    Psychiatrists = 'Psychiatrists'
    Veterinarian = 'Veterinarian'
    Radiologist = 'Radiologist'
    Pulmonologist = 'Pulmonologist'
    Endocrinologist = 'Endocrinologist'
    Oncologist = 'Oncologist'
    Neurologist = 'Neurologist'
    Physician = 'Physician'
    Ophthalmologist = 'Ophthalmologist'


class AlliedHealthProfessional(str, Enum):
    Dietitians = 'Dietitians'
    Physiotherapists = 'Physiotherapists'
    Podiatrists = 'Podiatrists'
    Speech_pathologists = 'Speech Pathologists'
    Psychologists = 'Psychologists'


class ListOfSpecialization(str, Enum):
    Audiologist = 'Audiologist'
    Cardiologist = 'Cardiologist'
    Dentist = 'Dentist'
    ENT_Specialist = 'ENT Specialist'
    Gynaecologist = 'Gynaecologist'
    Orthopaedic = 'Orthopaedic'
    Paediatrician = 'Paediatrician'
    Psychiatrists = 'Psychiatrists'
    Veterinarian = 'Veterinarian'
    Radiologist = 'Radiologist'
    Pulmonologist = 'Pulmonologist'
    Endocrinologist = 'Endocrinologist'
    Oncologist = 'Oncologist'
    Neurologist = 'Neurologist'
    Physician = 'Physician'
    Ophthalmologist = 'Ophthalmologist'
    Dietitians = 'Dietitians'
    Physiotherapists = 'Physiotherapists'
    Podiatrists = 'Podiatrists'
    Speech_pathologists = 'Speech Pathologists'
    Psychologists = 'Psychologists'

    @classmethod
    def has_value(cls, value):
        return value in cls._value2member_map_


class ListOfDepartment(str, Enum):
    Audiologist = 'Audiology'
    Cardiologist = 'Cardiology'
    Dentist = 'Dental'
    ENT_Specialist = 'ENT'
    Gynaecologist = 'Gynaecology'
    Orthopaedic = 'Orthopedics'
    Paediatrician = 'Pediatrics'
    Psychiatrists = 'Psychiatry'
    Veterinarian = 'Veterinary'
    Radiologist = 'Radiology'
    Pulmonologist = 'Pulmonology'
    Endocrinologist = 'Endocrinology'
    Oncologist = 'Oncology'
    Neurologist = 'Neurology'
    Physician = 'Family Care'
    Ophthalmologist = 'Ophthalmology'
    Dietitians = 'Dietetics and Nutrition'
    Physiotherapists = 'Physiotherapy'
    Podiatrists = 'DPM'
    Speech_pathologists = 'Speech Pathology'
    Psychologists = 'Psychology'


class SpecializationAndAbbreviation(str, Enum):
    Audiologist = 'AU'
    Cardiologist = 'CA'
    Dentist = 'DE'
    ENTSpecialist = 'ES'
    Gynaecologist = 'GY'
    Orthopaedic = 'OR'
    Paediatrician = 'PA'
    # Psychiatrists = 'PC'
    Veterinarian = 'VE'
    Radiologist = 'RA'
    Pulmonologist = 'PU'
    Endocrinologist = 'EN'
    Oncologist = 'ON'
    Neurologist = 'NE'
    Physician = 'PH'
    Ophthalmologist = 'OP'
    Dietitians = 'DI'
    # Physiotherapists = 'PT'
    Podiatrists = 'PO'
    Speech_pathologist = 'SP'
    Psychologist = 'PS'
    ConsultantPsychiatrist = 'PC'
    Therapist = 'PT'
    ConsultantPhysician = 'CP'


# class AvailabilityType(str, Enum):
#     InClinic = 'InClinic'
#     Virtual = 'Virtual'


class DoctorConsultationDetails(BaseModel):
    slot_duration: Optional[int] = 30
    fees: Optional[int] = 1000


class DoctorSignUpView(BaseModel):
    doctorid: Optional[str]
    firstname: str
    lastname: str
    dob: str
    email: str
    mobile: str
    password: str
    gender: Gender
    ayooid: Optional[str]
    consulting_duration_virtual: Optional[int] = 30
    consulting_duration_clinic: Optional[int] = 15
    consulting_fees_virtual: Optional[int] = 500
    consulting_fees_clinic: Optional[int] = 500

    profilename: Optional[str] = ''
    doctortype: Optional[DoctorType] = DoctorType.General
    specialization: Optional[str] = 'Physician'
    prescription_type: Optional[str] = ''
    specialization_field: Optional[str] = 'Medical Health'
    languages: Optional[List[str]] = []
    graduation: Optional[str] = ''
    masters: Optional[str] = ''
    additional_qualification: Optional[str] = ''
    fellowship: Optional[str] = ''
    residency: Optional[str] = ''
    experience: Optional[str] = ''
    practice_area: Optional[List[str]] = []
    interest_area: Optional[List[str]] = []
    consultation_symptoms: Optional[List[str]] = []
    awards: Optional[str] = ''
    homeaddress: Optional[str] = ''
    bio: Optional[List[str]] = []
    degree: Optional[List[dict]] = []
    working_hour_starts_at: Optional[str] = '9:00 AM'
    working_hour_ends_at: Optional[str] = '6:00 PM'
    family_doctor_active: Optional[bool]

    license: Optional[str] = ''
    signature: Optional[str] = ''
    clinics_attached: Optional[List] = []
    image_id: Optional[str] = ''
    profile_image_url: Optional[str] = ''
    is_active: Optional[bool] = True

    virtual_consultation_and_fees: Optional[List[DoctorConsultationDetails]] = []
    clinic_consultation_and_fees: Optional[List[DoctorConsultationDetails]] = []

    display_sequence: int = 1


class DoctorsInfo(BaseModel):
    doctorid: str
    profilename: Optional[str] = ''
    doctortype: Optional[DoctorType] = ''
    specialization: Optional[str] = ''
    ayoo_id_initials: str
    languages: Optional[List[str]] = []
    graduation: Optional[str] = ''
    masters: Optional[str] = ''
    additional_qualification: Optional[str] = ''
    fellowship: Optional[str] = ''
    residency: Optional[str] = ''
    experience: Optional[str] = ''
    practice_area: Optional[List[str]] = []
    interest_area: Optional[List[str]] = []
    consultation_symptoms: Optional[List[str]] = []
    awards: Optional[str] = ''
    homeaddress: Optional[str] = ''
    bio: Optional[List[str]] = []
    degree: Optional[List[dict]] = []
    is_active: Optional[bool] = True
    working_hour_starts_at: Optional[str] = '9:00 AM'
    working_hour_ends_at: Optional[str] = '6:00 PM'
    family_doctor_active: Optional[bool]
    license: Optional[str] = ''
    signature: Optional[str] = ''
    virtual_consultation_and_fees: Optional[List] = []
    clinic_consultation_and_fees: Optional[List] = []
    display_sequence:int=1


class DoctorSignupResponse(BaseModel):
    ayooid: str
    email: str
    mobile: str


class DoctorLoginView(BaseModel):
    ayooid: str
    password: str

    def __str__(self):
        return self.ayooid


class DoctorLoginToken(BaseModel):
    ayooid: str
    doctorid: str
    email: str
    access_token: str
    token_type: str


class BankDetails(BaseModel):
    accid: str
    acc: str
    accholder: str
    ifsc: str
    branch: Optional[str]


class DoctorVerifyOtpView(BaseModel):
    firstname: str
    lastname: str
    dob: str
    email: str
    mobile: str
    password: str
    gender: Gender
    ayooid: str


class DoctorChangePasswordView(BaseModel):
    current_password: str
    new_password: str


class DoctorAndClinic(BaseModel):
    mappingid: str
    sunday: Optional[List[dict]] = []
    monday: Optional[List[dict]] = []
    tuesday: Optional[List[dict]] = []
    wednesday: Optional[List[dict]] = []
    thursday: Optional[List[dict]] = []
    friday: Optional[List[dict]] = []
    saturday: Optional[List[dict]] = []


class DoctorAndClinicMapping(BaseModel):
    clinicid: str
    doctorid: Optional[str]
    sunday: Optional[List[dict]] = []
    monday: Optional[List[dict]] = []
    tuesday: Optional[List[dict]] = []
    wednesday: Optional[List[dict]] = []
    thursday: Optional[List[dict]] = []
    friday: Optional[List[dict]] = []
    saturday: Optional[List[dict]] = []


class VirtualAppointment(BaseModel):
    sunday: Optional[List[dict]] = []
    monday: Optional[List[dict]] = []
    tuesday: Optional[List[dict]] = []
    wednesday: Optional[List[dict]] = []
    thursday: Optional[List[dict]] = []
    friday: Optional[List[dict]] = []
    saturday: Optional[List[dict]] = []


class VirtualAppointmentBooking(BaseModel):
    doctorid: Optional[str]
    sunday: Optional[List[dict]] = []
    monday: Optional[List[dict]] = []
    tuesday: Optional[List[dict]] = []
    wednesday: Optional[List[dict]] = []
    thursday: Optional[List[dict]] = []
    friday: Optional[List[dict]] = []
    saturday: Optional[List[dict]] = []


class RequestAppointmentList(BaseModel):
    starts_from: str
    till: Optional[str]
    patient_id: Optional[str] = None


class ResponseAppointmentList(BaseModel):
    appointment_id: str
    caseid: str
    symptoms: List[str] = []
    symptoms_audio_clip: Optional[str]
    appointment_slot: datetime
    slot_duration: Optional[int] = 0
    end_date: datetime
    appointment_type: str
    appointment_for: str
    patient: Optional[dict] = {}
    clinic: Optional[dict] = {}
    doctor: Optional[dict] = {}
    additional_notes: Optional[str]
    meeting_link: Optional[str] = None
    meeting_code: Optional[str] = None
    doctor_specialization: Optional[str] = None
    doctor_specialization_field: Optional[str] = None


class RequestDoctorsVirtualAvailableSlots(BaseModel):
    specialization: Optional[str]
    search_date: str
    availability_type: Optional[str] = 'Virtual'
    clinic_id: Optional[str] = ''
    user_id: Optional[str] = ''


class ResponseDoctorsVirtualAvailableSlots(BaseModel):
    doctorid: str
    firstname: str
    lastname: str
    gender: str
    bio: Optional[List[str]] = []
    graduation: str
    masters: str
    doctortype: str
    specialization: str
    specialization_field: str = 'Medical Health'
    languages: Optional[List]
    practice_areas: Optional[List[str]] = []
    interest_area: Optional[List[str]] = []
    consultation_symptoms: Optional[List[str]] = []
    availability_slotid: Optional[str] = None
    doctor_profile_picture: Optional[dict] = None
    consultation_durations: Optional[List] = []
    slots: Optional[dict] = {}


class UpdateDoctorSignUpView(BaseModel):
    doctorid: str
    firstname: Optional[str]
    lastname: Optional[str]
    dob: Optional[str]
    email: Optional[str]
    mobile: Optional[str]
    password: Optional[str]
    gender: Optional[Gender]
    ayooid: Optional[str]
    consulting_duration_virtual: Optional[int]
    consulting_duration_clinic: Optional[int]
    consulting_fees_virtual: Optional[int]
    consulting_fees_clinic: Optional[int]

    profilename: Optional[str]
    doctortype: Optional[DoctorType]
    specialization: Optional[str]
    languages: Optional[List[str]]
    graduation: Optional[str]
    masters: Optional[str]
    additional_qualification: Optional[str]
    fellowship: Optional[str]
    residency: Optional[str]
    experience: Optional[str]
    practice_area: Optional[List[str]] = []
    interest_area: Optional[List[str]] = []
    consultation_symptoms: Optional[List[str]] = []
    awards: Optional[str]
    homeaddress: Optional[str]
    bio: Optional[List[str]] = []
    degree: Optional[List[dict]] = []
    working_hour_starts_at: Optional[str]
    working_hour_ends_at: Optional[str]
    license: Optional[str]
    signature: Optional[str]
    is_active: Optional[bool] = True

    virtual_consultation_and_fees: Optional[List[DoctorConsultationDetails]] = []
    clinic_consultation_and_fees: Optional[List[DoctorConsultationDetails]] = []

    display_sequence:int=1


class GetDoctorById(BaseModel):
    doctorid: str


class DeleteDoctorResponseView(BaseModel):
    doctorid: str
    firstname: str
    status: str


class UpdateDoctorAndClinicMapping(BaseModel):
    mappingid: str
    sunday: Optional[List[dict]] = []
    monday: Optional[List[dict]] = []
    tuesday: Optional[List[dict]] = []
    wednesday: Optional[List[dict]] = []
    thursday: Optional[List[dict]] = []
    friday: Optional[List[dict]] = []
    saturday: Optional[List[dict]] = []


class UploadDoctorProfileImages(BaseModel):
    profile_image_encoded: str


class UploadDoctorSignatureImages(BaseModel):
    signature_image_encoded: str
    doctor_id: str


class DoctorProfileImageView(BaseModel):
    # doctorid: str
    image_id: str
    profile_image_url: str
    # update_date: datetime


class SearchDoctorsBasedOnSpecialization(BaseModel):
    specialization: Optional[str] = ''

    # class Config:
    #     use_enum_values = True


class UploadDoctorProfileImagesByAdmin(BaseModel):
    doctorid: str
    profile_image_encoded: str


class VirtualAppointmentCopy(BaseModel):
    doctorid: str
    sunday: Optional[List[dict]] = []
    monday: Optional[List[dict]] = []
    tuesday: Optional[List[dict]] = []
    wednesday: Optional[List[dict]] = []
    thursday: Optional[List[dict]] = []
    friday: Optional[List[dict]] = []
    saturday: Optional[List[dict]] = []
    # availability_type: AvailabilityType
    #
    # class Config:
    #     use_enum_values = True
    #


class RequestAvailableSlots(BaseModel):
    starts_from: str
    till: Optional[str]


class ExtendEndDateOfCaseId(BaseModel):
    case_id: str
    extended_date: str


class CloseActiveCaseId(BaseModel):
    case_id: str


class ResponseCaseIdChange(BaseModel):
    case_id: str
    status: str


class RequestDoctorsVirtualSlots(BaseModel):
    doctorid: str


class VirtualSlotsAdminResponse(BaseModel):
    doctorid: Optional[str]
    sunday: Optional[List[dict]] = []
    monday: Optional[List[dict]] = []
    tuesday: Optional[List[dict]] = []
    wednesday: Optional[List[dict]] = []
    thursday: Optional[List[dict]] = []
    friday: Optional[List[dict]] = []
    saturday: Optional[List[dict]] = []


class FamilyDoctorStatus(BaseModel):
    family_doctor_active: bool


class FamilyDoctorStatusAdmin(BaseModel):
    doctorid: str
    family_doctor_active: bool


class PatientDirectoryResponse(BaseModel):
    doctor_id: str
    active_cases_list: Optional[List]
    family_doctor_list: Optional[List]


class SearchByAreaOrSpecialization(BaseModel):
    search_word: str


class SearchPatientsByName(BaseModel):
    doctor_id: Optional[str]
    patient_name: str


class SearchSlotsOnADate(BaseModel):
    search_date: str
    doctor_id: Optional[str]


class ActivateDoctorAccount(BaseModel):
    doctor_id: str
