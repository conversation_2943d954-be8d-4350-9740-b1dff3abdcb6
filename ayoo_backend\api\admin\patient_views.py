from requests.models import HTTPError
from fastapi import <PERSON>Router, Depends, HTTPException, Query
from ..services.auth import get_admin_from_token
from ..DAOs.allPatientsDAO import AllPatientsDAO, PIIQuery, PatientExists, GetPatientByUserID
from ..DTOs.userDTO import AdminPIID<PERSON>, AdminPatientExistsDTO
from ..services.aws_email_and_message_sender import AWSEmailAndMsgSender, NotSentException
from ..services.aws_email_model import ResendRegistrationEmail
from ..text_local_service.text_local_controller import TextLocalController
from ..api_configs import WEB_URL_PATH
from pyshorteners import Shortener

patients_router = APIRouter(prefix= "/patients", dependencies=[Depends(get_admin_from_token)], tags = ["admin", "patients", "users", "relatives"])


@patients_router.post("/pii")
async def get_pii(pii_query: PIIQuery):
    all_profiles = AllPatientsDAO().list(filters = pii_query)
    return AdminPIIDTO.create_from_profile_list(all_profiles, pii_query.email, pii_query.mobile)

@patients_router.post("/exists")
async def patient_exists(check_patient_query: PatientExists):
    all_patients = AllPatientsDAO().list(filters = check_patient_query)
    return AdminPatientExistsDTO.create_from_patient_list(all_patients)

@patients_router.get("/resend_registration_link")
async def resend_registration_link(userid_query: GetPatientByUserID = Depends()):
    patient_list = AllPatientsDAO().list(filters = userid_query)
    if len(patient_list) == 0:
        raise HTTPException(400, "No such user exists !!!!")
    if len(patient_list) > 1:
        raise HTTPException(500, "Something went wrong in profile identification !!!!")
    patient = patient_list[0]
    if patient.get("source") != "relative":
        raise HTTPException(400, "User already registered !!!!")
    url = "{WEB_URL_PATH}memberSignup?relativeId={userid}&firstname={firstname}&lastname={lastname}&email={email}&mobile={mobile}&birthdate={birthdate}&gender={gender}".format(WEB_URL_PATH = WEB_URL_PATH, **patient)
    shortened_url = Shortener().tinyurl.short(url)
    email = ResendRegistrationEmail.create_from_details(to = patient.get("email"), url = shortened_url)
    print(email.convert_to_aws_format())
    try:
        email_message_id = AWSEmailAndMsgSender().send_email(**email.convert_to_aws_format())
    except Exception as e:
        email_message_id =  str(e)
    try:
        sms_message_id = TextLocalController().send_sms(template_name='WelcomeMessage', var_list=[shortened_url], numbers=patient.get("mobile"))
    except Exception as e:
        sms_message_id = str(e)
    return {"email": email_message_id, "sms": sms_message_id}
