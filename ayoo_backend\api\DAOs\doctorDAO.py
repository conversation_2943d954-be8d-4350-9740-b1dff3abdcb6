from fastapi import Query
from .baseDAO import BaseMongoDAO, BaseMongoSort, BaseMongoQueryFields, BaseSQLDAO
from ..DataModels.doctor import Doctor
from ..dbmodels import DBDoctor
from .specializationDAO import SpecializationDAO, GetAreasForSpecialization


class DoctorQueryFields(BaseMongoQueryFields):
    specialization: str = None

    def apply_filters(self, query_object, dao, *args, **kwargs):
        if self.specialization is None:
            return {}
        all_areas = SpecializationDAO().list(0, 1000, GetAreasForSpecialization(specialization_field = self.specialization))
        all_areas.extend(SpecializationDAO().list(0, 1000, GetAreasForSpecialization(specialization = self.specialization)))
        all_areas = list(set([x.specialization for x in all_areas]))
        all_areas.append(self.specialization)
        return {
                "$or": [
                    {"specialization": {"$in": all_areas}},
                    {"practice_area": {"$in": all_areas}}
                ],
                "is_active": True
        }
        
class GetDoctor(BaseMongoQueryFields):
    doctorid: str

class DoctorSort(BaseMongoSort):
    pass

class DoctorDAO(BaseMongoDAO):

    collection = "DoctorsInfo"
    _model_class = Doctor


class DoctorProfileDAO(BaseSQLDAO):

    _model_class = DBDoctor
    pk = "doctorid"
